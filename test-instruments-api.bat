@echo off
echo Testing Investment Toolkit Instruments API
echo.

echo 1. Testing GET /api/instruments (paginated)
curl -X GET "http://localhost:8080/investment-toolkit/api/instruments?page=0&size=5&sortBy=marketCap&sortDir=desc" -H "Content-Type: application/json"
echo.
echo.

echo 2. Testing GET /api/instruments/statistics
curl -X GET "http://localhost:8080/investment-toolkit/api/instruments/statistics" -H "Content-Type: application/json"
echo.
echo.

echo 3. Testing GET /api/instruments/search
curl -X GET "http://localhost:8080/investment-toolkit/api/instruments/search?q=AAPL" -H "Content-Type: application/json"
echo.
echo.

echo API tests completed.
pause
