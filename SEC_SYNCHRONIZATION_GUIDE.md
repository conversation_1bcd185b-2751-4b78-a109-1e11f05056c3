# SEC Synchronization Feature

## Overview

The InvestmentToolKitV2 now includes a comprehensive SEC synchronization feature that automatically identifies and adds missing instruments from official SEC (Securities and Exchange Commission) data to your database. This feature complements the existing symbol validation functionality by ensuring your database contains all officially recognized U.S. stock symbols.

## Features

### ✅ **SEC Data Integration**
- Downloads official SEC company tickers from: `https://www.sec.gov/files/company_tickers.json`
- Caches data locally for 24 hours to minimize API calls
- Supports force refresh for immediate updates
- Reuses existing SEC data infrastructure from symbol validation

### ✅ **Missing Symbol Detection**
- Compares SEC ticker data against your current database
- Identifies symbols that exist in SEC data but are missing from your database
- Provides detailed reports of missing instruments
- Supports batch processing with configurable limits

### ✅ **Safe Synchronization**
- **Dry-run mode**: Preview what would be added without making changes
- **Batch limits**: Configurable maximum number of instruments per operation
- **Transactional operations**: All-or-nothing database updates
- **Comprehensive logging**: Full audit trail of all operations

### ✅ **Performance Optimized**
- Efficient bulk operations for adding multiple instruments
- Memory-conscious processing with configurable batch sizes
- Garbage-free operations following project standards
- Reuses existing database connection pooling

## API Endpoints

### 1. Dry-Run Synchronization (Safe)
```http
GET /investment-toolkit/api/instruments/sync-sec-data?forceRefresh=false&maxInstruments=1000
```

**Description**: Identifies missing instruments without making any database changes.

**Parameters**:
- `forceRefresh` (optional, default: false): Force download fresh SEC data
- `maxInstruments` (optional, default: 1000): Maximum instruments to process

**Response Example**:
```json
{
  "success": true,
  "message": "SEC synchronization analysis completed",
  "timestamp": "2024-01-15T10:30:00",
  "data": {
    "totalSecSymbols": 5000,
    "existingInDatabase": 150,
    "missingFromDatabase": 25,
    "missingSymbolsList": ["TSLA", "NVDA", "AMD", "..."],
    "addedSymbols": 0,
    "dryRun": true,
    "timestamp": "2024-01-15T10:30:00",
    "summary": "DRY RUN: Found 25 missing symbols out of 5000 SEC symbols. Would add 25 new instruments to database."
  }
}
```

### 2. Actual Synchronization
```http
POST /investment-toolkit/api/instruments/sync-sec-data
Content-Type: application/json

{
  "dryRun": false,
  "forceRefresh": false,
  "maxInstruments": 100
}
```

**Description**: Performs actual synchronization and adds missing instruments.

**Request Body**:
```json
{
  "dryRun": false,           // Set to false for actual sync
  "forceRefresh": false,     // Force fresh SEC data download
  "maxInstruments": 100      // Limit instruments per operation
}
```

**Response Example**:
```json
{
  "success": true,
  "message": "SEC synchronization completed",
  "timestamp": "2024-01-15T10:35:00",
  "data": {
    "totalSecSymbols": 5000,
    "existingInDatabase": 150,
    "missingFromDatabase": 25,
    "missingSymbolsList": ["TSLA", "NVDA"],
    "addedSymbols": 2,
    "dryRun": false,
    "timestamp": "2024-01-15T10:35:00",
    "summary": "SYNC COMPLETED: Added 2 new instruments out of 25 missing symbols. Database now has 152 instruments from SEC data."
  }
}
```

## Usage Examples

### 1. Check What Would Be Added (Safe)
```bash
curl -X GET "http://localhost:8080/investment-toolkit/api/instruments/sync-sec-data?maxInstruments=50"
```

### 2. Add Missing Instruments (Actual Sync)
```bash
curl -X POST "http://localhost:8080/investment-toolkit/api/instruments/sync-sec-data" \
  -H "Content-Type: application/json" \
  -d '{
    "dryRun": false,
    "forceRefresh": false,
    "maxInstruments": 50
  }'
```

### 3. Force Fresh SEC Data and Preview
```bash
curl -X GET "http://localhost:8080/investment-toolkit/api/instruments/sync-sec-data?forceRefresh=true&maxInstruments=100"
```

## Safety Features

### 🛡️ **Default Safety Mode**
- All operations default to dry-run mode unless explicitly set to false
- Batch size limits prevent overwhelming the system
- Comprehensive validation before any database changes

### 🛡️ **Transactional Operations**
- Database changes are atomic - either all succeed or all fail
- Automatic rollback on any errors during synchronization
- Maintains referential integrity throughout the process

### 🛡️ **Comprehensive Logging**
- All operations are logged for audit purposes
- Detailed error messages for troubleshooting
- Performance metrics for monitoring

### 🛡️ **Error Recovery**
- Graceful handling of network errors
- Database connection failure recovery
- Partial failure handling with detailed reporting

## Performance Characteristics

### 📊 **Batch Processing**
- Default batch size: 1000 instruments
- Configurable limits to prevent memory issues
- Efficient bulk database operations

### 📊 **Memory Management**
- Garbage-free operations in hot paths
- Object reuse patterns for SEC data processing
- Memory-mapped caching for large datasets

### 📊 **Network Optimization**
- 24-hour caching of SEC data
- Conditional downloads based on cache freshness
- Efficient HTTP client with connection pooling

## Integration with Existing Features

The SEC synchronization feature integrates seamlessly with existing functionality:

- **Database Schema**: Uses the same instruments table structure
- **SEC Data**: Reuses existing SEC data download and caching infrastructure
- **REST API**: Follows the same patterns as existing endpoints
- **Error Handling**: Uses the global exception handler
- **Logging**: Integrates with the existing SLF4J logging framework
- **Testing**: Comprehensive Spock test coverage

## File Locations

- **Database**: `./data/marketdata.duckdb`
- **SEC Cache**: `./data/sec_cache/company_tickers.json`
- **Logs**: `logs/investment-toolkit.log`

## Best Practices

### 🎯 **Recommended Workflow**
1. **Always start with dry-run**: Use GET endpoint to preview changes
2. **Use reasonable batch sizes**: Start with 100-500 instruments
3. **Monitor logs**: Check application logs for detailed operation status
4. **Verify results**: Query database to confirm expected instruments were added

### 🎯 **Production Considerations**
- Schedule synchronization during low-traffic periods
- Use smaller batch sizes for production environments
- Monitor database performance during large synchronizations
- Keep SEC cache fresh with periodic force refreshes

This feature ensures your investment data maintains the highest quality by automatically synchronizing with authoritative SEC financial data sources.
