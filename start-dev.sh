#!/bin/bash

echo "Starting Investment Toolkit Development Environment"
echo

echo "Starting Backend (Spring Boot)..."
gnome-terminal --title="Backend" -- bash -c "./gradlew bootRun; exec bash" &

echo "Waiting for backend to start..."
sleep 10

echo "Starting Frontend (React)..."
gnome-terminal --title="Frontend" -- bash -c "cd frontend && npm start; exec bash" &

echo
echo "Development environment starting..."
echo "Backend: http://localhost:8080/investment-toolkit"
echo "Frontend: http://localhost:3000"
echo "API Docs: http://localhost:8080/investment-toolkit/swagger-ui.html"
echo
echo "Press Ctrl+C to exit..."

# Keep script running
while true; do
    sleep 1
done
