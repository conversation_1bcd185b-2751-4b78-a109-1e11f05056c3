# Enhanced Market Data Downloader Guide

## Overview

The MarketDataDownloaderApp has been significantly enhanced to provide **automatic symbol discovery** and **comprehensive batch processing** capabilities. Instead of maintaining a hardcoded list of symbols, the application now automatically discovers all instruments from your database and downloads historical OHLCV data for each one.

## Key Enhancements

### ✅ **Automatic Symbol Discovery**
- Automatically retrieves all instruments from the `instruments` table
- No more hardcoded symbol lists to maintain
- Processes all instruments currently in your database
- Integrates seamlessly with the SEC synchronization feature

### ✅ **Sequential Processing with Error Handling**
- Processes each instrument one by one to avoid overwhelming data providers
- Continues processing even if individual symbols fail
- Comprehensive retry logic with configurable attempts
- Detailed progress tracking and logging

### ✅ **Intelligent Date Range Management**
- Automatically determines optimal start dates for each instrument
- Skips downloading data that already exists (configurable)
- Handles gaps in historical data intelligently
- Supports custom date ranges via command line

### ✅ **Comprehensive Configuration**
- Command-line arguments for flexible operation
- Pre-configured modes for common use cases
- Configurable retry logic and error handling
- Rate limiting to be respectful to data providers

## Usage Examples

### 1. Basic Usage (Recommended)
```bash
java -jar build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar
```
**What it does:**
- Discovers all instruments from database
- Downloads missing historical data for each instrument
- Uses intelligent date range detection
- Continues on errors, provides comprehensive summary

### 2. Download Recent Data Only
```bash
java -jar build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar --recent-only
```
**What it does:**
- Downloads only the last 30 days of data
- Perfect for daily/weekly updates
- Fast execution for maintenance operations

### 3. Download Complete Historical Data
```bash
java -jar build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar --full-history
```
**What it does:**
- Downloads complete historical data from 1962
- Re-downloads all data (doesn't skip existing)
- Use for initial setup or complete refresh

### 4. Custom Date Range
```bash
java -jar build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar --start-date 2020-01-01 --end-date 2023-12-31
```
**What it does:**
- Downloads data for specific date range
- Useful for backtesting or analysis of specific periods

### 5. Strict Error Handling
```bash
java -jar build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar --stop-on-error --max-retries 5
```
**What it does:**
- Stops processing if any symbol fails
- Increases retry attempts to 5 per symbol
- Use when data integrity is critical

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--start-date YYYY-MM-DD` | Start date for historical data | Calculated based on existing data |
| `--end-date YYYY-MM-DD` | End date for historical data | Today |
| `--full-history` | Download complete historical data from 1962 | false |
| `--recent-only` | Download only recent data (last 30 days) | false |
| `--no-skip-existing` | Re-download all data, don't skip existing | false (skip existing) |
| `--stop-on-error` | Stop processing if any symbol fails | false (continue on error) |
| `--max-retries N` | Maximum retries per symbol | 3 |
| `--help` | Show help message | - |

## Configuration Details

### MarketDataDownloadConfig Class

The application uses a comprehensive configuration system:

<augment_code_snippet path="src/main/java/com/investment/config/MarketDataDownloadConfig.java" mode="EXCERPT">
```java
public class MarketDataDownloadConfig {
    private LocalDate startDate;
    private LocalDate endDate;
    private boolean continueOnError = true;
    private int maxRetries = 3;
    private long retryDelayMs = 1000;
    private boolean skipExistingData = true;
```
</augment_code_snippet>

### Intelligent Date Range Detection

The application automatically determines the best start date for each instrument:

1. **No existing data**: Uses configuration start date or defaults to historical data start
2. **Existing data + skip mode**: Starts from 7 days before last data point (handles gaps)
3. **Custom date range**: Uses specified dates regardless of existing data

## Integration with Existing Features

### 🔗 **SEC Synchronization Integration**
- Works seamlessly with the SEC synchronization feature
- First use SEC sync to populate your instruments database
- Then use market data downloader to fetch historical data
- Perfect workflow for comprehensive data management

### 🔗 **Database Integration**
- Uses existing DatabaseManager.getAllInstruments() method
- Leverages existing OHLCV data storage infrastructure
- Maintains data integrity with existing validation

### 🔗 **Yahoo Finance Provider**
- Continues to use the robust YahooFinanceProvider
- Inherits all existing error handling and data parsing
- Benefits from proven data download mechanisms

## Performance Characteristics

### 📊 **Rate Limiting**
- 500ms delay between symbol downloads
- Respectful to Yahoo Finance API limits
- Configurable retry delays (default: 1 second)

### 📊 **Memory Management**
- Processes symbols sequentially to minimize memory usage
- Garbage-free operations in hot paths
- Efficient database connection reuse

### 📊 **Error Recovery**
- Individual symbol failures don't affect others
- Comprehensive retry logic with exponential backoff
- Detailed error reporting for troubleshooting

## Logging and Monitoring

### 📋 **Progress Tracking**
```
Processing instrument 1/150: AAPL (Apple Inc.)
✓ Successfully processed AAPL
Processing instrument 2/150: GOOGL (Alphabet Inc.)
✓ Successfully processed GOOGL
...
Market data download completed:
  ✓ Successful: 145
  ⊘ Skipped: 3
  ✗ Errors: 2
  📊 Total processed: 150/150
```

### 📋 **Error Reporting**
- Detailed error messages for each failed symbol
- Retry attempt logging
- Final summary with success/failure counts
- Integration with existing SLF4J logging framework

## Best Practices

### 🎯 **Recommended Workflow**

1. **Initial Setup**:
   ```bash
   # First, populate instruments using SEC sync
   curl -X POST "http://localhost:8080/investment-toolkit/api/instruments/sync-sec-data" \
     -H "Content-Type: application/json" \
     -d '{"dryRun": false, "maxInstruments": 100}'
   
   # Then download historical data
   java -jar build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar --full-history
   ```

2. **Daily Updates**:
   ```bash
   java -jar build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar --recent-only
   ```

3. **Weekly Maintenance**:
   ```bash
   java -jar build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar
   ```

### 🎯 **Production Considerations**
- Schedule downloads during off-peak hours
- Monitor logs for consistent failures
- Use `--recent-only` for frequent updates
- Consider `--stop-on-error` for critical operations

## File Locations

- **Application JAR**: `build/libs/InvestmentTookKitV2-1.0-SNAPSHOT.jar`
- **Database**: `./data/marketdata.duckdb`
- **Logs**: `logs/investment-toolkit.log`
- **Configuration**: Command-line arguments (no config files needed)

## Error Handling

The application provides robust error handling:

- **Network errors**: Automatic retry with exponential backoff
- **Data parsing errors**: Detailed logging and continuation
- **Database errors**: Transaction rollback and error reporting
- **Invalid symbols**: Graceful handling and logging

This enhanced market data downloader ensures your investment database maintains comprehensive, up-to-date historical data with minimal manual intervention.
