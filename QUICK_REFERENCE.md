# Process Management System - Quick Reference

## 🚨 Emergency Operations

### Abort All Running Processes
```bash
curl -X POST http://localhost:8080/investment-toolkit/api/processes/abort-all
```

### Check System Status
```bash
curl http://localhost:8080/investment-toolkit/api/processes/statistics
```

## 📊 Monitoring Operations

### List All Active Processes
```bash
curl http://localhost:8080/investment-toolkit/api/processes/active
```

### Get Specific Process Details
```bash
curl http://localhost:8080/investment-toolkit/api/processes/{processId}
```

### Filter Processes by Status
```bash
# Running processes only
curl "http://localhost:8080/investment-toolkit/api/processes?status=RUNNING"

# Completed processes
curl "http://localhost:8080/investment-toolkit/api/processes?status=COMPLETED"

# Failed processes
curl "http://localhost:8080/investment-toolkit/api/processes?status=FAILED"
```

### Filter Processes by Type
```bash
# DMI calculations
curl "http://localhost:8080/investment-toolkit/api/processes?type=DMI_CALCULATION"

# OHLCV refresh operations
curl "http://localhost:8080/investment-toolkit/api/processes?type=OHLCV_REFRESH"
```

## 🎯 Process Control

### Abort Specific Process
```bash
curl -X POST http://localhost:8080/investment-toolkit/api/processes/{processId}/abort
```

### Start Async DMI Calculation
```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/dmi/calculate-async \
  -H "Content-Type: application/json" \
  -d '{
    "period": 14,
    "dryRun": false,
    "maxSymbols": 1000,
    "calculationMode": "INCREMENTAL"
  }' \
  --user admin
```

## 🧹 Maintenance Operations

### Clean Up Completed Processes
```bash
curl -X POST http://localhost:8080/investment-toolkit/api/processes/cleanup
```

## 📈 Example Workflow

### 1. Start Long-Running Operation
```bash
# Start async DMI calculation
RESPONSE=$(curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/dmi/calculate-async \
  -H "Content-Type: application/json" \
  -d '{"period": 14, "dryRun": false}' \
  --user admin)

# Extract process ID
PROCESS_ID=$(echo $RESPONSE | jq -r '.data.processId')
echo "Started process: $PROCESS_ID"
```

### 2. Monitor Progress
```bash
# Check progress periodically
while true; do
  STATUS=$(curl -s http://localhost:8080/investment-toolkit/api/processes/$PROCESS_ID | jq -r '.data.status')
  PROGRESS=$(curl -s http://localhost:8080/investment-toolkit/api/processes/$PROCESS_ID | jq -r '.data.progressPercentage')
  
  echo "Process $PROCESS_ID: $STATUS ($PROGRESS%)"
  
  if [[ "$STATUS" == "COMPLETED" || "$STATUS" == "FAILED" || "$STATUS" == "ABORTED" ]]; then
    break
  fi
  
  sleep 5
done
```

### 3. Emergency Stop (if needed)
```bash
# Abort specific process
curl -X POST http://localhost:8080/investment-toolkit/api/processes/$PROCESS_ID/abort

# Or abort all processes
curl -X POST http://localhost:8080/investment-toolkit/api/processes/abort-all
```

## 🔍 Process Status Values

- **RUNNING**: Process is actively executing
- **COMPLETED**: Process finished successfully  
- **FAILED**: Process terminated due to error
- **ABORTED**: Process was cancelled by user/system
- **ABORTING**: Process is in the process of being cancelled

## 📋 Process Types

- **DMI_CALCULATION**: Directional Movement Index calculations
- **BOLLINGER_BANDS_CALCULATION**: Bollinger Bands technical indicators
- **OHLCV_REFRESH**: Market data refresh operations
- **SEC_SYNCHRONIZATION**: SEC data synchronization
- **CSV_INSTRUMENT_UPLOAD**: CSV file processing
- **SYMBOL_VALIDATION**: Symbol validation operations
- **BULK_DATA_PROCESSING**: Generic bulk operations

## 🎛️ Response Format

All endpoints return responses in this format:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ },
  "timestamp": "2024-12-15T14:30:00"
}
```

## ⚡ Performance Tips

1. **Use async endpoints** for operations that may take more than a few seconds
2. **Monitor progress** regularly to detect stuck processes
3. **Clean up completed processes** periodically to free memory
4. **Use filtering** to reduce API response sizes
5. **Abort processes** before system maintenance

## 🛡️ Safety Features

- **Graceful termination**: Processes clean up resources when aborted
- **Thread safety**: All operations are thread-safe
- **Resource protection**: Automatic cleanup prevents memory leaks
- **Error handling**: Comprehensive error reporting and recovery
- **State consistency**: Process states are always consistent

## 📞 Support

For issues or questions about the process management system:
1. Check process logs for detailed error information
2. Use the statistics endpoint to understand system load
3. Monitor active processes to identify bottlenecks
4. Use abort operations for emergency situations
