# Investment Toolkit - Frontend/Backend Architecture

A comprehensive financial analysis platform split into separate frontend and backend applications for better scalability and maintainability.

## Architecture Overview

### Backend (Spring Boot API Server)
- **Technology**: Java 21, Spring Boot 3.2.3, DuckDB
- **Port**: 8080
- **Context Path**: `/investment-toolkit`
- **API Base**: `http://localhost:8080/investment-toolkit/api`

### Frontend (React Application)
- **Technology**: React 18, TypeScript, Material-UI
- **Port**: 3000 (development)
- **URL**: `http://localhost:3000`

## Quick Start

### 1. Start the Backend
```bash
# From project root
./gradlew bootRun
```
Backend will be available at: `http://localhost:8080/investment-toolkit`

### 2. Start the Frontend
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies (first time only)
npm install

# Start development server
npm start
```
Frontend will be available at: `http://localhost:3000`

## Project Structure

```
InvestmentTookKitV2/
├── src/main/java/com/investment/          # Backend Spring Boot application
│   ├── api/controller/                    # REST API controllers
│   ├── config/                           # Configuration classes (including CORS)
│   ├── service/                          # Business logic services
│   ├── model/                            # Domain models
│   └── database/                         # Database management
├── frontend/                             # React frontend application
│   ├── src/
│   │   ├── components/                   # Reusable React components
│   │   ├── pages/                        # Page components
│   │   ├── services/api/                 # API service layer
│   │   └── types/                        # TypeScript interfaces
│   ├── public/                           # Static assets
│   └── package.json                      # Frontend dependencies
├── data/                                 # DuckDB database files
├── logs/                                 # Application logs
├── build.gradle                          # Backend build configuration
└── README.md                            # This file
```

## Features

### Backend API Endpoints
- **Instruments** (`/api/instruments`) - Financial instrument management
- **OHLCV** (`/api/ohlcv`) - Price data operations
- **Technical Indicators** (`/api/technical-indicators`) - Bollinger Bands, DMI
- **Positions** (`/api/positions`) - Portfolio management
- **Watch List** (`/api/watchlist`) - Symbol tracking
- **Processes** (`/api/processes`) - Background operation monitoring

### Frontend Pages
- **Dashboard** - System overview and statistics
- **Instruments** - Search and manage financial instruments
- **OHLCV Data** - Interactive price charts with technical indicators
- **Technical Indicators** - Calculate Bollinger Bands and DMI
- **Positions** - Portfolio position management
- **Watch List** - Symbol tracking and alerts
- **Processes** - Real-time process monitoring

## Configuration

### Backend CORS Configuration
The backend is configured to accept requests from the React frontend:

```java
// src/main/java/com/investment/config/AppConfig.java
@Bean
public WebMvcConfigurer corsConfigurer() {
    return new WebMvcConfigurer() {
        @Override
        public void addCorsMappings(CorsRegistry registry) {
            registry.addMapping("/api/**")
                    .allowedOrigins("http://localhost:3000", "http://127.0.0.1:3000")
                    .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
                    .allowedHeaders("*")
                    .allowCredentials(true);
        }
    };
}
```

### Frontend API Configuration
The frontend is configured to communicate with the backend:

```typescript
// frontend/src/services/api/apiClient.ts
const apiClient = axios.create({
  baseURL: 'http://localhost:8080/investment-toolkit/api',
  withCredentials: true,
});
```

## Development Workflow

### Backend Development
1. Make changes to Java code in `src/main/java/`
2. Run tests: `./gradlew test`
3. Start application: `./gradlew bootRun`
4. API documentation: `http://localhost:8080/investment-toolkit/swagger-ui.html`

### Frontend Development
1. Navigate to `frontend/` directory
2. Make changes to React components
3. Hot reload automatically updates the browser
4. Build for production: `npm run build`

### Full Stack Development
1. Start backend: `./gradlew bootRun`
2. In another terminal, start frontend: `cd frontend && npm start`
3. Access application at `http://localhost:3000`
4. API calls will be proxied to the backend automatically

## API Documentation

### Swagger/OpenAPI
- **URL**: `http://localhost:8080/investment-toolkit/swagger-ui.html`
- **API Docs**: `http://localhost:8080/investment-toolkit/api-docs`

### Key Endpoints
- `GET /api/instruments` - List financial instruments
- `GET /api/ohlcv/{symbol}` - Get OHLCV data for symbol
- `POST /api/technical-indicators/bollinger-bands/calculate` - Calculate Bollinger Bands
- `POST /api/technical-indicators/dmi/calculate` - Calculate DMI indicators
- `GET /api/processes` - List background processes

## Database

### DuckDB Configuration
- **File**: `./data/marketdata.duckdb`
- **Connection**: Configured in `application.properties`
- **Migrations**: Handled by `DatabaseManager.java`

### Key Tables
- `instruments` - Financial instrument metadata
- `ohlcv` - Price and volume data with technical indicators
- `positions` - Portfolio positions
- `watch_list` - Tracked symbols

## Testing

### Backend Tests
```bash
./gradlew test
```

### Frontend Tests
```bash
cd frontend
npm test
```

## Production Deployment

### Backend
```bash
./gradlew build
java -jar build/libs/investment-toolkit-*.jar
```

### Frontend
```bash
cd frontend
npm run build
# Serve the build/ directory with a web server
```

### Docker (Future Enhancement)
Consider containerizing both applications for easier deployment.

## Performance Considerations

### Backend
- **Low-latency optimizations**: Primitive collections, garbage-free patterns
- **Database**: DuckDB for analytical workloads
- **Async processing**: Background operations for heavy calculations

### Frontend
- **Code splitting**: Lazy load routes
- **Caching**: API response caching
- **Optimization**: React.memo, useCallback, useMemo

## Security

### Current State
- No authentication implemented
- CORS configured for development
- Input validation on API endpoints

### Future Enhancements
- JWT authentication
- Role-based access control
- API rate limiting
- HTTPS in production

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure backend CORS configuration includes frontend URL
   - Check that backend is running on port 8080

2. **API Connection Failed**
   - Verify backend is running: `http://localhost:8080/investment-toolkit/swagger-ui.html`
   - Check frontend API base URL configuration

3. **Database Issues**
   - Ensure `./data/` directory exists
   - Check DuckDB file permissions

4. **Build Failures**
   - Backend: `./gradlew clean build`
   - Frontend: `rm -rf node_modules && npm install`

## Contributing

1. Follow existing code patterns
2. Add tests for new features
3. Update documentation
4. Test both frontend and backend integration

## License

[Add your license information here]
