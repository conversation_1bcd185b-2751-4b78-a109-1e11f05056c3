@echo off
echo Testing OHLCV Update API Endpoints
echo.

echo 1. Testing POST /api/ohlcv/update/AAPL (single symbol update)
curl -X POST "http://localhost:8080/investment-toolkit/api/ohlcv/update/AAPL" -H "Content-Type: application/json"
echo.
echo.

echo 2. Testing POST /api/ohlcv/update/MSFT (single symbol update)
curl -X POST "http://localhost:8080/investment-toolkit/api/ohlcv/update/MSFT" -H "Content-Type: application/json"
echo.
echo.

echo 3. Testing POST /api/ohlcv/update (bulk update - for comparison)
curl -X POST "http://localhost:8080/investment-toolkit/api/ohlcv/update" -H "Content-Type: application/json" -d "{\"symbols\":[\"AAPL\",\"MSFT\"]}"
echo.
echo.

echo API tests completed.
pause
