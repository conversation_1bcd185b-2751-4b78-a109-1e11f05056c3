# OHLCV Data Integrity Fix Summary

## Problem Description

The Investment Toolkit had a critical data integrity issue where calling the `POST /api/ohlcv/update/{symbol}` endpoint was unexpectedly modifying instrument metadata, specifically overriding the Market Cap field to null and potentially affecting other instrument fields like sector and industry.

## Root Cause Analysis

### Issue Location
The problem was in the OHLCV update process chain:

1. **OHLCVController.updateSymbolData()** → calls `ohlcvService.updateOHLCVData(symbol, null, null)`
2. **OHLCVService.updateOHLCVData()** → creates new `Instrument` object with default values
3. **YahooFinanceProvider.downloadHistoricalData()** → calls `dbManager.saveInstrument()`
4. **DatabaseManager.saveInstrument()** → overwrites existing instrument data

### Specific Code Issues

#### 1. OHLCVService.updateOHLCVData() (Lines 76-80)
```java
// Create instrument object with default values
Instrument instrument = new Instrument(symbol, name != null ? name : symbol,
        type != null ? type : InstrumentType.US_STOCK);
```

When called with `null` values (which happens in bulk updates), this creates:
- `name = symbol` (instead of preserving existing name)
- `type = InstrumentType.US_STOCK` (default)
- `marketCap = null` (from backward compatibility constructor)

#### 2. YahooFinanceProvider.downloadHistoricalData() (Line 27)
```java
// Save instrument to database
dbManager.saveInstrument(instrument.getSymbol(), instrument.getName(), instrument.getType().name());
```

This unconditionally saves instrument data, overwriting existing records.

#### 3. DatabaseManager.saveInstrument() → saveInstrumentWithDetails()
The `ON CONFLICT DO UPDATE` clause in the database operation overwrites all existing instrument fields with the new (often null) values.

## Solution Implemented

### 1. New DatabaseManager Method

**File**: `src/main/java/com/investment/database/DatabaseManager.java`

Added `saveInstrumentIfNotExists()` method that preserves existing instrument data:

```java
/**
 * Save instrument only if it doesn't already exist in the database.
 * This method preserves existing instrument data and only creates new records.
 * Used during OHLCV updates to avoid overwriting existing instrument metadata.
 */
public boolean saveInstrumentIfNotExists(String symbol, String name, String type) {
    // First check if the instrument already exists
    if (symbolExists(symbol)) {
        logger.debug("Instrument {} already exists, skipping save to preserve existing data", symbol);
        return false;
    }

    // Only save if it doesn't exist
    logger.info("Creating new instrument record for symbol: {}", symbol);
    saveInstrumentWithDetails(symbol, name, type, null, null, null, null, null);
    return true;
}
```

### 2. Updated YahooFinanceProvider

**File**: `src/main/java/com/investment/provider/YahooFinanceProvider.java`

Modified to use the new preservation method:

```java
// Save instrument to database only if it doesn't exist (preserves existing metadata)
boolean isNewInstrument = dbManager.saveInstrumentIfNotExists(
    instrument.getSymbol(), 
    instrument.getName(), 
    instrument.getType().name()
);

if (isNewInstrument) {
    logger.info("Created new instrument record for {}", instrument.getSymbol());
} else {
    logger.debug("Using existing instrument record for {} (preserving metadata)", instrument.getSymbol());
}
```

### 3. Enhanced Test Coverage

**File**: `src/test/groovy/com/investment/database/DatabaseManagerSpec.groovy`

Added comprehensive tests to verify the fix:

#### Test 1: Preserve Existing Data
```groovy
def "should not overwrite existing instrument when using saveInstrumentIfNotExists"() {
    given: "an existing instrument with market cap"
    databaseManager.saveInstrumentWithDetails("AAPL", "Apple Inc.", "US_STOCK", 
        new BigDecimal("3000000000000"), "United States", 1980, "Technology", "Consumer Electronics")

    when: "trying to save with different data"
    boolean wasCreated = databaseManager.saveInstrumentIfNotExists("AAPL", "Different Name", "US_STOCK")

    then: "original data is preserved"
    !wasCreated
    def instrument = getInstrumentFromDb("AAPL")
    instrument.name == "Apple Inc." // Original preserved
    instrument.market_cap == new BigDecimal("3000000000000.00") // Original preserved
}
```

#### Test 2: Create New Instruments
```groovy
def "should create new instrument when using saveInstrumentIfNotExists for non-existing symbol"() {
    when: "saving a new instrument"
    boolean wasCreated = databaseManager.saveInstrumentIfNotExists("MSFT", "Microsoft Corporation", "US_STOCK")

    then: "new instrument is created"
    wasCreated
    def instrument = getInstrumentFromDb("MSFT")
    instrument.symbol == "MSFT"
    instrument.name == "Microsoft Corporation"
}
```

## Fix Verification

### 1. Compilation Success
- ✅ Backend compiles successfully with new methods
- ✅ No syntax errors or missing dependencies
- ✅ All existing functionality preserved

### 2. Data Integrity Protection
- ✅ Existing instruments are no longer overwritten during OHLCV updates
- ✅ Market cap, sector, industry, and other metadata fields are preserved
- ✅ New instruments can still be created when needed

### 3. Logging Enhancement
- ✅ Clear logging when preserving existing instruments
- ✅ Informative logging when creating new instruments
- ✅ Debug-level logging for routine operations

## Impact on Bulk Operations

### Before Fix (Problematic Behavior)
1. User selects multiple symbols for bulk OHLCV update
2. Frontend calls `POST /api/ohlcv/update/{symbol}` for each symbol
3. Backend creates new `Instrument` objects with default/null values
4. `saveInstrument()` overwrites existing instrument data
5. **Result**: Market cap and other metadata lost

### After Fix (Correct Behavior)
1. User selects multiple symbols for bulk OHLCV update
2. Frontend calls `POST /api/ohlcv/update/{symbol}` for each symbol
3. Backend creates new `Instrument` objects with default/null values
4. `saveInstrumentIfNotExists()` checks if instrument exists
5. If exists: Skips save, preserves existing metadata
6. If new: Creates new instrument record
7. **Result**: Existing metadata preserved, OHLCV data updated correctly

## Files Modified

### Backend Changes
1. **DatabaseManager.java**: Added `saveInstrumentIfNotExists()` method
2. **YahooFinanceProvider.java**: Updated to use preservation method
3. **DatabaseManagerSpec.groovy**: Added comprehensive tests

### No Frontend Changes Required
- Frontend bulk operations continue to work as designed
- API contracts remain unchanged
- User experience is improved (no data loss)

## Testing Strategy

### Automated Tests
- ✅ Unit tests for `saveInstrumentIfNotExists()` method
- ✅ Integration tests for data preservation scenarios
- ✅ Regression tests for new instrument creation

### Manual Testing Scenarios
1. **Existing Instrument Update**: Verify market cap preservation
2. **New Instrument Creation**: Verify proper instrument creation
3. **Bulk Operations**: Test multiple symbol updates
4. **Mixed Scenarios**: Test combination of existing and new symbols

## Expected Behavior After Fix

### OHLCV Updates Should:
- ✅ Update only OHLCV table data (price, volume, technical indicators)
- ✅ Preserve all existing instrument metadata (market cap, sector, industry)
- ✅ Create new instrument records only when symbols don't exist
- ✅ Log appropriate messages for debugging and monitoring

### OHLCV Updates Should NOT:
- ❌ Modify existing instrument market cap values
- ❌ Override existing sector or industry information
- ❌ Change existing instrument names or types
- ❌ Affect any data outside the OHLCV table

## Monitoring and Validation

### Log Messages to Watch For
```
INFO  - Created new instrument record for NEWSYMBOL
DEBUG - Using existing instrument record for AAPL (preserving metadata)
DEBUG - Instrument AAPL already exists, skipping save to preserve existing data
```

### Database Validation Queries
```sql
-- Verify market cap preservation
SELECT symbol, name, market_cap, sector, industry 
FROM instruments 
WHERE symbol IN ('AAPL', 'MSFT', 'GOOGL');

-- Check OHLCV data updates
SELECT symbol, COUNT(*) as ohlcv_records 
FROM ohlcv 
WHERE symbol IN ('AAPL', 'MSFT', 'GOOGL') 
GROUP BY symbol;
```

## Conclusion

The data integrity issue has been resolved through:

1. **Root Cause Identification**: Found the exact location where instrument data was being overwritten
2. **Targeted Fix**: Created a preservation method that only saves new instruments
3. **Comprehensive Testing**: Added tests to prevent regression
4. **Logging Enhancement**: Added clear logging for monitoring

**Result**: OHLCV updates are now completely isolated from instrument metadata updates, preserving existing market cap and other instrument fields while only updating price/volume data in the OHLCV table.

The bulk OHLCV update functionality will now work correctly without affecting instrument market cap or other metadata fields.
