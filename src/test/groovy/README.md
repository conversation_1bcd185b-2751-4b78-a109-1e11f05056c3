# Spock Testing Framework

This project uses the Spock framework for writing tests. Spock is a testing and specification framework for Java and Groovy applications that makes it easy to write expressive and readable tests.

## Key Features of Spock

1. **Expressive Specification Language**: Spock allows you to write tests in a more natural, specification-like language.
2. **Given-When-Then Structure**: Tests are structured in a way that clearly separates the setup, action, and verification phases.
3. **Data-Driven Testing**: Easily parameterize your tests with different inputs and expected outputs.
4. **Mocking and Stubbing**: Built-in mocking capabilities without requiring additional libraries.
5. **Failure Reporting**: Detailed and readable failure messages.

## Example Test Structure

```groovy
def "should perform some operation"() {
    given: "the initial setup"
    // Setup code here
    
    when: "an action is performed"
    // Action code here
    
    then: "the expected outcome is achieved"
    // Assertions here
    
    and: "additional conditions are met"
    // More assertions
    
    where: "we test with different inputs"
    input | expectedOutput
    1     | 2
    2     | 4
    3     | 6
}
```

## Writing Tests

1. Create a new Groovy file in the `src/test/groovy` directory with a name ending in `Spec.groovy`.
2. Extend the `spock.lang.Specification` class.
3. Write test methods using the given-when-then structure.

## Running Tests

You can run Spock tests using Gradle:

```bash
./gradlew test
```

Or run a specific test:

```bash
./gradlew test --tests "com.investment.provider.YahooFinanceScraperSpec"
```

## Advantages Over JUnit

1. **More Readable**: The given-when-then structure makes tests more readable and easier to understand.
2. **Less Boilerplate**: Spock reduces the amount of code needed to write tests.
3. **Better Failure Messages**: When tests fail, Spock provides more detailed and helpful error messages.
4. **Data Tables**: The `where` block allows for concise data-driven testing.
5. **Mocking**: Built-in mocking capabilities without requiring additional libraries.

## Example Comparison

### JUnit Test:
```java
@Test
void testAddition() {
    // Setup
    Calculator calculator = new Calculator();
    
    // Execute
    int result = calculator.add(2, 3);
    
    // Verify
    assertEquals(5, result);
}
```

### Spock Test:
```groovy
def "should add two numbers correctly"() {
    given: "a calculator"
    def calculator = new Calculator()
    
    when: "adding two numbers"
    def result = calculator.add(a, b)
    
    then: "the result should be the sum"
    result == expected
    
    where:
    a | b | expected
    1 | 2 | 3
    2 | 3 | 5
    5 | 8 | 13
}
```

The Spock test is more expressive, includes multiple test cases, and clearly separates the different phases of the test.
