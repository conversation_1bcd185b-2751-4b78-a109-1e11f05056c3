package com.investment.database

import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDate

/**
 * Test class to verify that DatabaseManager.getOHLCVData handles null date parameters correctly.
 * This test addresses the NullPointerException that occurred when retrieving all OHLCV data
 * without date filtering.
 */
class DatabaseManagerOHLCVNullDateTest extends Specification {

    @Subject
    DatabaseManager databaseManager

    def setup() {
        // Clean up any existing test database
        new File("./data/ohlcv_null_date_test.duckdb").delete()

        // Use file-based database for testing following project patterns
        DatabaseManager.setDbUrl("**********************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()

        // Save instrument first (required for foreign key constraint)
        databaseManager.saveInstrument("TEST", "Test Company", "Technology")

        // Insert some test data
        def testData = [
            new OHLCV("TEST", LocalDate.of(2024, 1, 1), 100.0, 105.0, 99.0, 104.0, 1000000L),
            new OHLCV("TEST", LocalDate.of(2024, 1, 2), 104.0, 106.0, 103.0, 105.0, 1100000L),
            new OHLCV("TEST", LocalDate.of(2024, 1, 3), 105.0, 107.0, 104.0, 106.0, 1200000L)
        ]
        databaseManager.saveOHLCVData(testData)
    }

    def cleanup() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/ohlcv_null_date_test.duckdb").delete()
    }

    def "should retrieve all OHLCV data when both startDate and endDate are null"() {
        when: "retrieving OHLCV data with null dates"
        List<OHLCV> result = databaseManager.getOHLCVData("TEST", null, null)

        then: "should return all data without throwing NullPointerException"
        result != null
        result.size() == 3
        result[0].date == LocalDate.of(2024, 1, 1)
        result[1].date == LocalDate.of(2024, 1, 2)
        result[2].date == LocalDate.of(2024, 1, 3)
    }

    def "should retrieve filtered data when only startDate is provided"() {
        when: "retrieving OHLCV data with only startDate"
        List<OHLCV> result = databaseManager.getOHLCVData("TEST", LocalDate.of(2024, 1, 2), null)

        then: "should return data from startDate onwards"
        result != null
        result.size() == 2
        result[0].date == LocalDate.of(2024, 1, 2)
        result[1].date == LocalDate.of(2024, 1, 3)
    }

    def "should retrieve filtered data when only endDate is provided"() {
        when: "retrieving OHLCV data with only endDate"
        List<OHLCV> result = databaseManager.getOHLCVData("TEST", null, LocalDate.of(2024, 1, 2))

        then: "should return data up to endDate"
        result != null
        result.size() == 2
        result[0].date == LocalDate.of(2024, 1, 1)
        result[1].date == LocalDate.of(2024, 1, 2)
    }

    def "should retrieve filtered data when both startDate and endDate are provided"() {
        when: "retrieving OHLCV data with both dates"
        List<OHLCV> result = databaseManager.getOHLCVData("TEST", 
            LocalDate.of(2024, 1, 2), LocalDate.of(2024, 1, 2))

        then: "should return data within the date range"
        result != null
        result.size() == 1
        result[0].date == LocalDate.of(2024, 1, 2)
    }

    def "should return empty list for non-existent symbol"() {
        when: "retrieving OHLCV data for non-existent symbol"
        List<OHLCV> result = databaseManager.getOHLCVData("NONEXISTENT", null, null)

        then: "should return empty list without throwing exception"
        result != null
        result.isEmpty()
    }
}
