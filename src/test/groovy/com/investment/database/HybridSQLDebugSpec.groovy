package com.investment.database

import spock.lang.Specification

import java.time.LocalDate

/**
 * Simple test to debug the hybrid SQL generation.
 */
class HybridSQLDebugSpec extends Specification {

    def "should generate valid SQL for hybrid DMI calculation"() {
        given: "a database manager instance"
        DatabaseManager.setDbUrl("****************************************")
        DatabaseManager databaseManager = new DatabaseManager()
        databaseManager.initDatabase()

        when: "building the hybrid DMI query"
        String sql = databaseManager.buildHybridDMIQuery("TEST", 14, null)

        then: "SQL should be generated"
        sql != null
        sql.length() > 0
        
        and: "SQL should contain expected keywords"
        sql.contains("WITH dmi_base AS")
        sql.contains("dmi_calc AS")
        sql.contains("dmi_smooth AS")
        sql.contains("SELECT")
        sql.contains("FROM dmi_smooth")
        sql.contains("ORDER BY date")

        cleanup:
        databaseManager?.closeConnection()
        new File("./data/sql_debug_test.duckdb").delete()
    }

    def "should execute hybrid SQL without syntax errors"() {
        given: "a database manager with test data"
        DatabaseManager.setDbUrl("***************************************")
        DatabaseManager databaseManager = new DatabaseManager()
        databaseManager.initDatabase()

        // Add test instrument and data
        databaseManager.saveInstrument("TEST", "Test Company", "Technology")

        when: "building and executing the hybrid DMI query"
        String sql = databaseManager.buildHybridDMIQuery("TEST", 14, null)
        println "Generated SQL: $sql"

        // Try to prepare the statement
        def pstmt = databaseManager.connection.prepareStatement(sql)
        pstmt.setString(1, "TEST")

        then: "should not throw SQL syntax errors"
        noExceptionThrown()

        cleanup:
        pstmt?.close()
        databaseManager?.closeConnection()
        new File("./data/sql_exec_test.duckdb").delete()
    }
}
