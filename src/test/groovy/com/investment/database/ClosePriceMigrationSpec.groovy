package com.investment.database

import spock.lang.Specification
import spock.lang.Shared
import java.sql.Connection
import java.sql.DriverManager
import java.sql.Statement
import java.sql.ResultSet
import java.math.BigDecimal

/**
 * Test specification for close_price column migration.
 */
class ClosePriceMigrationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager
    @Shared
    String testDbUrl = "****************************************************"

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/close_price_migration_test.duckdb").delete()
        new File("./data/close_price_migration_test.duckdb.wal").delete()
        
        // Set test database URL
        DatabaseManager.setDbUrl(testDbUrl)
        databaseManager = new DatabaseManager()
        databaseManager.connect()
    }

    def cleanupSpec() {
        if (databaseManager != null) {
            databaseManager.close()
        }
        // Clean up test database files
        new File("./data/close_price_migration_test.duckdb").delete()
        new File("./data/close_price_migration_test.duckdb.wal").delete()
    }

    def "should migrate to version 9 and add close_price column"() {
        when: "database is initialized"
        // Database should be at version 9 after initialization

        then: "schema version should be 9"
        getCurrentSchemaVersion() == 9

        and: "close_price column should exist in positions table"
        closePriceColumnExists()
    }

    def "should allow NULL values in close_price column"() {
        given: "a position is created without close_price"
        // First create a test instrument
        createTestInstrument("TEST")
        
        Long positionId = databaseManager.createPosition(
            "TEST",
            new BigDecimal("100"),
            "BUY",
            "OPEN",
            new BigDecimal("150.00"),
            new BigDecimal("15000.00"),
            new BigDecimal("100000.00"),
            new BigDecimal("1000.00"),
            new BigDecimal("0.02"),
            new BigDecimal("0.01")
        )

        when: "position is retrieved"
        Map<String, Object> position = databaseManager.getPositionById(positionId)

        then: "close_price should be null"
        position != null
        position.get("close_price") == null
    }

    def "should store and retrieve close_price when position is updated"() {
        given: "a position exists"
        createTestInstrument("AAPL")
        
        Long positionId = databaseManager.createPosition(
            "AAPL",
            new BigDecimal("100"),
            "BUY",
            "OPEN",
            new BigDecimal("150.00"),
            new BigDecimal("15000.00"),
            new BigDecimal("100000.00"),
            new BigDecimal("1000.00"),
            new BigDecimal("0.02"),
            new BigDecimal("0.01")
        )

        when: "position is updated with close_price"
        BigDecimal closePrice = new BigDecimal("155.75")
        databaseManager.updatePosition(
            positionId,
            new BigDecimal("155.75"), // lastPrice
            new BigDecimal("15575.00"), // lastValue
            new BigDecimal("1000.00"), // riskUnit
            new BigDecimal("0.02"), // stopPercent
            new BigDecimal("160.00"), // highestAfterTrade
            new BigDecimal("148.00"), // stopValueFromHighest
            new BigDecimal("152.00"), // lastBbmb
            new BigDecimal("0.01"), // bbmbAdjPercent
            new BigDecimal("150.48"), // stopValueFromBbmb
            "EXPANDING", // expandOrContract
            new BigDecimal("150.48"), // effectiveStopValue
            new BigDecimal("575.00"), // pnlValue
            new BigDecimal("0.0383"), // pnlPercent
            "CLOSED", // status
            closePrice // close_price
        )

        and: "position is retrieved"
        Map<String, Object> updatedPosition = databaseManager.getPositionById(positionId)

        then: "close_price should be stored correctly"
        updatedPosition != null
        updatedPosition.get("close_price") == closePrice
        updatedPosition.get("status") == "CLOSED"
    }

    def "should handle close_price in getPositions query"() {
        given: "multiple positions with different close_price values"
        createTestInstrument("MSFT")
        
        // Create open position (no close_price)
        Long openPositionId = databaseManager.createPosition(
            "MSFT",
            new BigDecimal("50"),
            "BUY",
            "OPEN",
            new BigDecimal("300.00"),
            new BigDecimal("15000.00"),
            new BigDecimal("100000.00"),
            new BigDecimal("1000.00"),
            new BigDecimal("0.02"),
            new BigDecimal("0.01")
        )
        
        // Create closed position with close_price
        Long closedPositionId = databaseManager.createPosition(
            "MSFT",
            new BigDecimal("50"),
            "BUY",
            "CLOSED",
            new BigDecimal("300.00"),
            new BigDecimal("15000.00"),
            new BigDecimal("100000.00"),
            new BigDecimal("1000.00"),
            new BigDecimal("0.02"),
            new BigDecimal("0.01")
        )
        
        // Update closed position with close_price
        databaseManager.updatePosition(
            closedPositionId,
            new BigDecimal("310.50"), // lastPrice
            new BigDecimal("15525.00"), // lastValue
            new BigDecimal("1000.00"), // riskUnit
            new BigDecimal("0.02"), // stopPercent
            null, null, null, null, null, null, null,
            new BigDecimal("525.00"), // pnlValue
            new BigDecimal("0.035"), // pnlPercent
            "CLOSED", // status
            new BigDecimal("310.50") // close_price
        )

        when: "all positions are retrieved"
        List<Map<String, Object>> positions = databaseManager.getPositions(null, null, null)

        then: "both positions should be returned with correct close_price values"
        positions.size() >= 2
        
        Map<String, Object> openPos = positions.find { it.get("id") == openPositionId }
        Map<String, Object> closedPos = positions.find { it.get("id") == closedPositionId }
        
        openPos != null
        openPos.get("close_price") == null
        openPos.get("status") == "OPEN"
        
        closedPos != null
        closedPos.get("close_price") == new BigDecimal("310.50")
        closedPos.get("status") == "CLOSED"
    }

    private int getCurrentSchemaVersion() {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            ResultSet rs = stmt.executeQuery("SELECT MAX(version) as version FROM schema_version")
            if (rs.next()) {
                return rs.getInt("version")
            }
            return 0
        } finally {
            conn.close()
        }
    }

    private boolean closePriceColumnExists() {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            stmt.executeQuery("SELECT close_price FROM positions LIMIT 1")
            return true
        } catch (Exception e) {
            return false
        } finally {
            conn.close()
        }
    }

    private void createTestInstrument(String symbol) {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            stmt.execute("""
                INSERT OR IGNORE INTO instruments (symbol, name, instrument_type, market_cap, country, ipo_year, sector, industry, created_at, updated_at)
                VALUES ('${symbol}', '${symbol} Test Company', 'Common Stock', 1000000000.00, 'USA', 2000, 'Technology', 'Software', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """)
        } finally {
            conn.close()
        }
    }
}
