package com.investment.database

import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Shared

import java.math.BigDecimal
import java.time.LocalDate

/**
 * Test specification for market cap ordering functionality in DatabaseManager.
 * Verifies that symbols are returned in correct market cap order and that
 * DMI calculations continue to work correctly with the new ordering.
 */
class DatabaseManagerMarketCapOrderingSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/market_cap_ordering_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("**************************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/market_cap_ordering_test.duckdb").delete()
    }

    def setup() {
        // Clean up any existing test data
        try {
            databaseManager.connection.createStatement().execute("DELETE FROM ohlcv WHERE symbol LIKE 'MKT_%'")
            databaseManager.connection.createStatement().execute("DELETE FROM instruments WHERE symbol LIKE 'MKT_%'")
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }

    def "should order symbols by market cap descending with null values last"() {
        given: "instruments with different market caps including null values"
        // Create instruments with varying market caps
        databaseManager.saveInstrumentWithDetails("MKT_LARGE", "Large Cap Company", "US_STOCK", 
                                                  new BigDecimal("500000000000"), "United States", 2000, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("MKT_SMALL", "Small Cap Company", "US_STOCK", 
                                                  new BigDecimal("1000000000"), "United States", 2010, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("MKT_MID", "Mid Cap Company", "US_STOCK", 
                                                  new BigDecimal("10000000000"), "United States", 2005, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("MKT_NULL", "No Market Cap Company", "US_STOCK", 
                                                  null, "United States", 2015, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("MKT_MEGA", "Mega Cap Company", "US_STOCK", 
                                                  new BigDecimal("2000000000000"), "United States", 1995, "Technology", "Software")

        and: "OHLCV data for all symbols"
        LocalDate baseDate = LocalDate.now().minusDays(30)
        ["MKT_LARGE", "MKT_SMALL", "MKT_MID", "MKT_NULL", "MKT_MEGA"].each { symbol ->
            List<OHLCV> data = createTestOHLCVData(symbol, 30, baseDate)
            databaseManager.saveOHLCVData(data)
        }

        when: "retrieving symbols with market cap ordering"
        List<String> symbolsByMarketCap = databaseManager.getSymbolsWithOhlcvData(true)
        List<String> symbolsAlphabetical = databaseManager.getSymbolsWithOhlcvData(false)

        then: "symbols should be ordered by market cap (descending) with null last"
        def marketCapSymbols = symbolsByMarketCap.findAll { it.startsWith("MKT_") }
        marketCapSymbols.size() == 5
        marketCapSymbols[0] == "MKT_MEGA"    // $2T
        marketCapSymbols[1] == "MKT_LARGE"   // $500B
        marketCapSymbols[2] == "MKT_MID"     // $10B
        marketCapSymbols[3] == "MKT_SMALL"   // $1B
        marketCapSymbols[4] == "MKT_NULL"    // null (last)

        and: "alphabetical ordering should still work"
        def alphabeticalSymbols = symbolsAlphabetical.findAll { it.startsWith("MKT_") }
        alphabeticalSymbols.size() == 5
        alphabeticalSymbols == ["MKT_LARGE", "MKT_MEGA", "MKT_MID", "MKT_NULL", "MKT_SMALL"]
    }

    def "should handle symbols that exist in OHLCV but not in instruments table"() {
        given: "an instrument with OHLCV data but no corresponding instruments record"
        // This should not happen in normal operation due to foreign key constraints,
        // but we test the behavior for robustness
        
        // First create a valid instrument and OHLCV data
        databaseManager.saveInstrumentWithDetails("MKT_VALID", "Valid Company", "US_STOCK", 
                                                  new BigDecimal("100000000000"), "United States", 2000, "Technology", "Software")
        List<OHLCV> validData = createTestOHLCVData("MKT_VALID", 10, LocalDate.now().minusDays(10))
        databaseManager.saveOHLCVData(validData)

        when: "retrieving symbols with market cap ordering"
        List<String> symbolsByMarketCap = databaseManager.getSymbolsWithOhlcvData(true)

        then: "only symbols with corresponding instruments records should be returned"
        def validSymbols = symbolsByMarketCap.findAll { it.startsWith("MKT_VALID") }
        validSymbols.size() == 1
        validSymbols[0] == "MKT_VALID"
    }

    def "should maintain DMI calculation accuracy with market cap ordering"() {
        given: "instruments with market cap data and sufficient OHLCV history"
        databaseManager.saveInstrumentWithDetails("MKT_DMI_A", "DMI Test A", "US_STOCK", 
                                                  new BigDecimal("200000000000"), "United States", 2000, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("MKT_DMI_B", "DMI Test B", "US_STOCK", 
                                                  new BigDecimal("100000000000"), "United States", 2005, "Technology", "Software")

        // Create 50 days of realistic OHLCV data for DMI calculation
        LocalDate startDate = LocalDate.now().minusDays(50)
        List<OHLCV> dataA = createRealisticOHLCVData("MKT_DMI_A", 50, startDate)
        List<OHLCV> dataB = createRealisticOHLCVData("MKT_DMI_B", 50, startDate)
        
        databaseManager.saveOHLCVData(dataA)
        databaseManager.saveOHLCVData(dataB)

        when: "calculating DMI with market cap ordering (default behavior)"
        int updatedRecordsA = databaseManager.calculateAndUpdateDMI("MKT_DMI_A", 14, false)
        int updatedRecordsB = databaseManager.calculateAndUpdateDMI("MKT_DMI_B", 14, false)

        then: "DMI calculations should be successful"
        updatedRecordsA > 0
        updatedRecordsB > 0

        when: "retrieving calculated DMI data"
        List<OHLCV> resultsA = databaseManager.getOHLCVData("MKT_DMI_A", startDate, startDate.plusDays(49))
        List<OHLCV> resultsB = databaseManager.getOHLCVData("MKT_DMI_B", startDate, startDate.plusDays(49))

        then: "DMI values should be calculated correctly"
        resultsA.size() == 50
        resultsB.size() == 50
        
        // Check that DMI values are present for recent dates (after the initial period)
        def recentResultsA = resultsA.findAll { it.dmiPlusDi != null && it.dmiMinusDi != null && it.dmiAdx != null }
        def recentResultsB = resultsB.findAll { it.dmiPlusDi != null && it.dmiMinusDi != null && it.dmiAdx != null }
        
        recentResultsA.size() >= 20  // Should have DMI values for most recent dates
        recentResultsB.size() >= 20
        
        // Verify DMI values are within reasonable ranges
        recentResultsA.each { ohlcv ->
            assert ohlcv.dmiPlusDi >= 0 && ohlcv.dmiPlusDi <= 100
            assert ohlcv.dmiMinusDi >= 0 && ohlcv.dmiMinusDi <= 100
            assert ohlcv.dmiAdx >= 0 && ohlcv.dmiAdx <= 100
        }
        
        recentResultsB.each { ohlcv ->
            assert ohlcv.dmiPlusDi >= 0 && ohlcv.dmiPlusDi <= 100
            assert ohlcv.dmiMinusDi >= 0 && ohlcv.dmiMinusDi <= 100
            assert ohlcv.dmiAdx >= 0 && ohlcv.dmiAdx <= 100
        }

        and: "symbols should be returned in market cap order"
        List<String> orderedSymbols = databaseManager.getSymbolsWithOhlcvData(true)
        def dmiSymbols = orderedSymbols.findAll { it.startsWith("MKT_DMI_") }
        dmiSymbols.size() == 2
        dmiSymbols[0] == "MKT_DMI_A"  // Higher market cap ($200B)
        dmiSymbols[1] == "MKT_DMI_B"  // Lower market cap ($100B)
    }

    def "should handle edge cases with market cap ordering"() {
        given: "instruments with edge case market cap values"
        databaseManager.saveInstrumentWithDetails("MKT_ZERO", "Zero Market Cap", "US_STOCK", 
                                                  BigDecimal.ZERO, "United States", 2020, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("MKT_NEGATIVE", "Negative Market Cap", "US_STOCK", 
                                                  new BigDecimal("-1000000"), "United States", 2021, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("MKT_TINY", "Tiny Market Cap", "US_STOCK", 
                                                  new BigDecimal("1"), "United States", 2022, "Technology", "Software")

        and: "OHLCV data for edge case symbols"
        LocalDate baseDate = LocalDate.now().minusDays(10)
        ["MKT_ZERO", "MKT_NEGATIVE", "MKT_TINY"].each { symbol ->
            List<OHLCV> data = createTestOHLCVData(symbol, 10, baseDate)
            databaseManager.saveOHLCVData(data)
        }

        when: "retrieving symbols with market cap ordering"
        List<String> symbolsByMarketCap = databaseManager.getSymbolsWithOhlcvData(true)

        then: "symbols should be ordered correctly including edge cases"
        def edgeCaseSymbols = symbolsByMarketCap.findAll { it.startsWith("MKT_") && (it.contains("ZERO") || it.contains("NEGATIVE") || it.contains("TINY")) }
        edgeCaseSymbols.size() == 3
        
        // Order should be: TINY (1), ZERO (0), NEGATIVE (-1000000)
        def tinyIndex = edgeCaseSymbols.indexOf("MKT_TINY")
        def zeroIndex = edgeCaseSymbols.indexOf("MKT_ZERO")
        def negativeIndex = edgeCaseSymbols.indexOf("MKT_NEGATIVE")
        
        tinyIndex < zeroIndex
        zeroIndex < negativeIndex
    }

    def "should preserve backward compatibility with existing code"() {
        given: "instruments and OHLCV data"
        databaseManager.saveInstrumentWithDetails("MKT_COMPAT", "Compatibility Test", "US_STOCK", 
                                                  new BigDecimal("50000000000"), "United States", 2000, "Technology", "Software")
        List<OHLCV> data = createTestOHLCVData("MKT_COMPAT", 20, LocalDate.now().minusDays(20))
        databaseManager.saveOHLCVData(data)

        when: "calling the original method without parameters"
        List<String> defaultOrdering = databaseManager.getSymbolsWithOhlcvData()

        and: "calling the new method with explicit market cap ordering"
        List<String> explicitMarketCapOrdering = databaseManager.getSymbolsWithOhlcvData(true)

        then: "both should return the same result (market cap ordering by default)"
        defaultOrdering == explicitMarketCapOrdering
        
        and: "should contain our test symbol"
        defaultOrdering.contains("MKT_COMPAT")
        explicitMarketCapOrdering.contains("MKT_COMPAT")
    }

    /**
     * Create test OHLCV data with simple price progression.
     */
    private List<OHLCV> createTestOHLCVData(String symbol, int days, LocalDate startDate) {
        List<OHLCV> data = []
        double basePrice = 100.0

        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            double open = basePrice + (i * 0.1)
            double close = open + 0.5
            double high = Math.max(open, close) + 0.2
            double low = Math.min(open, close) - 0.2
            long volume = 1000000L

            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
        }

        return data
    }

    /**
     * Create realistic OHLCV data with proper price movements for DMI calculation.
     */
    private List<OHLCV> createRealisticOHLCVData(String symbol, int days, LocalDate startDate) {
        List<OHLCV> data = []
        double basePrice = 100.0
        Random random = new Random(symbol.hashCode()) // Deterministic based on symbol

        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements with trends and volatility
            double change = (random.nextGaussian() * 0.02) // 2% daily volatility
            double open = basePrice * (1 + change)
            double close = open * (1 + (random.nextGaussian() * 0.015)) // 1.5% intraday volatility
            double high = Math.max(open, close) * (1 + Math.abs(random.nextGaussian() * 0.01))
            double low = Math.min(open, close) * (1 - Math.abs(random.nextGaussian() * 0.01))
            long volume = 1000000L + (long)(random.nextGaussian() * 200000)

            data.add(new OHLCV(symbol, date, open, high, low, close, Math.max(volume, 100000L)))
            basePrice = close // Use close as next day's base
        }

        return data
    }
}
