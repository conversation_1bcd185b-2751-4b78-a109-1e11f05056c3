package com.investment.database

import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Debug test for hybrid incremental DMI calculation.
 */
class HybridIncrementalDebugSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/hybrid_incremental_debug.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("**************************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/hybrid_incremental_debug.duckdb").delete()
    }

    def setup() {
        // Clean up any existing test data
        try {
            databaseManager.connection.createStatement().execute("DELETE FROM ohlcv WHERE symbol LIKE 'DEBUG_%'")
            databaseManager.connection.createStatement().execute("DELETE FROM instruments WHERE symbol LIKE 'DEBUG_%'")
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }

    def "should debug hybrid incremental calculation"() {
        given: "initial test data"
        String symbol = "DEBUG_HYBRID"
        List<OHLCV> initialData = createTestOHLCVData(symbol, 30)

        when: "saving initial data and calculating DMI"
        databaseManager.saveInstrument(symbol, "Debug Hybrid Company", "Technology")
        databaseManager.saveOHLCVData(initialData)
        int initialUpdated = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "initial calculation should be successful"
        initialUpdated > 0
        println "Initial calculation updated: $initialUpdated records"

        when: "checking last DMI calculation date"
        LocalDate lastCalculatedDate = databaseManager.getLastDMICalculationDate(symbol)
        println "Last calculated date: $lastCalculatedDate"

        and: "adding more data"
        List<OHLCV> additionalData = createTestOHLCVData(symbol, 5, initialData[-1].date.plusDays(1))
        databaseManager.saveOHLCVData(additionalData)
        println "Added ${additionalData.size()} additional records from ${additionalData[0].date} to ${additionalData[-1].date}"

        and: "performing incremental calculation"
        int incrementalUpdated = databaseManager.calculateAndUpdateDMIHybridIncremental(symbol, 14, false)
        println "Incremental calculation updated: $incrementalUpdated records"

        then: "incremental calculation should update some records"
        incrementalUpdated > 0

        when: "checking all data"
        List<OHLCV> allData = databaseManager.getOHLCVData(symbol, 
                                                           initialData[0].date, 
                                                           additionalData[-1].date)
        def recordsWithDMI = allData.findAll { it.dmiPlusDi != null && it.dmiMinusDi != null && it.dmiAdx != null }
        println "Total records: ${allData.size()}, Records with DMI: ${recordsWithDMI.size()}"

        then: "should have DMI values for the new data"
        allData.size() == initialData.size() + additionalData.size()
        recordsWithDMI.size() >= (allData.size() - 27)
    }

    def "should compare pure Java vs hybrid incremental"() {
        given: "test data for comparison"
        String javaSymbol = "DEBUG_JAVA"
        String hybridSymbol = "DEBUG_HYBRID_CMP"
        List<OHLCV> initialData = createTestOHLCVData("DEBUG", 30)

        when: "setting up identical initial data"
        databaseManager.saveInstrument(javaSymbol, "Debug Java Company", "Technology")
        databaseManager.saveInstrument(hybridSymbol, "Debug Hybrid Company", "Technology")
        
        List<OHLCV> javaInitial = initialData.collect { ohlcv ->
            new OHLCV(javaSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> hybridInitial = initialData.collect { ohlcv ->
            new OHLCV(hybridSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        
        databaseManager.saveOHLCVData(javaInitial)
        databaseManager.saveOHLCVData(hybridInitial)

        and: "performing initial calculations"
        int javaInitialUpdated = databaseManager.calculateAndUpdateDMI(javaSymbol, 14, false)
        int hybridInitialUpdated = databaseManager.calculateAndUpdateDMIHybrid(hybridSymbol, 14, false)
        
        println "Java initial updated: $javaInitialUpdated, Hybrid initial updated: $hybridInitialUpdated"

        and: "checking last calculation dates"
        LocalDate javaLastDate = databaseManager.getLastDMICalculationDate(javaSymbol)
        LocalDate hybridLastDate = databaseManager.getLastDMICalculationDate(hybridSymbol)
        println "Java last date: $javaLastDate, Hybrid last date: $hybridLastDate"

        and: "adding identical additional data"
        List<OHLCV> additionalData = createTestOHLCVData("DEBUG", 5, initialData[-1].date.plusDays(1))
        
        List<OHLCV> javaAdditional = additionalData.collect { ohlcv ->
            new OHLCV(javaSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> hybridAdditional = additionalData.collect { ohlcv ->
            new OHLCV(hybridSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        
        databaseManager.saveOHLCVData(javaAdditional)
        databaseManager.saveOHLCVData(hybridAdditional)
        
        println "Added ${additionalData.size()} additional records from ${additionalData[0].date} to ${additionalData[-1].date}"

        and: "performing incremental calculations"
        int javaIncrementalUpdated = databaseManager.calculateAndUpdateDMIIncremental(javaSymbol, 14, false)
        int hybridIncrementalUpdated = databaseManager.calculateAndUpdateDMIHybridIncremental(hybridSymbol, 14, false)
        
        println "Java incremental updated: $javaIncrementalUpdated, Hybrid incremental updated: $hybridIncrementalUpdated"

        then: "both approaches should update the same number of records"
        javaInitialUpdated == hybridInitialUpdated
        javaIncrementalUpdated == hybridIncrementalUpdated
        javaIncrementalUpdated > 0
    }

    /**
     * Create test OHLCV data with realistic price movements.
     */
    private List<OHLCV> createTestOHLCVData(String symbol, int days, LocalDate startDate = LocalDate.now().minusDays(days)) {
        List<OHLCV> data = []
        double basePrice = 100.0
        Random random = new Random(42) // Fixed seed for reproducible tests

        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements
            double change = (random.nextGaussian() * 0.02) // 2% daily volatility
            double open = basePrice * (1 + change)
            double close = open * (1 + (random.nextGaussian() * 0.015)) // 1.5% intraday volatility
            double high = Math.max(open, close) * (1 + Math.abs(random.nextGaussian() * 0.01))
            double low = Math.min(open, close) * (1 - Math.abs(random.nextGaussian() * 0.01))
            long volume = 1000000L + (long)(random.nextGaussian() * 200000)

            data.add(new OHLCV(symbol, date, open, high, low, close, Math.max(volume, 100000L)))
            basePrice = close // Use close as next day's base
        }

        return data
    }
}
