package com.investment.database

import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Test specification for DMI NaN handling improvements.
 * Verifies that the fixes for ADX NaN propagation work correctly.
 */
class DatabaseManagerDMINaNHandlingSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/nan_handling_test.duckdb").delete()

        // Initialize test database
        DatabaseManager.setDbUrl("*******************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/nan_handling_test.duckdb").delete()
    }

    def setup() {
        // Clean up any existing test data
        databaseManager.connection.createStatement().execute("DELETE FROM ohlcv WHERE symbol LIKE 'NAN_TEST%'")
        databaseManager.connection.createStatement().execute("DELETE FROM instruments WHERE symbol LIKE 'NAN_TEST%'")
    }

    def "should handle edge case where +DI and -DI sum to zero"() {
        given: "test data that creates zero sum for +DI and -DI"
        String symbol = "NAN_TEST_ZERO_SUM"
        List<OHLCV> testData = createZeroSumDITestData(symbol)

        when: "saving the instrument and data"
        databaseManager.saveInstrument(symbol, "NaN Test Zero Sum Company", "Technology")
        databaseManager.saveOHLCVData(testData)

        and: "calculating DMI using hybrid approach"
        int updatedRecords = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "calculation should complete without propagating NaN values indefinitely"
        updatedRecords > 0

        when: "retrieving the calculated data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 testData[0].date, 
                                                                 testData[-1].date)

        then: "should have valid DMI data where mathematically possible"
        def recordsWithDMI = retrievedData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() > 0
        
        and: "ADX should recover from temporary NaN DX values"
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        recordsWithADX.size() > 0
        
        and: "no infinite or invalid values should be present"
        retrievedData.each { record ->
            if (record.dmiPlusDi != null) {
                assert record.dmiPlusDi >= 0 && record.dmiPlusDi <= 100
            }
            if (record.dmiMinusDi != null) {
                assert record.dmiMinusDi >= 0 && record.dmiMinusDi <= 100
            }
            if (record.dmiDx != null) {
                assert record.dmiDx >= 0 && record.dmiDx <= 100
            }
            if (record.dmiAdx != null) {
                assert record.dmiAdx >= 0 && record.dmiAdx <= 100
            }
        }
    }

    def "should handle consecutive NaN DX values and allow ADX recovery"() {
        given: "test data that creates consecutive NaN DX values"
        String symbol = "NAN_TEST_CONSECUTIVE"
        List<OHLCV> testData = createConsecutiveNaNTestData(symbol)

        when: "saving the instrument and data"
        databaseManager.saveInstrument(symbol, "NaN Test Consecutive Company", "Technology")
        databaseManager.saveOHLCVData(testData)

        and: "calculating DMI using hybrid approach"
        int updatedRecords = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "calculation should complete successfully"
        updatedRecords > 0

        when: "retrieving the calculated data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 testData[0].date, 
                                                                 testData[-1].date)

        then: "ADX should eventually recover after consecutive NaN values"
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        recordsWithADX.size() > 0
        
        and: "final ADX values should be mathematically valid"
        def finalRecordsWithADX = retrievedData.findAll { it.dmiAdx != null }.sort { it.date }
        if (finalRecordsWithADX.size() > 1) {
            // Check that ADX values are reasonable (not stuck at NaN)
            def lastADX = finalRecordsWithADX[-1].dmiAdx
            assert lastADX >= 0 && lastADX <= 100
        }
    }

    def "should compare pure Java and hybrid approaches for NaN handling consistency"() {
        given: "identical test data for both approaches"
        String symbolJava = "NAN_TEST_JAVA"
        String symbolHybrid = "NAN_TEST_HYBRID"
        List<OHLCV> testData = createNaNHandlingTestData(symbolJava)
        List<OHLCV> hybridData = testData.collect { record ->
            new OHLCV(symbolHybrid, record.date, record.open, record.high, record.low, record.close, record.volume)
        }

        when: "saving instruments and data"
        databaseManager.saveInstrument(symbolJava, "NaN Test Java Company", "Technology")
        databaseManager.saveInstrument(symbolHybrid, "NaN Test Hybrid Company", "Technology")
        databaseManager.saveOHLCVData(testData)
        databaseManager.saveOHLCVData(hybridData)

        and: "calculating DMI using both approaches"
        int javaUpdatedRecords = databaseManager.calculateAndUpdateDMI(symbolJava, 14, false)
        int hybridUpdatedRecords = databaseManager.calculateAndUpdateDMIHybrid(symbolHybrid, 14, false)

        then: "both approaches should complete successfully"
        javaUpdatedRecords > 0
        hybridUpdatedRecords > 0

        when: "retrieving results from both approaches"
        List<OHLCV> javaResults = databaseManager.getOHLCVData(symbolJava, 
                                                               testData[0].date, 
                                                               testData[-1].date)
        List<OHLCV> hybridResults = databaseManager.getOHLCVData(symbolHybrid, 
                                                                  testData[0].date, 
                                                                  testData[-1].date)

        then: "both approaches should handle NaN values consistently"
        def javaADXCount = javaResults.count { it.dmiAdx != null }
        def hybridADXCount = hybridResults.count { it.dmiAdx != null }
        
        // Allow some tolerance for different NaN handling strategies
        Math.abs(javaADXCount - hybridADXCount) <= 2
        
        and: "final ADX values should be similar between approaches"
        def javaFinalADX = javaResults.findAll { it.dmiAdx != null }.sort { it.date }[-1]?.dmiAdx
        def hybridFinalADX = hybridResults.findAll { it.dmiAdx != null }.sort { it.date }[-1]?.dmiAdx
        
        if (javaFinalADX != null && hybridFinalADX != null) {
            Math.abs(javaFinalADX - hybridFinalADX) < 5.0 // Allow 5% difference
        }
    }

    /**
     * Create test data that results in +DI and -DI summing to zero (or very close to zero).
     * This tests the improved SQL DX calculation logic.
     */
    private List<OHLCV> createZeroSumDITestData(String symbol) {
        List<OHLCV> data = []
        LocalDate startDate = LocalDate.now().minusDays(50)
        
        // Create initial normal data
        for (int i = 0; i < 30; i++) {
            data << new OHLCV(symbol, startDate.plusDays(i), 100.0 + i * 0.1, 101.0 + i * 0.1, 99.0 + i * 0.1, 100.5 + i * 0.1, 1000000L)
        }
        
        // Create problematic data where +DM and -DM are both zero or very small
        // This happens when price movements are minimal
        for (int i = 30; i < 45; i++) {
            double basePrice = 103.0
            data << new OHLCV(symbol, startDate.plusDays(i), basePrice, basePrice + 0.001, basePrice - 0.001, basePrice, 1000000L)
        }
        
        // Resume normal data to test recovery
        for (int i = 45; i < 50; i++) {
            data << new OHLCV(symbol, startDate.plusDays(i), 103.0 + (i - 45) * 0.2, 104.0 + (i - 45) * 0.2, 102.0 + (i - 45) * 0.2, 103.5 + (i - 45) * 0.2, 1000000L)
        }
        
        return data
    }

    /**
     * Create test data that results in consecutive NaN DX values.
     */
    private List<OHLCV> createConsecutiveNaNTestData(String symbol) {
        List<OHLCV> data = []
        LocalDate startDate = LocalDate.now().minusDays(60)
        
        // Create normal data
        for (int i = 0; i < 30; i++) {
            data << new OHLCV(symbol, startDate.plusDays(i), 50.0 + i * 0.5, 52.0 + i * 0.5, 48.0 + i * 0.5, 51.0 + i * 0.5, 1000000L)
        }
        
        // Create consecutive periods with zero directional movement
        for (int i = 30; i < 40; i++) {
            double flatPrice = 65.0
            data << new OHLCV(symbol, startDate.plusDays(i), flatPrice, flatPrice, flatPrice, flatPrice, 1000000L)
        }
        
        // Resume with strong directional movement to test recovery
        for (int i = 40; i < 60; i++) {
            data << new OHLCV(symbol, startDate.plusDays(i), 65.0 + (i - 40) * 1.0, 67.0 + (i - 40) * 1.0, 63.0 + (i - 40) * 1.0, 66.0 + (i - 40) * 1.0, 1000000L)
        }
        
        return data
    }

    /**
     * Create comprehensive test data for NaN handling comparison.
     */
    private List<OHLCV> createNaNHandlingTestData(String symbol) {
        List<OHLCV> data = []
        LocalDate startDate = LocalDate.now().minusDays(80)
        
        // Mix of normal, problematic, and recovery data
        for (int i = 0; i < 80; i++) {
            double basePrice = 100.0
            double volatility = 1.0
            
            // Create different market conditions
            if (i >= 20 && i < 30) {
                // Low volatility period (potential NaN DX)
                volatility = 0.01
            } else if (i >= 40 && i < 50) {
                // Flat market (zero directional movement)
                volatility = 0.0
                basePrice = 110.0
            } else if (i >= 60 && i < 70) {
                // High volatility recovery
                volatility = 3.0
            }
            
            data << new OHLCV(symbol, startDate.plusDays(i), basePrice + i * 0.1, basePrice + i * 0.1 + volatility, basePrice + i * 0.1 - volatility, basePrice + i * 0.1 + (volatility * 0.5), 1000000L)
        }
        
        return data
    }
}
