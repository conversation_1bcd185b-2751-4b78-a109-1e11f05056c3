package com.investment.database

import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Test specification for hybrid SQL+Java DMI calculation functionality.
 * Verifies that the hybrid approach produces identical results to the pure Java implementation.
 */
class DatabaseManagerHybridDMISpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/hybrid_dmi_test.duckdb").delete()

        // Initialize test database
        DatabaseManager.setDbUrl("*****************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/hybrid_dmi_test.duckdb").delete()
    }

    def setup() {
        // Clean up any existing test data
        try {
            databaseManager.connection.createStatement().execute("DELETE FROM ohlcv WHERE symbol LIKE 'TEST%'")
            databaseManager.connection.createStatement().execute("DELETE FROM instruments WHERE symbol LIKE 'TEST%'")
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }

    def "should calculate DMI using hybrid SQL+Java approach"() {
        given: "test OHLCV data for DMI calculation"
        String symbol = "TEST_HYBRID"
        List<OHLCV> testData = createTestOHLCVData(symbol, 50) // 50 days of data

        when: "saving the instrument and data"
        databaseManager.saveInstrument(symbol, "Test Hybrid Company", "Technology")
        databaseManager.saveOHLCVData(testData)

        and: "calculating DMI using hybrid approach"
        int updatedRecords = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "DMI calculation should be successful"
        updatedRecords > 0

        when: "retrieving the calculated data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 testData[0].date, 
                                                                 testData[-1].date)

        then: "should have DMI values calculated"
        retrievedData.size() == testData.size()
        
        // Check that DMI values are calculated for sufficient data points
        def recordsWithDMI = retrievedData.findAll { it.dmiPlusDi != null && it.dmiMinusDi != null && it.dmiAdx != null }
        recordsWithDMI.size() >= (testData.size() - 27) // Should have DMI for all but first 27 records (2*14-1)
        
        // Verify that DX values are also calculated
        def recordsWithDX = retrievedData.findAll { it.dmiDx != null }
        recordsWithDX.size() >= (testData.size() - 27)
    }

    def "should produce identical results between pure Java and hybrid SQL+Java approaches"() {
        given: "test OHLCV data for comparison"
        String symbolJava = "TEST_JAVA"
        String symbolHybrid = "TEST_HYBRID_CMP"
        List<OHLCV> testData = createTestOHLCVData("TEST", 100) // 100 days for better comparison

        when: "saving instruments and data for both symbols"
        databaseManager.saveInstrument(symbolJava, "Test Java Company", "Technology")
        databaseManager.saveInstrument(symbolHybrid, "Test Hybrid Company", "Technology")
        
        // Create identical data for both symbols
        List<OHLCV> javaData = testData.collect { ohlcv ->
            new OHLCV(symbolJava, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> hybridData = testData.collect { ohlcv ->
            new OHLCV(symbolHybrid, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        
        databaseManager.saveOHLCVData(javaData)
        databaseManager.saveOHLCVData(hybridData)

        and: "calculating DMI using pure Java approach"
        int javaUpdatedRecords = databaseManager.calculateAndUpdateDMI(symbolJava, 14, false)

        and: "calculating DMI using hybrid SQL+Java approach"
        int hybridUpdatedRecords = databaseManager.calculateAndUpdateDMIHybrid(symbolHybrid, 14, false)

        then: "both approaches should update the same number of records"
        javaUpdatedRecords == hybridUpdatedRecords
        javaUpdatedRecords > 0

        when: "retrieving results from both approaches"
        List<OHLCV> javaResults = databaseManager.getOHLCVData(symbolJava, 
                                                               testData[0].date, 
                                                               testData[-1].date)
        List<OHLCV> hybridResults = databaseManager.getOHLCVData(symbolHybrid, 
                                                                  testData[0].date, 
                                                                  testData[-1].date)

        then: "results should be identical"
        javaResults.size() == hybridResults.size()
        
        for (int i = 0; i < javaResults.size(); i++) {
            def javaRecord = javaResults[i]
            def hybridRecord = hybridResults[i]
            
            // Compare dates
            javaRecord.date == hybridRecord.date
            
            // Compare DMI values (allowing for small floating point differences)
            if (javaRecord.dmiPlusDi != null && hybridRecord.dmiPlusDi != null) {
                Math.abs(javaRecord.dmiPlusDi - hybridRecord.dmiPlusDi) < 0.001
            } else {
                javaRecord.dmiPlusDi == hybridRecord.dmiPlusDi // Both should be null
            }
            
            if (javaRecord.dmiMinusDi != null && hybridRecord.dmiMinusDi != null) {
                Math.abs(javaRecord.dmiMinusDi - hybridRecord.dmiMinusDi) < 0.001
            } else {
                javaRecord.dmiMinusDi == hybridRecord.dmiMinusDi // Both should be null
            }
            
            if (javaRecord.dmiDx != null && hybridRecord.dmiDx != null) {
                Math.abs(javaRecord.dmiDx - hybridRecord.dmiDx) < 0.001
            } else {
                javaRecord.dmiDx == hybridRecord.dmiDx // Both should be null
            }
            
            if (javaRecord.dmiAdx != null && hybridRecord.dmiAdx != null) {
                Math.abs(javaRecord.dmiAdx - hybridRecord.dmiAdx) < 0.001
            } else {
                javaRecord.dmiAdx == hybridRecord.dmiAdx // Both should be null
            }
        }
    }

    def "should handle incremental hybrid DMI calculation"() {
        given: "initial test data"
        String symbol = "TEST_INCR_HYBRID"
        List<OHLCV> initialData = createTestOHLCVData(symbol, 30)

        when: "saving initial data and calculating DMI"
        databaseManager.saveInstrument(symbol, "Test Incremental Hybrid Company", "Technology")
        databaseManager.saveOHLCVData(initialData)
        int initialUpdated = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "initial calculation should be successful"
        initialUpdated > 0

        when: "adding more data and performing incremental calculation"
        List<OHLCV> additionalData = createTestOHLCVData(symbol, 10, initialData[-1].date.plusDays(1))
        databaseManager.saveOHLCVData(additionalData)
        int incrementalUpdated = databaseManager.calculateAndUpdateDMIHybridIncremental(symbol, 14, false)

        then: "incremental calculation should be successful"
        incrementalUpdated > 0

        when: "retrieving all data"
        List<OHLCV> allData = databaseManager.getOHLCVData(symbol, 
                                                           initialData[0].date, 
                                                           additionalData[-1].date)

        then: "should have DMI values for the new data"
        allData.size() == initialData.size() + additionalData.size()
        def recordsWithDMI = allData.findAll { it.dmiPlusDi != null && it.dmiMinusDi != null && it.dmiAdx != null }
        recordsWithDMI.size() >= (allData.size() - 27)
    }

    def "should handle dry run mode for hybrid calculations"() {
        given: "test data"
        String symbol = "TEST_DRY_HYBRID"
        List<OHLCV> testData = createTestOHLCVData(symbol, 50)

        when: "saving data and performing dry run"
        databaseManager.saveInstrument(symbol, "Test Dry Run Hybrid Company", "Technology")
        databaseManager.saveOHLCVData(testData)
        int dryRunCount = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, true)

        then: "dry run should return expected count without updating data"
        dryRunCount > 0

        when: "checking that no DMI data was actually calculated"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 testData[0].date, 
                                                                 testData[-1].date)

        then: "should have no DMI values"
        retrievedData.every { it.dmiPlusDi == null && it.dmiMinusDi == null && it.dmiAdx == null && it.dmiDx == null }
    }

    /**
     * Create test OHLCV data with realistic price movements for DMI calculation testing.
     */
    private List<OHLCV> createTestOHLCVData(String symbol, int days, LocalDate startDate = LocalDate.now().minusDays(days)) {
        List<OHLCV> data = []
        double basePrice = 100.0
        Random random = new Random(42) // Fixed seed for reproducible tests

        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements
            double change = (random.nextGaussian() * 0.02) // 2% daily volatility
            double open = basePrice * (1 + change)
            double close = open * (1 + (random.nextGaussian() * 0.015)) // 1.5% intraday volatility
            double high = Math.max(open, close) * (1 + Math.abs(random.nextGaussian() * 0.01))
            double low = Math.min(open, close) * (1 - Math.abs(random.nextGaussian() * 0.01))
            long volume = 1000000L + (long)(random.nextGaussian() * 200000)

            data.add(new OHLCV(symbol, date, open, high, low, close, Math.max(volume, 100000L)))
            basePrice = close // Use close as next day's base
        }

        return data
    }
}
