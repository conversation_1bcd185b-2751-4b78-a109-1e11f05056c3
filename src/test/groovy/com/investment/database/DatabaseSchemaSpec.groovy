package com.investment.database

import spock.lang.Specification
import com.investment.model.OHLCV

import java.math.BigDecimal
import java.sql.Connection
import java.sql.DriverManager
import java.sql.Statement
import java.time.LocalDate

/**
 * Simple test to verify the new database schema works correctly.
 */
class DatabaseSchemaSpec extends Specification {

    String testDbUrl = "*************************************"
    DatabaseManager databaseManager

    def setup() {
        // Clean up any existing test database
        new File("./data/schema_test.duckdb").delete()
        
        DatabaseManager.setDbUrl(testDbUrl)
        databaseManager = new DatabaseManager()
    }

    def cleanup() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/schema_test.duckdb").delete()
    }

    def "should create new schema and save instruments with extended data"() {
        when: "initializing the database"
        databaseManager.initDatabase()

        and: "saving an instrument with extended data"
        databaseManager.saveInstrumentWithDetails(
            "AAPL", 
            "Apple Inc.", 
            "US_STOCK",
            new BigDecimal("3000000000000"), // 3 trillion market cap
            "United States",
            1980,
            "Technology",
            "Consumer Electronics"
        )

        then: "the instrument should be saved with all details"
        def instrument = getInstrumentFromDb("AAPL")
        instrument != null
        instrument.symbol == "AAPL"
        instrument.name == "Apple Inc."
        instrument.instrument_type == "US_STOCK"
        instrument.market_cap == new BigDecimal("3000000000000.00")
        instrument.country == "United States"
        instrument.ipo_year == 1980
        instrument.sector == "Technology"
        instrument.industry == "Consumer Electronics"
    }

    def "should support backward compatibility with old saveInstrument method"() {
        when: "initializing the database"
        databaseManager.initDatabase()

        and: "saving an instrument with old method"
        databaseManager.saveInstrument("MSFT", "Microsoft Corporation", "US_STOCK")

        then: "the instrument should be saved with basic info"
        def instrument = getInstrumentFromDb("MSFT")
        instrument != null
        instrument.symbol == "MSFT"
        instrument.name == "Microsoft Corporation"
        instrument.instrument_type == "US_STOCK"
        instrument.market_cap == null
        instrument.country == null
        instrument.ipo_year == null
        instrument.sector == null
        instrument.industry == null
    }

    def "should create OHLCV table with Bollinger Band columns"() {
        when: "initializing the database"
        databaseManager.initDatabase()

        and: "saving an instrument first"
        databaseManager.saveInstrument("AAPL", "Apple Inc.", "US_STOCK")

        and: "saving OHLCV data with Bollinger Bands"
        def ohlcvData = [
            new OHLCV("AAPL", LocalDate.of(2024, 1, 1), 150.0, 155.0, 148.0, 152.0, 1000000L,
                     151.0, 2.5, 156.0, 146.0), // with Bollinger Bands
            new OHLCV("AAPL", LocalDate.of(2024, 1, 2), 152.0, 157.0, 150.0, 154.0, 1100000L) // without Bollinger Bands
        ]
        databaseManager.saveOHLCVData(ohlcvData)

        then: "OHLCV data should be saved with Bollinger Band columns"
        def retrievedData = databaseManager.getOHLCVData("AAPL", LocalDate.of(2024, 1, 1), LocalDate.of(2024, 1, 2))
        retrievedData.size() == 2

        and: "first record should have Bollinger Band data"
        def firstRecord = retrievedData.find { it.date == LocalDate.of(2024, 1, 1) }
        firstRecord.bbMiddleBand == 151.0
        firstRecord.bbStdDev == 2.5
        firstRecord.bbUpperBand == 156.0
        firstRecord.bbLowerBand == 146.0

        and: "second record should have null Bollinger Band data"
        def secondRecord = retrievedData.find { it.date == LocalDate.of(2024, 1, 2) }
        secondRecord.bbMiddleBand == null
        secondRecord.bbStdDev == null
        secondRecord.bbUpperBand == null
        secondRecord.bbLowerBand == null
    }

    def "should retrieve only symbols with OHLCV data"() {
        when: "initializing the database"
        databaseManager.initDatabase()

        and: "saving instruments"
        databaseManager.saveInstrument("AAPL", "Apple Inc.", "US_STOCK")
        databaseManager.saveInstrument("MSFT", "Microsoft Corp.", "US_STOCK")
        databaseManager.saveInstrument("NOOHLCV", "No OHLCV Data Corp.", "US_STOCK")

        and: "saving OHLCV data for only some instruments"
        def ohlcvData = [
            new OHLCV("AAPL", LocalDate.of(2024, 1, 1), 150.0, 155.0, 148.0, 152.0, 1000000L),
            new OHLCV("MSFT", LocalDate.of(2024, 1, 1), 300.0, 305.0, 298.0, 302.0, 500000L)
        ]
        databaseManager.saveOHLCVData(ohlcvData)

        then: "getAllSymbols should return all instruments"
        def allSymbols = databaseManager.getAllSymbols()
        allSymbols.size() == 3
        allSymbols.containsAll(["AAPL", "MSFT", "NOOHLCV"])

        and: "getSymbolsWithOhlcvData should return only symbols with OHLCV data"
        def symbolsWithOhlcv = databaseManager.getSymbolsWithOhlcvData()
        symbolsWithOhlcv.size() == 2
        symbolsWithOhlcv.containsAll(["AAPL", "MSFT"])
        !symbolsWithOhlcv.contains("NOOHLCV")

        and: "symbols should be ordered alphabetically"
        symbolsWithOhlcv == ["AAPL", "MSFT"]
    }

    private Map getInstrumentFromDb(String symbol) {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            def rs = stmt.executeQuery("SELECT * FROM instruments WHERE symbol = '${symbol}'")
            if (rs.next()) {
                return [
                    symbol: rs.getString("symbol"),
                    name: rs.getString("name"),
                    instrument_type: rs.getString("instrument_type"),
                    market_cap: rs.getBigDecimal("market_cap"),
                    country: rs.getString("country"),
                    ipo_year: rs.getObject("ipo_year", Integer.class),
                    sector: rs.getString("sector"),
                    industry: rs.getString("industry")
                ]
            }
        } finally {
            conn.close()
        }
        return null
    }
}
