package com.investment.provider

import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.TempDir

import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.time.LocalDate

/**
 * Spock specification for testing the YahooFinanceScraper.
 * This demonstrates the advantages of <PERSON>pock's expressive syntax and
 * the given/when/then blocks for better test readability.
 */
class YahooFinanceScraperSpec extends Specification {

    @Subject
    YahooFinanceScraper scraper = new YahooFinanceScraper()

    def "should scrape historical data from Yahoo Finance"() {
        given: "a stock symbol and date range"
        String symbol = "AAPL"
        LocalDate startDate = LocalDate.now().minusDays(30) // Use a wider date range to ensure we get data
        LocalDate endDate = LocalDate.now()

        and: "the corresponding Unix timestamps"
        long period1 = startDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC)
        long period2 = endDate.plusDays(1).atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC)

        when: "scraping historical data from Yahoo Finance"
        List<OHLCV> results = scraper.scrapeHistoricalData(symbol, period1, period2)

        then: "we should get non-empty results or skip the test if network issues"
        if (results.isEmpty()) {
            println "WARNING: No data returned from Yahoo Finance. Skipping assertions."
            return
        }

        and: "each data point should have the correct structure"
        results.each { OHLCV data ->
            assert data.symbol == symbol
            assert data.date.isAfter(startDate.minusDays(1))
            assert data.date.isBefore(endDate.plusDays(1))
            assert data.open > 0
            assert data.high > 0
            assert data.low > 0
            assert data.close > 0
            assert data.volume > 0
        }
    }

    def "should handle invalid symbols gracefully"() {
        given: "an invalid stock symbol"
        String invalidSymbol = "INVALID_SYMBOL_XYZ123"
        LocalDate startDate = LocalDate.now().minusDays(7)
        LocalDate endDate = LocalDate.now()

        and: "the corresponding Unix timestamps"
        long period1 = startDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC)
        long period2 = endDate.plusDays(1).atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC)

        when: "attempting to scrape data for an invalid symbol"
        List<OHLCV> results = scraper.scrapeHistoricalData(invalidSymbol, period1, period2)

        then: "we should get an empty list rather than an exception"
        results.isEmpty()
    }

    def "should save HTML content to file"() {
        given: "a stock symbol and date range"
        String symbol = "AAPL"
        LocalDate startDate = LocalDate.now().minusDays(1) // Just one day to minimize data
        LocalDate endDate = LocalDate.now()

        and: "the corresponding Unix timestamps"
        long period1 = startDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC)
        long period2 = endDate.plusDays(1).atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC)

        and: "the HTML storage directory path"
        String downloadDir = System.getProperty("user.home") + "/Downloads/yahoo-finance-html"
        Path dirPath = Paths.get(downloadDir)

        and: "the count of HTML files before the test"
        int fileCountBefore = 0
        if (Files.exists(dirPath)) {
            fileCountBefore = countHtmlFiles(dirPath, symbol)
        }

        when: "scraping historical data from Yahoo Finance"
        scraper.scrapeHistoricalData(symbol, period1, period2)

        then: "the HTML storage directory should exist"
        Files.exists(dirPath)

        and: "at least one new HTML file should have been created"
        int fileCountAfter = countHtmlFiles(dirPath, symbol)
        fileCountAfter > fileCountBefore
    }

    private int countHtmlFiles(Path dirPath, String symbol) {
        // Count HTML files for the given symbol
        return (int) Files.list(dirPath)
                .filter { path -> path.fileName.toString().startsWith("yahoo_finance_" + symbol) }
                .filter { path -> path.fileName.toString().endsWith(".html") }
                .count()
    }

    // This test is commented out because it requires network access and may be flaky
    // It's included as an example of Spock's data-driven testing capabilities
    /*
    def "should handle different date ranges correctly"() {
        given: "a stock symbol and various date ranges"
        LocalDate start = LocalDate.now().minusDays(startDaysAgo)
        LocalDate end = LocalDate.now().minusDays(endDaysAgo)

        and: "the corresponding Unix timestamps"
        long p1 = start.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC)
        long p2 = end.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC)

        when: "scraping historical data for the specified date range"
        List<OHLCV> results = scraper.scrapeHistoricalData(symbol, p1, p2)

        then: "we should get appropriate results"
        results.size() >= expectedMinResults

        where: "we test with different symbols and date ranges"
        symbol | startDaysAgo | endDaysAgo | expectedMinResults
        "AAPL" | 30          | 20         | 5  // Expect at least 5 trading days in a 10-day period
        "MSFT" | 10          | 5          | 2  // Expect at least 2 trading days in a 5-day period
        "GOOG" | 60          | 30         | 15 // Expect at least 15 trading days in a 30-day period
    }
    */

    // HTML Cleanup Tests
    @TempDir
    Path tempDir

    def "should clean up HTML files successfully"() {
        given: "a temporary directory with HTML and non-HTML files"
        def htmlFile1 = tempDir.resolve("test1.html")
        def htmlFile2 = tempDir.resolve("test2.HTML")  // Test case insensitive
        def htmlFile3 = tempDir.resolve("yahoo_finance_AAPL_123456.html")
        def txtFile = tempDir.resolve("test.txt")
        def csvFile = tempDir.resolve("data.csv")

        // Create test files
        Files.write(htmlFile1, "<html><body>Test 1</body></html>".bytes)
        Files.write(htmlFile2, "<html><body>Test 2</body></html>".bytes)
        Files.write(htmlFile3, "<html><body>Yahoo Finance Data</body></html>".bytes)
        Files.write(txtFile, "This is a text file".bytes)
        Files.write(csvFile, "symbol,date,price\nAAPL,2023-01-01,150.00".bytes)

        and: "verify all files exist"
        assert Files.exists(htmlFile1)
        assert Files.exists(htmlFile2)
        assert Files.exists(htmlFile3)
        assert Files.exists(txtFile)
        assert Files.exists(csvFile)

        when: "calling cleanUpHtmlFiles using reflection to access private method"
        def method = YahooFinanceScraper.getDeclaredMethod("cleanUpHtmlFiles")
        method.setAccessible(true)

        // Set the HTML storage directory for testing
        scraper.setHtmlStorageDir(tempDir.toString())

        method.invoke(scraper)

        then: "HTML files should be deleted but other files should remain"
        !Files.exists(htmlFile1)
        !Files.exists(htmlFile2)
        !Files.exists(htmlFile3)
        Files.exists(txtFile)
        Files.exists(csvFile)
    }

    def "should handle empty directory gracefully"() {
        given: "an empty directory"
        def emptyDir = tempDir.resolve("empty")
        Files.createDirectory(emptyDir)

        when: "calling cleanUpHtmlFiles on empty directory"
        def method = YahooFinanceScraper.getDeclaredMethod("cleanUpHtmlFiles")
        method.setAccessible(true)

        scraper.setHtmlStorageDir(emptyDir.toString())
        method.invoke(scraper)

        then: "no exception should be thrown"
        notThrown(Exception)
    }

    def "should handle non-existent directory gracefully"() {
        given: "a non-existent directory path"
        def nonExistentDir = tempDir.resolve("does-not-exist")

        when: "calling cleanUpHtmlFiles on non-existent directory"
        def method = YahooFinanceScraper.getDeclaredMethod("cleanUpHtmlFiles")
        method.setAccessible(true)

        scraper.setHtmlStorageDir(nonExistentDir.toString())
        method.invoke(scraper)

        then: "no exception should be thrown"
        notThrown(Exception)
    }

    def "should handle files with HTML in name but different extension"() {
        given: "files with 'html' in name but different extensions"
        def htmlTxtFile = tempDir.resolve("html_data.txt")
        def htmlCsvFile = tempDir.resolve("export_html.csv")
        def actualHtmlFile = tempDir.resolve("page.html")

        Files.write(htmlTxtFile, "HTML data in text file".bytes)
        Files.write(htmlCsvFile, "HTML,data,csv".bytes)
        Files.write(actualHtmlFile, "<html><body>Real HTML</body></html>".bytes)

        when: "calling cleanUpHtmlFiles"
        def method = YahooFinanceScraper.getDeclaredMethod("cleanUpHtmlFiles")
        method.setAccessible(true)

        scraper.setHtmlStorageDir(tempDir.toString())
        method.invoke(scraper)

        then: "only actual HTML file should be deleted"
        Files.exists(htmlTxtFile)
        Files.exists(htmlCsvFile)
        !Files.exists(actualHtmlFile)
    }
}
