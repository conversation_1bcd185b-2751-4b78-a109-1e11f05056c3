package com.investment.provider

import com.investment.database.DatabaseManager
import com.investment.model.Instrument
import com.investment.model.InstrumentType
import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDate

/**
 * Spock specification for testing the YahooFinanceProvider.
 * This demonstrates <PERSON><PERSON><PERSON>'s mocking capabilities.
 */
class YahooFinanceProviderSpec extends Specification {

    // The class under test
    @Subject
    YahooFinanceProvider provider

    // Test data
    Instrument instrument
    LocalDate startDate
    LocalDate endDate
    List<OHLCV> testData

    def setup() {
        // Initialize the provider
        provider = new YahooFinanceProvider()

        // Setup test data
        instrument = new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK)
        startDate = LocalDate.of(2023, 1, 1)
        endDate = LocalDate.of(2023, 1, 31)

        // Create some test OHLCV data
        testData = [
            new OHLCV("AAPL", LocalDate.of(2023, 1, 2), 130.0, 135.0, 129.0, 133.0, 100000),
            new OHLCV("AAPL", LocalDate.of(2023, 1, 3), 133.0, 138.0, 132.0, 137.0, 120000),
            new OHLCV("AAPL", LocalDate.of(2023, 1, 4), 137.0, 140.0, 135.0, 139.0, 110000)
        ]
    }

    def "should download historical data and save to database"() {
        given: "a database manager"
        def dbManager = Mock(DatabaseManager)

        and: "a mock scraper that returns test data"
        def mockScraper = Mock(YahooFinanceScraper)
        provider.scraper = mockScraper

        and: "the scraper will return test data when called"
        mockScraper.scrapeHistoricalData(_, _, _) >> testData

        when: "downloading historical data"
        provider.downloadHistoricalData(instrument, startDate, endDate, dbManager)

        then: "the instrument should be saved to the database"
        1 * dbManager.saveInstrument(instrument.getSymbol(), instrument.getName(), instrument.getType().name())

        and: "the OHLCV data should be saved to the database"
        1 * dbManager.saveOHLCVData(testData)
    }

    def "should handle exceptions when downloading data"() {
        given: "a database manager"
        def dbManager = Mock(DatabaseManager)

        and: "a mock scraper that throws an exception"
        def mockScraper = Mock(YahooFinanceScraper)
        provider.scraper = mockScraper
        mockScraper.scrapeHistoricalData(_, _, _) >> { throw new IOException("Network error") }

        when: "downloading historical data"
        provider.downloadHistoricalData(instrument, startDate, endDate, dbManager)

        then: "a RuntimeException should be thrown"
        def exception = thrown(RuntimeException)
        exception.message == "Failed to download historical data"
        exception.cause instanceof IOException

        and: "the instrument should still be saved to the database"
        1 * dbManager.saveInstrument(_, _, _)
    }
}
