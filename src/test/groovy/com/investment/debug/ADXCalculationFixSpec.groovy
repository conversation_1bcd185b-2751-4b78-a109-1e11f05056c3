package com.investment.debug

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import spock.lang.Shared
import spock.lang.Specification

import java.time.LocalDate

/**
 * Debug test specification to verify that the ADX calculation fix works correctly.
 * This test directly tests the DatabaseManager's DMI calculation to isolate the ADX issue.
 */
class ADXCalculationFixSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/adx_fix_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("**************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        if (databaseManager) {
            databaseManager.closeConnection()
        }
        // Clean up test database
        new File("./data/adx_fix_test.duckdb").delete()
    }

    def "should calculate ADX values correctly without NaN"() {
        given: "a test symbol with sufficient data for DMI calculation"
        String symbol = "ADX_FIX_TEST"
        databaseManager.saveInstrument(symbol, "ADX Fix Test Company", "Technology")
        
        and: "50 days of realistic test data"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        when: "calculating DMI using Pure Java method"
        int recordsUpdated = databaseManager.calculateAndUpdateDMI(symbol, 14, false)
        
        then: "calculation should complete successfully"
        recordsUpdated > 0
        println "Records updated: $recordsUpdated"

        when: "retrieving the calculated data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 testData[0].date, 
                                                                 testData[-1].date)
        
        then: "should have DMI data"
        def recordsWithDMI = retrievedData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() > 0
        println "Records with DMI data: ${recordsWithDMI.size()}"

        and: "should have ADX values (this was the bug - ADX was NaN)"
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        recordsWithADX.size() > 0
        println "Records with ADX values: ${recordsWithADX.size()}"

        and: "ADX values should be valid numbers (not NaN)"
        recordsWithADX.every { record ->
            !Double.isNaN(record.dmiAdx)
        }
        println "SUCCESS: All ADX values are valid numbers (not NaN)"

        and: "ADX values should be reasonable (between 0 and 100)"
        recordsWithADX.every { record ->
            record.dmiAdx >= 0.0 && record.dmiAdx <= 100.0
        }

        and: "print some sample ADX values for verification"
        recordsWithADX.take(5).each { record ->
            println "Date: ${record.date}, +DI: ${String.format('%.2f', record.dmiPlusDi)}, " +
                   "-DI: ${String.format('%.2f', record.dmiMinusDi)}, " +
                   "DX: ${String.format('%.2f', record.dmiDx)}, " +
                   "ADX: ${String.format('%.2f', record.dmiAdx)}"
        }
    }

    def "should calculate ADX values correctly using Hybrid SQL+Java method"() {
        given: "a test symbol with sufficient data for DMI calculation"
        String symbol = "ADX_HYBRID_TEST"
        databaseManager.saveInstrument(symbol, "ADX Hybrid Test Company", "Technology")

        and: "50 days of realistic test data"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        when: "calculating DMI using Hybrid SQL+Java method"
        int recordsUpdated = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "calculation should complete successfully"
        recordsUpdated > 0
        println "Hybrid method - Records updated: $recordsUpdated"

        when: "retrieving the calculated data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol,
                                                                 testData[0].date,
                                                                 testData[-1].date)

        then: "should have ADX values (this was the bug - ADX was NaN)"
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        recordsWithADX.size() > 0
        println "Hybrid method - Records with ADX values: ${recordsWithADX.size()}"

        and: "ADX values should be valid numbers (not NaN)"
        recordsWithADX.every { record ->
            !Double.isNaN(record.dmiAdx)
        }
        println "SUCCESS: Hybrid method - All ADX values are valid numbers (not NaN)"

        and: "ADX values should be reasonable (between 0 and 100)"
        recordsWithADX.every { record ->
            record.dmiAdx >= 0.0 && record.dmiAdx <= 100.0
        }

        and: "print some sample ADX values for verification"
        recordsWithADX.take(5).each { record ->
            println "Hybrid Date: ${record.date}, +DI: ${String.format('%.2f', record.dmiPlusDi)}, " +
                   "-DI: ${String.format('%.2f', record.dmiMinusDi)}, " +
                   "DX: ${String.format('%.2f', record.dmiDx)}, " +
                   "ADX: ${String.format('%.2f', record.dmiAdx)}"
        }
    }

    def "should reproduce the production issue with INCREMENTAL mode and HYBRID method"() {
        given: "a test symbol simulating production conditions"
        String symbol = "PROD_ISSUE_TEST"
        databaseManager.saveInstrument(symbol, "Production Issue Test Company", "Technology")

        and: "large dataset similar to AAPL (1000+ records)"
        List<OHLCV> testData = createRealisticTestData(symbol, 1200)
        databaseManager.saveOHLCVData(testData)

        when: "performing initial full calculation using Hybrid method"
        int initialRecords = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "initial calculation should work"
        initialRecords > 0
        println "Initial hybrid calculation - Records updated: $initialRecords"

        when: "adding new data and performing incremental calculation (simulating production)"
        List<OHLCV> newData = createRealisticTestData(symbol + "_NEW", 10)
        newData = newData.collect { ohlcv ->
            new OHLCV(symbol, ohlcv.date.plusDays(1200), ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        databaseManager.saveOHLCVData(newData)

        int incrementalRecords = databaseManager.calculateAndUpdateDMIHybridIncremental(symbol, 14, false)

        then: "incremental calculation should work"
        incrementalRecords > 0
        println "Incremental hybrid calculation - Records updated: $incrementalRecords"

        when: "retrieving all calculated data"
        List<OHLCV> allData = databaseManager.getOHLCVData(symbol,
                                                           testData[0].date,
                                                           newData[-1].date)

        then: "should have ADX values for recent records"
        def recentRecordsWithADX = allData.findAll { it.dmiAdx != null && it.date.isAfter(testData[-10].date) }
        recentRecordsWithADX.size() > 0
        println "Recent records with ADX values: ${recentRecordsWithADX.size()}"

        and: "ADX values should be valid numbers (not null/NaN)"
        recentRecordsWithADX.every { record ->
            record.dmiAdx != null && !Double.isNaN(record.dmiAdx)
        }
        println "SUCCESS: All recent ADX values are valid (not null/NaN)"

        and: "print some recent ADX values to verify they match production expectations"
        recentRecordsWithADX.take(5).each { record ->
            println "Recent Date: ${record.date}, +DI: ${String.format('%.2f', record.dmiPlusDi)}, " +
                   "-DI: ${String.format('%.2f', record.dmiMinusDi)}, " +
                   "DX: ${String.format('%.2f', record.dmiDx)}, " +
                   "ADX: ${record.dmiAdx != null ? String.format('%.2f', record.dmiAdx) : 'null'}"
        }
    }

    def "should compare Pure Java vs Hybrid SQL+Java methods"() {
        given: "a test symbol for comparison"
        String symbol = "COMPARISON_TEST"
        databaseManager.saveInstrument(symbol, "Comparison Test Company", "Technology")

        and: "test data for comparison"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        when: "calculating DMI using Pure Java method"
        int pureJavaRecords = databaseManager.calculateAndUpdateDMI(symbol, 14, false)

        then: "Pure Java calculation should work"
        pureJavaRecords > 0
        println "Pure Java - Records updated: $pureJavaRecords"

        when: "retrieving Pure Java results"
        List<OHLCV> pureJavaData = databaseManager.getOHLCVData(symbol, testData[0].date, testData[-1].date)

        then: "check Pure Java ADX pattern"
        def pureJavaADX = pureJavaData.findAll { it.dmiAdx != null }
        pureJavaADX.size() > 0
        println "Pure Java - Records with ADX: ${pureJavaADX.size()}"
        println "Pure Java - First ADX at index: ${pureJavaData.findIndexOf { it.dmiAdx != null }}"

        when: "clearing DMI data and calculating with Hybrid method"
        // Clear DMI data by setting all DMI columns to null
        databaseManager.connection.prepareStatement(
            "UPDATE ohlcv SET dmi_plus_di = NULL, dmi_minus_di = NULL, dmi_dx = NULL, dmi_adx = NULL WHERE symbol = ?"
        ).with { stmt ->
            stmt.setString(1, symbol)
            stmt.executeUpdate()
        }
        int hybridRecords = databaseManager.calculateAndUpdateDMIHybrid(symbol, 14, false)

        then: "Hybrid calculation should work"
        hybridRecords > 0
        println "Hybrid - Records updated: $hybridRecords"

        when: "retrieving Hybrid results"
        List<OHLCV> hybridData = databaseManager.getOHLCVData(symbol, testData[0].date, testData[-1].date)

        then: "check Hybrid ADX pattern"
        def hybridADX = hybridData.findAll { it.dmiAdx != null }
        hybridADX.size() > 0
        println "Hybrid - Records with ADX: ${hybridADX.size()}"
        println "Hybrid - First ADX at index: ${hybridData.findIndexOf { it.dmiAdx != null }}"

        and: "both methods should have ADX starting at the same index"
        def pureJavaFirstADXIndex = pureJavaData.findIndexOf { it.dmiAdx != null }
        def hybridFirstADXIndex = hybridData.findIndexOf { it.dmiAdx != null }
        pureJavaFirstADXIndex == hybridFirstADXIndex
        println "SUCCESS: Both methods start ADX at index $pureJavaFirstADXIndex"
    }

    def "should test exact production scenario with DMIService and default settings"() {
        given: "a test symbol simulating AAPL production conditions"
        String symbol = "AAPL_PROD_SIM"
        databaseManager.saveInstrument(symbol, "AAPL Production Simulation", "Technology")

        and: "smaller dataset for debugging (50 records to see debug output clearly)"
        List<OHLCV> testData = createRealisticTestData(symbol, 50)
        databaseManager.saveOHLCVData(testData)

        when: "using DMIService with default settings (which uses HYBRID_SQL_JAVA by default)"
        // Create a mock DMIService to simulate production conditions
        def mockProcessExecutor = Mock(com.investment.process.AsyncProcessExecutor)
        def dmiService = new com.investment.service.DMIService(databaseManager, mockProcessExecutor)

        // Create request with default settings (INCREMENTAL mode, HYBRID_SQL_JAVA method)
        def request = new com.investment.api.model.DMIRequest()
        request.symbols = [symbol]
        request.period = 14
        request.dryRun = false
        // Note: calculationMode defaults to INCREMENTAL, calculationMethod defaults to HYBRID_SQL_JAVA

        println "Testing with default DMI settings:"
        println "  Calculation Mode: ${request.calculationMode}"
        println "  Calculation Method: ${request.calculationMethod}"
        println "  Period: ${request.period}"
        println "  Symbol: ${symbol} (${testData.size()} records)"

        def response = dmiService.calculateDMI(request)

        then: "calculation should complete successfully"
        response.status == "success"
        response.processedSymbols == 1
        response.totalRecordsUpdated > 0
        println "DMI calculation completed: ${response.totalRecordsUpdated} records updated"

        when: "retrieving the calculated data to check for null ADX values"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol,
                                                                 testData[0].date,
                                                                 testData[-1].date)

        then: "should have DMI data"
        def recordsWithDMI = retrievedData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() > 0
        println "Records with DMI data: ${recordsWithDMI.size()}"

        and: "should have ADX values (this is where production fails)"
        def recordsWithADX = retrievedData.findAll { it.dmiAdx != null }
        recordsWithADX.size() > 0
        println "Records with ADX values: ${recordsWithADX.size()}"

        and: "ADX values should be valid numbers (not null/NaN)"
        recordsWithADX.every { record ->
            record.dmiAdx != null && !Double.isNaN(record.dmiAdx)
        }
        println "SUCCESS: All ADX values are valid (not null/NaN)"

        and: "check for records with DX but no ADX (this is EXPECTED behavior in DMI)"
        def recordsWithDXButNoADX = retrievedData.findAll { it.dmiDx != null && it.dmiAdx == null }
        // For period=14, we expect exactly 14 records with DX but no ADX (indices 13-26)
        def expectedGapSize = 14 // period
        recordsWithDXButNoADX.size() == expectedGapSize
        println "Records with DX but no ADX: ${recordsWithDXButNoADX.size()} (expected: ${expectedGapSize})"

        and: "verify this gap is mathematically correct for DMI algorithm"
        // The gap should be exactly 'period' records between first DX and first ADX
        def firstDXIndex = retrievedData.findIndexOf { it.dmiDx != null }
        def firstADXIndex = retrievedData.findIndexOf { it.dmiAdx != null }
        (firstADXIndex - firstDXIndex) == expectedGapSize
        println "SUCCESS: DMI gap is mathematically correct - DX starts at index ${firstDXIndex}, ADX starts at index ${firstADXIndex}"

        and: "log some sample records to show the expected pattern"
        println "Sample DX-only records (expected behavior):"
        recordsWithDXButNoADX.take(3).each { record ->
            println "  Date: ${record.date}, DX: ${String.format('%.2f', record.dmiDx)}, ADX: null (expected)"
        }

        println "Sample ADX records (calculation working correctly):"
        def adxRecords = retrievedData.findAll { it.dmiAdx != null }
        adxRecords.take(3).each { record ->
            println "  Date: ${record.date}, DX: ${String.format('%.2f', record.dmiDx)}, ADX: ${String.format('%.2f', record.dmiAdx)}"
        }
    }

    /**
     * Create realistic test data with varying price movements to ensure proper DMI calculation.
     */
    private List<OHLCV> createRealisticTestData(String symbol, int days) {
        List<OHLCV> data = []
        LocalDate startDate = LocalDate.now().minusDays(days)
        
        double basePrice = 100.0
        double currentPrice = basePrice
        
        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements with some volatility
            double change = (Math.random() - 0.5) * 4.0 // +/- 2% change
            currentPrice = Math.max(currentPrice + change, 10.0) // Don't go below $10
            
            double open = currentPrice
            double high = open + Math.random() * 2.0
            double low = open - Math.random() * 2.0
            double close = low + Math.random() * (high - low)
            long volume = (long)(1000000 + Math.random() * 2000000)
            
            // Ensure high >= low and close is between high and low
            if (high < low) {
                double temp = high
                high = low
                low = temp
            }
            close = Math.max(low, Math.min(high, close))
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            currentPrice = close
        }
        
        return data
    }
}
