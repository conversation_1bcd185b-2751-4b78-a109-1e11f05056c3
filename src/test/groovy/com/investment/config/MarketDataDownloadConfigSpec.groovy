package com.investment.config

import spock.lang.Specification

import java.time.LocalDate

/**
 * Spock specification for testing the MarketDataDownloadConfig.
 */
class MarketDataDownloadConfigSpec extends Specification {

    def "should create default configuration with sensible defaults"() {
        when: "creating default configuration"
        def config = new MarketDataDownloadConfig()

        then: "should have sensible defaults"
        config.startDate != null
        config.endDate == LocalDate.now()
        config.continueOnError == true
        config.maxRetries == 3
        config.retryDelayMs == 1000
        config.skipExistingData == true
        config.startDate.year <= LocalDate.now().year - 50 // Should be many years back
    }

    def "should create configuration with custom date range"() {
        given: "custom start and end dates"
        def startDate = LocalDate.of(2020, 1, 1)
        def endDate = LocalDate.of(2023, 12, 31)

        when: "creating configuration with custom dates"
        def config = new MarketDataDownloadConfig(startDate, endDate)

        then: "should use custom dates"
        config.startDate == startDate
        config.endDate == endDate
        config.continueOnError == true // Other defaults should remain
    }

    def "should create full historical data configuration"() {
        when: "creating full historical data configuration"
        def config = MarketDataDownloadConfig.fullHistoricalData()

        then: "should configure for full history"
        config.startDate.year == MarketDataDownloadConfig.YAHOO_HISTORICAL_DATA_START_YEAR
        config.startDate.monthValue == 1
        config.startDate.dayOfMonth == 2
        config.skipExistingData == false
        config.endDate == LocalDate.now()
    }

    def "should create recent data update configuration"() {
        when: "creating recent data update configuration"
        def config = MarketDataDownloadConfig.recentDataUpdate()

        then: "should configure for recent updates"
        config.startDate == LocalDate.now().minusDays(30)
        config.skipExistingData == true
        config.endDate == LocalDate.now()
    }

    def "should create date range configuration"() {
        given: "specific date range"
        def startDate = LocalDate.of(2022, 6, 1)
        def endDate = LocalDate.of(2022, 12, 31)

        when: "creating date range configuration"
        def config = MarketDataDownloadConfig.dateRange(startDate, endDate)

        then: "should use specified date range"
        config.startDate == startDate
        config.endDate == endDate
    }

    def "should allow modification of configuration parameters"() {
        given: "a default configuration"
        def config = new MarketDataDownloadConfig()

        when: "modifying configuration parameters"
        config.setContinueOnError(false)
        config.setMaxRetries(5)
        config.setRetryDelayMs(2000)
        config.setSkipExistingData(false)

        then: "should reflect the changes"
        config.continueOnError == false
        config.maxRetries == 5
        config.retryDelayMs == 2000
        config.skipExistingData == false
    }

    def "should provide meaningful toString representation"() {
        given: "a configuration"
        def config = new MarketDataDownloadConfig()
        config.setStartDate(LocalDate.of(2020, 1, 1))
        config.setEndDate(LocalDate.of(2023, 12, 31))

        when: "converting to string"
        def stringRepresentation = config.toString()

        then: "should contain key information"
        stringRepresentation.contains("startDate=2020-01-01")
        stringRepresentation.contains("endDate=2023-12-31")
        stringRepresentation.contains("continueOnError=true")
        stringRepresentation.contains("maxRetries=3")
        stringRepresentation.contains("retryDelayMs=1000")
        stringRepresentation.contains("skipExistingData=true")
    }

    def "should validate Yahoo historical data start year constant"() {
        expect: "Yahoo historical data start year to be reasonable"
        MarketDataDownloadConfig.YAHOO_HISTORICAL_DATA_START_YEAR == 1962
    }

    def "should calculate default history years correctly"() {
        when: "checking default history years"
        def defaultYears = MarketDataDownloadConfig.DEFAULT_HISTORY_YEARS

        then: "should be a reasonable number of years"
        defaultYears > 50
        defaultYears < 100
        defaultYears == LocalDate.now().year - MarketDataDownloadConfig.YAHOO_HISTORICAL_DATA_START_YEAR - 1
    }
}
