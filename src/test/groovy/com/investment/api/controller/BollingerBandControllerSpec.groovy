package com.investment.api.controller

import com.investment.api.model.BollingerBandRequest
import com.investment.api.model.BollingerBandResponse
import com.investment.service.BollingerBandService
import com.investment.service.DMIService
import spock.lang.Specification
import spock.lang.Subject

/**
 * Unit tests for Bollinger Band REST API controller endpoints.
 */
class BollingerBandControllerSpec extends Specification {

    BollingerBandService mockBollingerBandService = Mock()
    DMIService mockDMIService = Mock()

    @Subject
    TechnicalIndicatorController controller = new TechnicalIndicatorController(mockBollingerBandService, mockDMIService)

    def "should handle Bollinger Band calculation request successfully"() {
        given: "a valid Bollinger Band request"
        def request = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        
        and: "mock service returns successful response"
        def mockResponse = new BollingerBandResponse(
            "success", 10, 500, [], [], [], 1000L, [], false,
            new BollingerBandResponse.CalculationParameters(20, 2.0, 20, BollingerBandRequest.CalculationMode.INCREMENTAL)
        )
        mockBollingerBandService.calculateBollingerBands(request) >> mockResponse

        when: "calling the Bollinger Band calculation endpoint"
        def response = controller.calculateBollingerBands(request)

        then: "should return successful response"
        response.statusCode.is2xxSuccessful()
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("completed successfully")
    }

    def "should handle Bollinger Band calculation with specific symbols"() {
        given: "a Bollinger Band request with specific symbols"
        def request = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        request.symbols = ["AAPL", "MSFT", "GOOGL"]

        and: "mock service returns response for specific symbols"
        def mockResponse = new BollingerBandResponse(
            "success", 3, 150, [], [], [], 800L, [], false,
            new BollingerBandResponse.CalculationParameters(20, 2.0, 20, BollingerBandRequest.CalculationMode.INCREMENTAL)
        )
        mockBollingerBandService.calculateBollingerBands(_) >> mockResponse

        when: "calling the Bollinger Band calculation endpoint with specific symbols"
        def response = controller.calculateBollingerBands(request)

        then: "should return successful response for specific symbols"
        response.statusCode.is2xxSuccessful()
        response.body.success == true
        response.body.data.processedSymbols == 3
        response.body.message.contains("completed successfully")
    }

    def "should handle Bollinger Band calculation with pagination parameters"() {
        given: "a Bollinger Band request with pagination"
        def request = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        request.startIndex = 10
        request.endIndex = 20
        request.maxSymbols = 5

        and: "mock service returns paginated response"
        def mockResponse = new BollingerBandResponse(
            "success", 5, 250, [], [], [], 600L, [], false,
            new BollingerBandResponse.CalculationParameters(20, 2.0, 20, BollingerBandRequest.CalculationMode.INCREMENTAL)
        )
        mockBollingerBandService.calculateBollingerBands(_) >> mockResponse

        when: "calling the Bollinger Band calculation endpoint with pagination"
        def response = controller.calculateBollingerBands(request)

        then: "should return successful paginated response"
        response.statusCode.is2xxSuccessful()
        response.body.success == true
        response.body.data.processedSymbols == 5
    }

    def "should prioritize symbols over pagination when both provided"() {
        given: "a Bollinger Band request with both symbols and pagination parameters"
        def request = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        request.symbols = ["AAPL", "MSFT"]
        request.startIndex = 10
        request.endIndex = 20
        request.maxSymbols = 5

        and: "mock service returns response for specific symbols (ignoring pagination)"
        def mockResponse = new BollingerBandResponse(
            "success", 2, 100, [], [], [], 400L, [], false,
            new BollingerBandResponse.CalculationParameters(20, 2.0, 20, BollingerBandRequest.CalculationMode.INCREMENTAL)
        )
        mockBollingerBandService.calculateBollingerBands(_) >> mockResponse

        when: "calling the Bollinger Band calculation endpoint"
        def response = controller.calculateBollingerBands(request)

        then: "should process specific symbols and ignore pagination"
        response.statusCode.is2xxSuccessful()
        response.body.data.processedSymbols == 2
    }

    def "should handle Bollinger Band calculation with dry run"() {
        given: "a dry run Bollinger Band request"
        def request = new BollingerBandRequest(20, 2.0, true, BollingerBandRequest.CalculationMode.INCREMENTAL)
        
        and: "mock service returns dry run response"
        def mockResponse = new BollingerBandResponse(
            "success", 5, 250, [], [], [], 500L, [], true,
            new BollingerBandResponse.CalculationParameters(20, 2.0, 20, BollingerBandRequest.CalculationMode.INCREMENTAL)
        )
        mockBollingerBandService.calculateBollingerBands(request) >> mockResponse

        when: "calling the Bollinger Band calculation endpoint"
        def response = controller.calculateBollingerBands(request)

        then: "should return successful dry run response"
        response.statusCode.is2xxSuccessful()
        response.body.success == true
        response.body.data.dryRun == true
        response.body.message.contains("validation completed successfully")
    }

    def "should handle Bollinger Band calculation service errors"() {
        given: "a Bollinger Band request"
        def request = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        
        and: "mock service throws exception"
        mockBollingerBandService.calculateBollingerBands(request) >> { throw new RuntimeException("Database connection failed") }

        when: "calling the Bollinger Band calculation endpoint"
        def response = controller.calculateBollingerBands(request)

        then: "should return error response"
        response.statusCode.is5xxServerError()
        response.body.success == false
        response.body.message.contains("Failed to calculate Bollinger Bands")
        response.body.message.contains("Database connection failed")
    }

    def "should handle empty symbols list"() {
        given: "a Bollinger Band request with empty symbols list"
        def request = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        request.symbols = []

        and: "mock service returns response for empty processing"
        def mockResponse = new BollingerBandResponse(
            "success", 0, 0, [], [], [], 100L, [], false,
            new BollingerBandResponse.CalculationParameters(20, 2.0, 20, BollingerBandRequest.CalculationMode.INCREMENTAL)
        )
        mockBollingerBandService.calculateBollingerBands(_) >> mockResponse

        when: "calling the Bollinger Band calculation endpoint with empty symbols"
        def response = controller.calculateBollingerBands(request)

        then: "should return successful response with no processing"
        response.statusCode.is2xxSuccessful()
        response.body.data.processedSymbols == 0
    }

    def "should handle custom parameters"() {
        given: "a request with custom parameters"
        def request = new BollingerBandRequest(30, 2.5, false, BollingerBandRequest.CalculationMode.FULL_RECALCULATION)
        request.maxSymbols = 50
        request.minDataPoints = 35
        
        and: "mock service returns response with custom parameters"
        def mockResponse = new BollingerBandResponse(
            "success", 50, 2500, [], [], [], 2000L, [], false,
            new BollingerBandResponse.CalculationParameters(30, 2.5, 35, BollingerBandRequest.CalculationMode.FULL_RECALCULATION)
        )
        mockBollingerBandService.calculateBollingerBands(request) >> mockResponse

        when: "calling the Bollinger Band calculation endpoint"
        def response = controller.calculateBollingerBands(request)

        then: "should return response with custom parameters"
        response.statusCode.is2xxSuccessful()
        response.body.data.parameters.period == 30
        response.body.data.parameters.stdDevMultiplier == 2.5
        response.body.data.parameters.minDataPoints == 35
        response.body.data.processedSymbols == 50
    }
}
