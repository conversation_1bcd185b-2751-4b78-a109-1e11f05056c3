package com.investment.api.controller

import com.investment.api.model.RefreshAllRequest
import com.investment.api.model.RefreshAllResponse
import com.investment.api.model.UpdateRequest
import com.investment.model.OHLCV
import com.investment.service.OHLCVService
import org.springframework.http.HttpStatus
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Spock specification for testing the OHLCVController.
 */
class OHLCVControllerSpec extends Specification {

    @Subject
    OHLCVController controller

    OHLCVService mockService

    def setup() {
        mockService = Mock(OHLCVService)
        controller = new OHLCVController(mockService)
    }

    def "should return OHLCV data for a valid symbol"() {
        given: "a symbol and date range"
        String symbol = "AAPL"
        LocalDate startDate = LocalDate.now().minusDays(10)
        LocalDate endDate = LocalDate.now()

        and: "the service returns some data"
        List<OHLCV> serviceResponse = [
            new OHLCV(symbol, LocalDate.now().minusDays(2), 150.0, 155.0, 148.0, 152.0, 1000000),
            new OHLCV(symbol, LocalDate.now().minusDays(1), 152.0, 158.0, 151.0, 157.0, 1200000)
        ]
        mockService.getOHLCVData(symbol, startDate, endDate) >> serviceResponse

        when: "the controller method is called"
        def response = controller.getOHLCVData(symbol, startDate, endDate)

        then: "the response should be successful"
        response.statusCode == HttpStatus.OK
        response.body.success
        response.body.data.size() == 2
        response.body.data[0].symbol == symbol
        response.body.data[0].open == 150.0
    }

    def "should return all available data when no date parameters provided"() {
        given: "a symbol without date parameters"
        String symbol = "AAPL"

        and: "the service returns all available data"
        List<OHLCV> serviceResponse = [
            new OHLCV(symbol, LocalDate.now().minusDays(365), 100.0, 105.0, 98.0, 102.0, 800000),
            new OHLCV(symbol, LocalDate.now().minusDays(2), 150.0, 155.0, 148.0, 152.0, 1000000),
            new OHLCV(symbol, LocalDate.now().minusDays(1), 152.0, 158.0, 151.0, 157.0, 1200000)
        ]
        mockService.getOHLCVData(symbol, null, null) >> serviceResponse

        when: "the controller method is called without date parameters"
        def response = controller.getOHLCVData(symbol, null, null)

        then: "the response should return all available data"
        response.statusCode == HttpStatus.OK
        response.body.success
        response.body.data.size() == 3
        response.body.data[0].symbol == symbol
        // Verify the response contains OHLCVResponse objects with technical indicators
        response.body.data[0] instanceof com.investment.api.model.OHLCVResponse
    }

    def "should return 404 when no data is found"() {
        given: "a symbol with no data"
        String symbol = "UNKNOWN"
        LocalDate startDate = LocalDate.now().minusDays(10)
        LocalDate endDate = LocalDate.now()

        and: "the service returns empty list"
        mockService.getOHLCVData(symbol, startDate, endDate) >> []

        when: "the controller method is called"
        def response = controller.getOHLCVData(symbol, startDate, endDate)

        then: "the response should be not found"
        response.statusCode == HttpStatus.NOT_FOUND
        !response.body.success
        response.body.message.contains(symbol)
    }

    def "should update OHLCV data for multiple symbols"() {
        given: "an update request with multiple symbols"
        UpdateRequest request = new UpdateRequest(["AAPL", "MSFT", "GOOG"])

        and: "the service updates some symbols"
        mockService.updateOHLCVDataBatch(request.symbols) >> 2

        when: "the controller method is called"
        def response = controller.updateOHLCVData(request)

        then: "the response should be successful"
        response.statusCode == HttpStatus.OK
        response.body.success
        response.body.data.updatedCount == 2
        response.body.data.message.contains("2 out of 3")
    }

    def "should update OHLCV data for a single symbol"() {
        given: "a symbol to update"
        String symbol = "AAPL"

        and: "the service updates the data"
        mockService.updateOHLCVData(symbol, null, null) >> 5

        when: "the controller method is called"
        def response = controller.updateSymbolData(symbol)

        then: "the response should be successful"
        response.statusCode == HttpStatus.OK
        response.body.success
        response.body.data.updatedCount == 5
        response.body.data.message.contains("5 data points")
    }

    def "should refresh all OHLCV data in dry run mode"() {
        given: "a refresh all request in dry run mode"
        def request = new RefreshAllRequest(true, 100, false)
        def mockResponse = new RefreshAllResponse(
                50, 10, 5, 10, 0, 0,
                ["AAPL", "MSFT"], ["GOOGL"], [], true, 0, 100)
        mockService.refreshAllOHLCVData(true, 100, false, 0, null) >> mockResponse

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success
        response.body.data == mockResponse
        response.body.message.contains("OHLCV refresh validation completed")
    }

    def "should refresh all OHLCV data with actual updates"() {
        given: "a refresh all request with dry run false"
        def request = new RefreshAllRequest(false, 50, true)
        def mockResponse = new RefreshAllResponse(
                100, 25, 10, 20, 5, 150,
                ["AAPL", "MSFT", "GOOGL"], ["TSLA"], ["INVALID"], false, 0, 50)
        mockService.refreshAllOHLCVData(false, 50, true, 0, null) >> mockResponse

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success
        response.body.data == mockResponse
        response.body.message.contains("OHLCV refresh completed")
        response.body.data.successfulUpdates == 20
        response.body.data.failedUpdates == 5
        response.body.data.totalDataPointsUpdated == 150
    }

    def "should validate maxSymbols parameter"() {
        given: "a request with invalid maxSymbols"
        def request = new RefreshAllRequest(true, 0, false)

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("maxSymbols must be greater than 0")
    }

    def "should validate maxSymbols upper limit"() {
        given: "a request with maxSymbols exceeding limit"
        def request = new RefreshAllRequest(true, 1500, false)

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("maxSymbols cannot exceed 1000")
    }

    def "should handle refresh all service errors"() {
        given: "a valid request that causes service error"
        def request = new RefreshAllRequest(true, 100, false)
        mockService.refreshAllOHLCVData(true, 100, false, 0, null) >> { throw new RuntimeException("Service error") }

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should return error response"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("OHLCV refresh failed")
        response.body.message.contains("Service error")
    }

    def "should use default values in refresh all request"() {
        given: "a refresh request with default values"
        def request = new RefreshAllRequest()
        def mockResponse = new RefreshAllResponse(
                10, 5, 2, 5, 0, 25,
                ["AAPL"], ["MSFT"], [], true, 0, 100)

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should use default values"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        // Verify defaults: dryRun=true, maxSymbols=100, skipExisting=false, startIndex=0, endIndex=null
        1 * mockService.refreshAllOHLCVData(true, 100, false, 0, null) >> mockResponse
    }

    def "should handle skipExisting parameter correctly"() {
        given: "a refresh request with skipExisting enabled"
        def request = new RefreshAllRequest(true, 50, true)
        def mockResponse = new RefreshAllResponse(
                20, 5, 10, 5, 0, 0,
                ["AAPL"], ["MSFT", "GOOGL"], [], true, 0, 50)
        mockService.refreshAllOHLCVData(true, 50, true, 0, null) >> mockResponse

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should pass skipExisting parameter to service"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data.skippedSymbols == 10
        response.body.data.skippedSymbolsList == ["MSFT", "GOOGL"]
    }

    def "should validate startIndex parameter"() {
        given: "a request with negative startIndex"
        def request = new RefreshAllRequest(true, 100, false, -1, null)

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("startIndex must be >= 0")
    }

    def "should validate endIndex parameter"() {
        given: "a request with endIndex <= startIndex"
        def request = new RefreshAllRequest(true, 100, false, 10, 5)

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("endIndex must be greater than startIndex")
    }

    def "should handle pagination parameters correctly"() {
        given: "a refresh request with pagination"
        def request = new RefreshAllRequest(true, 100, false, 1000, 1500)
        def mockResponse = new RefreshAllResponse(
                5000, 500, 50, 450, 0, 0,
                [], [], [], true, 1000, 1500)
        mockService.refreshAllOHLCVData(true, 100, false, 1000, 1500) >> mockResponse

        when: "calling refresh all endpoint"
        def response = controller.refreshAllOHLCVData(request)

        then: "should pass pagination parameters to service"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data.startIndex == 1000
        response.body.data.endIndex == 1500
        response.body.data.summary.contains("(range: 1000-1499)")
    }
}
