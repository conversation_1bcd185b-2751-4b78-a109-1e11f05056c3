package com.investment.api.controller

import com.investment.api.model.CreateWatchListRequest
import com.investment.api.model.ReorderWatchListRequest
import com.investment.api.model.UpdateWatchListRequest
import com.investment.model.WatchListItem
import com.investment.service.WatchListService
import spock.lang.Specification

import java.math.BigDecimal
import java.sql.SQLException
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test specification for WatchListController.
 */
class WatchListControllerSpec extends Specification {

    WatchListController watchListController
    WatchListService watchListService

    def setup() {
        watchListService = Mock(WatchListService)
        watchListController = new WatchListController(watchListService)
    }

    def "should create watch list item successfully"() {
        given: "a valid create watch list request"
        def request = new CreateWatchListRequest(1, "AAPL", LocalDate.of(2024, 1, 15), "Strong growth potential")

        and: "service creates item successfully"
        def createdItem = new WatchListItem(1, "AAPL", LocalDate.of(2024, 1, 15), "Strong growth potential")
        createdItem.setId(1L)
        
        watchListService.createWatchListItem(request) >> createdItem

        when: "calling create watch list item endpoint"
        def response = watchListController.createWatchListItem(request)

        then: "should return successful response"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.symbol == "AAPL"
        response.body.data.displayIndex == 1
        response.body.data.startDate == LocalDate.of(2024, 1, 15)
        response.body.data.remarks == "Strong growth potential"
        response.body.message == "Watch list item created successfully"
    }

    def "should handle create watch list item validation error"() {
        given: "service throws IllegalArgumentException"
        def request = new CreateWatchListRequest(1, "INVALID", LocalDate.now())
        watchListService.createWatchListItem(request) >> { throw new IllegalArgumentException("Symbol not found") }

        when: "calling create watch list item endpoint"
        def response = watchListController.createWatchListItem(request)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Symbol not found")
    }

    def "should handle create watch list item database error"() {
        given: "service throws SQLException"
        def request = new CreateWatchListRequest(1, "AAPL", LocalDate.now())
        watchListService.createWatchListItem(request) >> { throw new SQLException("Database error") }

        when: "calling create watch list item endpoint"
        def response = watchListController.createWatchListItem(request)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to create watch list item: Database error")
    }

    def "should get all watch list items successfully"() {
        given: "service returns watch list items"
        def item1 = createSampleWatchListItem(1L, "AAPL", 1)
        def item2 = createSampleWatchListItem(2L, "MSFT", 2)
        watchListService.getAllWatchListItems() >> [item1, item2]

        when: "calling get all watch list items endpoint"
        def response = watchListController.getAllWatchListItems()

        then: "should return successful response"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.size() == 2
        response.body.data[0].symbol == "AAPL"
        response.body.data[1].symbol == "MSFT"
        response.body.message == "Retrieved 2 watch list items"
    }

    def "should get watch list item by ID successfully"() {
        given: "service returns watch list item"
        def item = createSampleWatchListItem(1L, "AAPL", 1)
        watchListService.getWatchListItemById(1L) >> Optional.of(item)

        when: "calling get watch list item endpoint"
        def response = watchListController.getWatchListItem(1L)

        then: "should return watch list item"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.symbol == "AAPL"
        response.body.message == "Watch list item found"
    }

    def "should return 404 for non-existent watch list item"() {
        given: "service returns empty optional"
        watchListService.getWatchListItemById(999L) >> Optional.empty()

        when: "calling get watch list item endpoint"
        def response = watchListController.getWatchListItem(999L)

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Watch list item not found: 999")
    }

    def "should update watch list item successfully"() {
        given: "a valid update request"
        def updateRequest = new UpdateWatchListRequest()
        updateRequest.setDisplayIndex(5)
        updateRequest.setRemarks("Updated analysis")

        and: "service updates item successfully"
        def updatedItem = createSampleWatchListItem(1L, "AAPL", 5)
        updatedItem.setRemarks("Updated analysis")
        watchListService.updateWatchListItem(1L, updateRequest) >> updatedItem

        when: "calling update watch list item endpoint"
        def response = watchListController.updateWatchListItem(1L, updateRequest)

        then: "should return updated item"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.displayIndex == 5
        response.body.data.remarks == "Updated analysis"
        response.body.message == "Watch list item updated successfully"
    }

    def "should handle update with no fields provided"() {
        given: "an empty update request"
        def updateRequest = new UpdateWatchListRequest()

        when: "calling update watch list item endpoint"
        def response = watchListController.updateWatchListItem(1L, updateRequest)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message == "No update fields provided"
    }

    def "should update performance metrics successfully"() {
        given: "service updates performance successfully"
        def updatedItem = createSampleWatchListItem(1L, "AAPL", 1)
        updatedItem.setOneMonthPerf(new BigDecimal("0.05"))
        updatedItem.setThreeMonthPerf(new BigDecimal("0.12"))
        watchListService.updateWatchListPerformance(1L, new BigDecimal("0.05"), new BigDecimal("0.12"), null) >> updatedItem

        when: "calling update performance endpoint"
        def response = watchListController.updateWatchListPerformance(1L, new BigDecimal("0.05"), new BigDecimal("0.12"), null)

        then: "should return updated item"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.oneMonthPerf == new BigDecimal("0.05")
        response.body.data.threeMonthPerf == new BigDecimal("0.12")
        response.body.message == "Performance metrics updated successfully"
    }

    def "should delete watch list item successfully"() {
        given: "service deletes item successfully"
        watchListService.deleteWatchListItem(1L) >> true

        when: "calling delete watch list item endpoint"
        def response = watchListController.deleteWatchListItem(1L)

        then: "should return success"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data == "Watch list item 1 deleted"
        response.body.message == "Watch list item deleted successfully"
    }

    def "should handle delete non-existent watch list item"() {
        given: "service returns false for deletion"
        watchListService.deleteWatchListItem(999L) >> false

        when: "calling delete watch list item endpoint"
        def response = watchListController.deleteWatchListItem(999L)

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Watch list item not found: 999")
    }

    def "should reorder watch list items successfully"() {
        given: "a valid reorder request"
        def idToIndexMap = [1L: 0, 2L: 1, 3L: 2]
        def request = new ReorderWatchListRequest(idToIndexMap)

        when: "calling reorder watch list items endpoint"
        def response = watchListController.reorderWatchListItems(request)

        then: "should return success"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data == "Reordered 3 items"
        response.body.message == "Watch list items reordered successfully"

        and: "service should be called"
        1 * watchListService.reorderWatchListItems(idToIndexMap)
    }

    def "should handle invalid reorder request"() {
        given: "an invalid reorder request with duplicate indexes"
        def idToIndexMap = [1L: 0, 2L: 0] // Duplicate index
        def request = new ReorderWatchListRequest(idToIndexMap)

        when: "calling reorder watch list items endpoint"
        def response = watchListController.reorderWatchListItems(request)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Duplicate display indexes are not allowed")
    }

    def "should handle reorder with non-existent item"() {
        given: "a reorder request"
        def idToIndexMap = [999L: 0]
        def request = new ReorderWatchListRequest(idToIndexMap)
        watchListService.reorderWatchListItems(idToIndexMap) >> { throw new IllegalArgumentException("Watch list item not found: 999") }

        when: "calling reorder watch list items endpoint"
        def response = watchListController.reorderWatchListItems(request)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Watch list item not found: 999")
    }

    def "should handle database errors gracefully"() {
        given: "service throws SQLException"
        watchListService.getAllWatchListItems() >> { throw new SQLException("Database connection failed") }

        when: "calling get all watch list items endpoint"
        def response = watchListController.getAllWatchListItems()

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to retrieve watch list items: Database connection failed")
    }

    def "should handle update watch list item not found"() {
        given: "service throws IllegalArgumentException for non-existent item"
        def updateRequest = new UpdateWatchListRequest()
        updateRequest.setDisplayIndex(5)
        watchListService.updateWatchListItem(999L, updateRequest) >> { throw new IllegalArgumentException("Watch list item not found: 999") }

        when: "calling update watch list item endpoint"
        def response = watchListController.updateWatchListItem(999L, updateRequest)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Watch list item not found: 999")
    }

    def "should handle performance update for non-existent item"() {
        given: "service throws IllegalArgumentException for non-existent item"
        watchListService.updateWatchListPerformance(999L, _, _, _) >> { throw new IllegalArgumentException("Watch list item not found: 999") }

        when: "calling update performance endpoint"
        def response = watchListController.updateWatchListPerformance(999L, new BigDecimal("0.05"), null, null)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Watch list item not found: 999")
    }

    def "should handle database error in get watch list item by ID"() {
        given: "service throws SQLException"
        watchListService.getWatchListItemById(1L) >> { throw new SQLException("Database error") }

        when: "calling get watch list item endpoint"
        def response = watchListController.getWatchListItem(1L)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to retrieve watch list item: Database error")
    }

    def "should handle database error in update watch list item"() {
        given: "service throws SQLException"
        def updateRequest = new UpdateWatchListRequest()
        updateRequest.setDisplayIndex(5)
        watchListService.updateWatchListItem(1L, updateRequest) >> { throw new SQLException("Database error") }

        when: "calling update watch list item endpoint"
        def response = watchListController.updateWatchListItem(1L, updateRequest)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to update watch list item: Database error")
    }

    def "should handle database error in delete watch list item"() {
        given: "service throws SQLException"
        watchListService.deleteWatchListItem(1L) >> { throw new SQLException("Database error") }

        when: "calling delete watch list item endpoint"
        def response = watchListController.deleteWatchListItem(1L)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to delete watch list item: Database error")
    }

    private WatchListItem createSampleWatchListItem(Long id, String symbol, Integer displayIndex) {
        def item = new WatchListItem(displayIndex, symbol, LocalDate.of(2024, 1, 15))
        item.setId(id)
        item.setCreatedDate(LocalDateTime.now())
        item.setUpdatedDate(LocalDateTime.now())
        return item
    }
}
