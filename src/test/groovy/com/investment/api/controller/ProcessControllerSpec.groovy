package com.investment.api.controller

import com.investment.process.*
import spock.lang.Specification

/**
 * Test specification for ProcessController.
 */
class ProcessControllerSpec extends Specification {

    ProcessController processController
    ProcessManager processManager

    def setup() {
        processManager = new ProcessManager()
        processController = new ProcessController(processManager)
    }

    def "should abort all active processes"() {
        given: "multiple active processes"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "Process 3", "user3")

        and: "one process is already completed"
        context3.markCompleted()

        when: "calling abort all endpoint"
        def response = processController.abortAllProcesses()

        then: "should return successful response"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.totalActiveProcesses == 2
        response.body.data.abortRequestsSent == 2
        response.body.data.fullSuccess

        and: "active processes should be marked for cancellation"
        context1.isCancellationRequested()
        context2.isCancellationRequested()
        !context3.isCancellationRequested() // Already completed
    }

    def "should get all processes"() {
        given: "processes with different statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        context2.markCompleted()

        when: "calling get all processes endpoint"
        def response = processController.getAllProcesses(null, null)

        then: "should return all processes"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.size() == 2
        response.body.message.contains("Retrieved 2 processes")
    }

    def "should filter processes by status"() {
        given: "processes with different statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "Process 3", "user3")
        
        context2.markCompleted()
        context3.markFailed("Test error")

        when: "filtering by running status"
        def runningResponse = processController.getAllProcesses(ProcessStatus.RUNNING, null)

        then: "should return only running processes"
        runningResponse.statusCode.is2xxSuccessful()
        runningResponse.body.data.size() == 1
        runningResponse.body.data[0].processId == context1.processInfo.processId

        when: "filtering by completed status"
        def completedResponse = processController.getAllProcesses(ProcessStatus.COMPLETED, null)

        then: "should return only completed processes"
        completedResponse.statusCode.is2xxSuccessful()
        completedResponse.body.data.size() == 1
        completedResponse.body.data[0].processId == context2.processInfo.processId
    }

    def "should filter processes by type"() {
        given: "processes of different types"
        def dmiContext = processManager.registerProcess(ProcessType.DMI_CALCULATION, "DMI process", "user1")
        def ohlcvContext = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "OHLCV process", "user2")
        def secContext = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "SEC process", "user3")

        when: "filtering by DMI type"
        def dmiResponse = processController.getAllProcesses(null, ProcessType.DMI_CALCULATION)

        then: "should return only DMI processes"
        dmiResponse.statusCode.is2xxSuccessful()
        dmiResponse.body.data.size() == 1
        dmiResponse.body.data[0].processType == ProcessType.DMI_CALCULATION
        dmiResponse.body.data[0].processId == dmiContext.processInfo.processId
    }

    def "should get active processes only"() {
        given: "processes with different statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "Process 3", "user3")
        
        context2.markCompleted()
        context3.markFailed("Test error")

        when: "calling get active processes endpoint"
        def response = processController.getActiveProcesses()

        then: "should return only active processes"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.size() == 1
        response.body.data[0].processId == context1.processInfo.processId
        response.body.data[0].running
        !response.body.data[0].terminal
    }

    def "should get specific process by ID"() {
        given: "a registered process"
        def context = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Test process", "user1")
        def processId = context.processInfo.processId

        when: "calling get process endpoint"
        def response = processController.getProcess(processId)

        then: "should return the specific process"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.processId == processId
        response.body.data.processType == ProcessType.DMI_CALCULATION
        response.body.data.description == "Test process"
        response.body.data.initiatedBy == "user1"
    }

    def "should return 404 for non-existent process"() {
        when: "calling get process with non-existent ID"
        def response = processController.getProcess("non-existent-id")

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Process not found")
    }

    def "should abort specific process"() {
        given: "a running process"
        def context = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Test process", "user1")
        def processId = context.processInfo.processId

        when: "calling abort process endpoint"
        def response = processController.abortProcess(processId)

        then: "should abort the process"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.processId == processId
        response.body.data.status == ProcessStatus.ABORTING
        context.isCancellationRequested()
    }

    def "should return 404 when aborting non-existent process"() {
        when: "calling abort with non-existent ID"
        def response = processController.abortProcess("non-existent-id")

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Process not found or already terminated")
    }

    def "should get process statistics"() {
        given: "processes with different types and statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "DMI 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "DMI 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "OHLCV 1", "user3")
        def context4 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "SEC 1", "user4")

        and: "different statuses"
        context2.markCompleted()
        context4.markFailed("Test error")

        when: "calling get statistics endpoint"
        def response = processController.getStatistics()

        then: "should return correct statistics"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.totalProcesses == 4
        response.body.data.activeProcesses == 2
        response.body.data.statusCounts[ProcessStatus.RUNNING] == 2
        response.body.data.statusCounts[ProcessStatus.COMPLETED] == 1
        response.body.data.statusCounts[ProcessStatus.FAILED] == 1
        response.body.data.typeCounts[ProcessType.DMI_CALCULATION] == 2
        response.body.data.typeCounts[ProcessType.OHLCV_REFRESH] == 1
        response.body.data.typeCounts[ProcessType.SEC_SYNCHRONIZATION] == 1
    }

    def "should cleanup completed processes"() {
        given: "processes with different statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "Process 3", "user3")

        and: "some processes are completed"
        context2.markCompleted()
        context3.markFailed("Test error")

        when: "calling cleanup endpoint"
        def response = processController.cleanupProcesses()

        then: "should cleanup completed processes"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.contains("Cleaned up 2 completed processes")

        and: "only running processes should remain"
        processManager.getAllProcesses().size() == 1
        processManager.getAllProcesses()[0].processId == context1.processInfo.processId
    }

    def "should handle empty process list gracefully"() {
        when: "calling abort all with no processes"
        def abortResponse = processController.abortAllProcesses()

        then: "should return successful response"
        abortResponse.statusCode.is2xxSuccessful()
        abortResponse.body.success
        abortResponse.body.data.totalActiveProcesses == 0
        abortResponse.body.data.abortRequestsSent == 0

        when: "calling get all processes with no processes"
        def getAllResponse = processController.getAllProcesses(null, null)

        then: "should return empty list"
        getAllResponse.statusCode.is2xxSuccessful()
        getAllResponse.body.success
        getAllResponse.body.data.size() == 0

        when: "calling cleanup with no processes"
        def cleanupResponse = processController.cleanupProcesses()

        then: "should return successful response"
        cleanupResponse.statusCode.is2xxSuccessful()
        cleanupResponse.body.success
        cleanupResponse.body.data.contains("Cleaned up 0 completed processes")
    }
}
