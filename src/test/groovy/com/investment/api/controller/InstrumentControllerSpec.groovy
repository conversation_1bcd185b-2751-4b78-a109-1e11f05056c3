package com.investment.api.controller

import com.investment.api.model.CreateInstrumentRequest
import com.investment.api.model.CsvUploadResponse
import com.investment.api.model.InstrumentResponse
import com.investment.api.model.PaginatedResponse
import com.investment.api.model.SyncRequest
import com.investment.api.model.SyncResponse
import com.investment.api.model.ValidationRequest
import com.investment.api.model.ValidationResponse
import com.investment.database.DatabaseManager
import com.investment.model.Instrument
import com.investment.model.InstrumentType
import com.investment.service.CsvInstrumentService
import com.investment.service.InstrumentService
import com.investment.service.SecSynchronizationService
import com.investment.service.SymbolValidationService
import org.springframework.http.HttpStatus
import org.springframework.mock.web.MockMultipartFile
import spock.lang.Specification
import spock.lang.Subject

import java.math.BigDecimal
import java.sql.SQLException

/**
 * Spock specification for testing the InstrumentController.
 */
class InstrumentControllerSpec extends Specification {

    @Subject
    InstrumentController controller

    SymbolValidationService mockValidationService
    SecSynchronizationService mockSyncService
    CsvInstrumentService mockCsvService
    InstrumentService mockInstrumentService
    DatabaseManager mockDatabaseManager

    def setup() {
        mockValidationService = Mock(SymbolValidationService)
        mockSyncService = Mock(SecSynchronizationService)
        mockCsvService = Mock(CsvInstrumentService)
        mockInstrumentService = Mock(InstrumentService)
        mockDatabaseManager = Mock(DatabaseManager)
        controller = new InstrumentController(mockValidationService, mockSyncService, mockCsvService, mockInstrumentService, mockDatabaseManager)
    }

    def "should perform dry run validation successfully"() {
        given: "a mock validation response"
        def mockResponse = new ValidationResponse(10, 8, 2, ["INVALID1", "INVALID2"], 0, 15, true)
        mockValidationService.validateSymbols(true, false) >> mockResponse

        when: "calling dry run validation endpoint"
        def response = controller.validateSymbolsDryRun(false)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("validation completed")
    }

    def "should perform dry run validation with force refresh"() {
        given: "a mock validation response"
        def mockResponse = new ValidationResponse(5, 5, 0, [], 0, 0, true)
        mockValidationService.validateSymbols(true, true) >> mockResponse

        when: "calling dry run validation with force refresh"
        def response = controller.validateSymbolsDryRun(true)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
    }

    def "should handle validation service errors in dry run"() {
        given: "validation service throws an exception"
        mockValidationService.validateSymbols(true, false) >> { throw new RuntimeException("Service error") }

        when: "calling dry run validation endpoint"
        def response = controller.validateSymbolsDryRun(false)

        then: "should return error response"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("Symbol validation failed")
    }

    def "should perform actual cleanup when dry run is false"() {
        given: "a validation request for actual cleanup"
        def request = new ValidationRequest(false, false)
        def mockResponse = new ValidationResponse(10, 8, 2, ["INVALID1", "INVALID2"], 2, 15, false)
        mockValidationService.validateSymbols(false, false) >> mockResponse

        when: "calling cleanup endpoint"
        def response = controller.validateAndCleanupSymbols(request)

        then: "should return successful cleanup response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("cleanup completed")
    }

    def "should perform dry run when requested"() {
        given: "a validation request for dry run"
        def request = new ValidationRequest(true, false)
        def mockResponse = new ValidationResponse(10, 8, 2, ["INVALID1", "INVALID2"], 0, 15, true)
        mockValidationService.validateSymbols(true, false) >> mockResponse

        when: "calling validation endpoint with dry run"
        def response = controller.validateAndCleanupSymbols(request)

        then: "should return dry run response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("validation (dry-run) completed")
    }

    def "should handle null request body"() {
        when: "calling validation endpoint with null request"
        def response = controller.validateAndCleanupSymbols(null)

        then: "should return bad request error"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message == "Request body is required"
    }

    def "should get cache status successfully"() {
        given: "mock cache status"
        def mockStatus = [
            cacheDirectory: "./data/sec_cache",
            cacheFile: "company_tickers.json",
            cachedTickersCount: 5000,
            cacheFileExists: true
        ]
        mockValidationService.getCacheStatus() >> mockStatus

        when: "calling cache status endpoint"
        def response = controller.getSecCacheStatus()

        then: "should return cache status"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockStatus
        response.body.message.contains("Cache status retrieved")
    }

    def "should handle cache status errors"() {
        given: "cache status throws an exception"
        mockValidationService.getCacheStatus() >> { throw new RuntimeException("Cache error") }

        when: "calling cache status endpoint"
        def response = controller.getSecCacheStatus()

        then: "should return error response"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("Failed to retrieve cache status")
    }

    def "should perform SEC synchronization dry run successfully"() {
        given: "a mock sync response"
        def mockResponse = new SyncResponse(5000, 100, 50, ["TSLA", "AMZN"], 0, true)
        mockSyncService.synchronizeInstruments(true, false, 1000) >> mockResponse

        when: "calling sync dry run endpoint"
        def response = controller.syncSecDataDryRun(false, 1000)

        then: "should return successful sync response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("synchronization analysis completed")
    }

    def "should perform SEC synchronization dry run with custom parameters"() {
        given: "a mock sync response with custom parameters"
        def mockResponse = new SyncResponse(5000, 200, 25, ["NVDA"], 0, true)
        mockSyncService.synchronizeInstruments(true, true, 500) >> mockResponse

        when: "calling sync dry run endpoint with custom parameters"
        def response = controller.syncSecDataDryRun(true, 500)

        then: "should return successful sync response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.data.totalSecSymbols == 5000
        response.body.data.missingFromDatabase == 25
    }

    def "should handle SEC synchronization dry run errors"() {
        given: "sync service throws exception"
        mockSyncService.synchronizeInstruments(true, false, 1000) >> { throw new RuntimeException("SEC API error") }

        when: "calling sync dry run endpoint"
        def response = controller.syncSecDataDryRun(false, 1000)

        then: "should return error response"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("SEC synchronization analysis failed")
        response.body.message.contains("SEC API error")
    }

    def "should perform actual SEC synchronization when dry run is false"() {
        given: "a sync request for actual synchronization"
        def request = new SyncRequest(false, false, 100)
        def mockResponse = new SyncResponse(5000, 100, 50, ["TSLA", "AMZN"], 2, false)
        mockSyncService.synchronizeInstruments(false, false, 100) >> mockResponse

        when: "calling sync endpoint"
        def response = controller.syncSecData(request)

        then: "should return successful sync response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("synchronization completed")
        response.body.data.addedSymbols == 2
    }

    def "should perform SEC synchronization dry run when dry run is true"() {
        given: "a sync request for dry run"
        def request = new SyncRequest(true, true, 200)
        def mockResponse = new SyncResponse(5000, 150, 30, ["NVDA", "AMD"], 0, true)
        mockSyncService.synchronizeInstruments(true, true, 200) >> mockResponse

        when: "calling sync endpoint"
        def response = controller.syncSecData(request)

        then: "should return successful dry run response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("synchronization (dry-run) completed")
        response.body.data.addedSymbols == 0
        response.body.data.dryRun == true
    }

    def "should handle null sync request"() {
        when: "calling sync endpoint with null request"
        def response = controller.syncSecData(null)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message == "Request body is required"
    }

    def "should handle SEC synchronization errors"() {
        given: "sync service throws exception"
        def request = new SyncRequest(true, false, 100)
        mockSyncService.synchronizeInstruments(true, false, 100) >> { throw new RuntimeException("Database error") }

        when: "calling sync endpoint"
        def response = controller.syncSecData(request)

        then: "should return error response"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("SEC synchronization failed")
        response.body.message.contains("Database error")
    }

    def "should use default values in sync request"() {
        given: "a sync request with default values"
        def request = new SyncRequest()
        def mockResponse = new SyncResponse(5000, 100, 50, [], 0, true)

        when: "calling sync endpoint"
        def response = controller.syncSecData(request)

        then: "should use default values"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        // Verify defaults: dryRun=true, forceRefresh=false, maxInstruments=1000
        1 * mockSyncService.synchronizeInstruments(true, false, 1000) >> mockResponse
    }

    def "should upload CSV file successfully in dry run mode"() {
        given: "a valid CSV file"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics
MSFT,Microsoft Corporation,300.00,2.00,0.67%,2.2T,United States,1986,30000000,Technology,Software"""
        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        def mockResponse = new CsvUploadResponse(2, 2, 0, 2, 0, 0, 0, [], ["AAPL", "MSFT"], true)
        mockCsvService.processCsvFile(file, true, 1000, true, true) >> mockResponse

        when: "uploading CSV file in dry run mode"
        def response = controller.uploadCsv(file, true, 1000, true, true)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("CSV validation completed")
    }

    def "should upload CSV file and save to database when dry run is false"() {
        given: "a valid CSV file"
        def csvContent = """Symbol,Name,Last Sale,Net Change,% Change,Market Cap,Country,IPO Year,Volume,Sector,Industry
AAPL,Apple Inc.,150.00,1.50,1.01%,2.5T,United States,1980,50000000,Technology,Consumer Electronics"""
        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", csvContent.bytes)
        def mockResponse = new CsvUploadResponse(1, 1, 0, 1, 0, 1, 0, [], ["AAPL"], false)
        mockCsvService.processCsvFile(file, false, 1000, true, true) >> mockResponse

        when: "uploading CSV file with dry run false"
        def response = controller.uploadCsv(file, false, 1000, true, true)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("CSV import completed")
        response.body.data.addedInstruments == 1
        response.body.data.dryRun == false
    }

    def "should handle empty CSV file upload"() {
        given: "an empty file"
        def file = new MockMultipartFile("file", "empty.csv", "text/csv", "".bytes)

        when: "uploading empty file"
        def response = controller.uploadCsv(file, true, 1000, true, true)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("File is required and cannot be empty")
    }

    def "should handle null file upload"() {
        when: "uploading null file"
        def response = controller.uploadCsv(null, true, 1000, true, true)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("File is required and cannot be empty")
    }

    def "should handle CSV processing errors"() {
        given: "a CSV file that causes processing error"
        def file = new MockMultipartFile("file", "invalid.csv", "text/csv", "invalid content".bytes)
        mockCsvService.processCsvFile(file, true, 1000, true, true) >> { throw new RuntimeException("Processing error") }

        when: "uploading CSV file"
        def response = controller.uploadCsv(file, true, 1000, true, true)

        then: "should return error response"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("CSV processing failed")
        response.body.message.contains("Processing error")
    }

    def "should use custom parameters for CSV upload"() {
        given: "a CSV file with custom parameters"
        def file = new MockMultipartFile("file", "instruments.csv", "text/csv", "Symbol,Name\nAAPL,Apple".bytes)
        def mockResponse = new CsvUploadResponse(1, 1, 0, 1, 0, 0, 1, [], ["AAPL"], false)

        when: "uploading with custom parameters"
        def response = controller.uploadCsv(file, false, 500, false, false)

        then: "should pass custom parameters to service"
        1 * mockCsvService.processCsvFile(file, false, 500, false, false) >> mockResponse
        response.statusCode == HttpStatus.OK
        response.body.success == true
    }

    // Tests for new paginated instruments endpoint
    def "should get instruments with pagination successfully"() {
        given: "mock instruments data"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software")
        ]
        mockDatabaseManager.getTotalInstrumentCount() >> 100
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(0, 50) >> instruments

        when: "calling get instruments endpoint"
        def response = controller.getInstruments(0, 50, "marketCap", "desc")

        then: "should return paginated instruments"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data instanceof PaginatedResponse
        response.body.data.content == instruments
        response.body.data.page == 0
        response.body.data.size == 50
        response.body.data.totalElements == 100
        response.body.data.totalPages == 2
        response.body.message.contains("Instruments retrieved successfully")
    }

    def "should validate pagination parameters"() {
        when: "calling with invalid page number"
        def response = controller.getInstruments(-1, 50, "marketCap", "desc")

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("Page number must be >= 0")
    }

    def "should validate page size parameters"() {
        when: "calling with invalid page size"
        def response = controller.getInstruments(0, 0, "marketCap", "desc")

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("Page size must be between 1 and 1000")
    }

    def "should validate sort parameters"() {
        when: "calling with invalid sort field"
        def response = controller.getInstruments(0, 50, "invalid", "desc")

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("sortBy must be one of: marketCap, symbol, name")
    }

    def "should validate page beyond available data"() {
        given: "total instruments count"
        mockDatabaseManager.getTotalInstrumentCount() >> 100

        when: "calling with page beyond available data"
        def response = controller.getInstruments(10, 50, "marketCap", "desc") // page 10 with size 50 = offset 500, but only 100 instruments

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("Page 10 is beyond available data")
    }

    def "should get instrument by symbol successfully"() {
        given: "mock instrument exists"
        def instrument = new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics")
        mockDatabaseManager.symbolExists("AAPL") >> true
        mockDatabaseManager.getAllInstruments() >> [instrument]

        when: "calling get instrument by symbol"
        def response = controller.getInstrumentBySymbol("AAPL")

        then: "should return the instrument"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == instrument
        response.body.message.contains("Instrument found")
    }

    def "should return not found for non-existent symbol"() {
        given: "symbol does not exist"
        mockDatabaseManager.symbolExists("INVALID") >> false

        when: "calling get instrument by symbol"
        def response = controller.getInstrumentBySymbol("INVALID")

        then: "should return not found"
        response.statusCode == HttpStatus.NOT_FOUND
        response.body.success == false
        response.body.message.contains("Instrument not found: INVALID")
    }

    def "should search instruments successfully"() {
        given: "mock instruments for search"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software")
        ]
        mockDatabaseManager.getAllInstruments() >> instruments

        when: "searching for instruments"
        def response = controller.searchInstruments("Apple")

        then: "should return matching instruments"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data.size() == 1
        response.body.data[0].symbol == "AAPL"
        response.body.message.contains("Found 1 instruments")
    }

    def "should handle empty search query"() {
        when: "searching with empty query"
        def response = controller.searchInstruments("")

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("Search query cannot be empty")
    }

    def "should get instrument statistics successfully"() {
        given: "mock statistics data"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, null, "US", 1986, "Technology", "Software")
        ]
        mockDatabaseManager.getTotalInstrumentCount() >> 2
        mockDatabaseManager.getAllInstruments() >> instruments

        when: "calling get statistics"
        def response = controller.getInstrumentStatistics()

        then: "should return statistics"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data.totalInstruments == 2
        response.body.data.instrumentsWithMarketCap == 1
        response.body.data.instrumentsWithoutMarketCap == 1
    }

    // Tests for create instrument functionality
    def "should create instrument successfully"() {
        given: "a valid create instrument request"
        def request = new CreateInstrumentRequest(
                symbol: "AAPL",
                name: "Apple Inc.",
                type: InstrumentType.US_STOCK,
                marketCap: new BigDecimal("2500000000000"),
                country: "United States",
                ipoYear: 1980,
                sector: "Technology",
                industry: "Consumer Electronics"
        )

        and: "service returns created instrument"
        def createdInstrument = new Instrument(
                "AAPL",
                "Apple Inc.",
                InstrumentType.US_STOCK,
                new BigDecimal("2500000000000"),
                "United States",
                1980,
                "Technology",
                "Consumer Electronics"
        )

        when: "creating the instrument"
        def response = controller.createInstrument(request)

        then: "should call instrument service"
        1 * mockInstrumentService.createInstrument(request) >> createdInstrument

        and: "should return 201 Created"
        response.statusCode == HttpStatus.CREATED
        response.body.success == true
        response.body.message == "Instrument created successfully"

        and: "should return instrument response"
        def instrumentResponse = response.body.data as InstrumentResponse
        instrumentResponse.symbol == "AAPL"
        instrumentResponse.name == "Apple Inc."
        instrumentResponse.type == InstrumentType.US_STOCK
        instrumentResponse.marketCap == new BigDecimal("2500000000000")
        instrumentResponse.country == "United States"
        instrumentResponse.ipoYear == 1980
        instrumentResponse.sector == "Technology"
        instrumentResponse.industry == "Consumer Electronics"
    }

    def "should return 409 when symbol already exists"() {
        given: "a request with existing symbol"
        def request = new CreateInstrumentRequest(
                symbol: "AAPL",
                name: "Apple Inc.",
                type: InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        def response = controller.createInstrument(request)

        then: "service throws IllegalArgumentException for existing symbol"
        1 * mockInstrumentService.createInstrument(request) >> {
            throw new IllegalArgumentException("Symbol already exists: AAPL")
        }

        and: "should return 409 Conflict"
        response.statusCode == HttpStatus.CONFLICT
        response.body.success == false
        response.body.message == "Symbol already exists: AAPL"
    }

    def "should return 400 for validation errors"() {
        given: "a request with validation errors"
        def request = new CreateInstrumentRequest(
                symbol: "INVALID@SYMBOL",
                name: "Test Company",
                type: InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        def response = controller.createInstrument(request)

        then: "service throws IllegalArgumentException for validation"
        1 * mockInstrumentService.createInstrument(request) >> {
            throw new IllegalArgumentException("Symbol must contain only uppercase letters, numbers, dots, and hyphens")
        }

        and: "should return 400 Bad Request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("Validation error")
    }

    def "should return 500 for database errors"() {
        given: "a valid request"
        def request = new CreateInstrumentRequest(
                symbol: "TEST",
                name: "Test Company",
                type: InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        def response = controller.createInstrument(request)

        then: "service throws SQLException"
        1 * mockInstrumentService.createInstrument(request) >> {
            throw new SQLException("Database connection failed")
        }

        and: "should return 500 Internal Server Error"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("Failed to create instrument")
    }

    def "should create instrument with minimal required fields"() {
        given: "a request with only required fields"
        def request = new CreateInstrumentRequest(
                symbol: "MSFT",
                name: "Microsoft Corporation",
                type: InstrumentType.US_STOCK
        )

        and: "service returns created instrument"
        def createdInstrument = new Instrument(
                "MSFT",
                "Microsoft Corporation",
                InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        def response = controller.createInstrument(request)

        then: "should call instrument service"
        1 * mockInstrumentService.createInstrument(request) >> createdInstrument

        and: "should return 201 Created"
        response.statusCode == HttpStatus.CREATED
        response.body.success == true

        and: "should return instrument response with null optional fields"
        def instrumentResponse = response.body.data as InstrumentResponse
        instrumentResponse.symbol == "MSFT"
        instrumentResponse.name == "Microsoft Corporation"
        instrumentResponse.type == InstrumentType.US_STOCK
        instrumentResponse.marketCap == null
        instrumentResponse.country == null
        instrumentResponse.ipoYear == null
        instrumentResponse.sector == null
        instrumentResponse.industry == null
    }
}
