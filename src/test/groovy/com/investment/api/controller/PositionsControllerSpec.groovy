package com.investment.api.controller

import com.investment.api.model.CreatePositionRequest
import com.investment.api.model.UpdatePositionRequest
import com.investment.model.Position
import com.investment.service.PositionsService
import spock.lang.Specification

import java.math.BigDecimal
import java.sql.SQLException
import java.time.LocalDateTime

/**
 * Test specification for PositionsController.
 */
class PositionsControllerSpec extends Specification {

    PositionsController positionsController
    PositionsService positionsService

    def setup() {
        positionsService = Mock(PositionsService)
        positionsController = new PositionsController(positionsService)
    }

    def "should create position successfully"() {
        given: "a valid create position request"
        def request = new CreatePositionRequest("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))
        request.setInitPortfolioNetValue(new BigDecimal("100000"))

        and: "service creates position successfully"
        def createdPosition = new Position("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))
        createdPosition.setId(1L)
        createdPosition.setInitPortfolioNetValue(new BigDecimal("100000"))
        
        positionsService.createPosition(request) >> createdPosition

        when: "calling create position endpoint"
        def response = positionsController.createPosition(request)

        then: "should return successful response"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.symbol == "AAPL"
        response.body.data.position == new BigDecimal("100")
        response.body.data.side == Position.Side.BUY
        response.body.data.tradePrice == new BigDecimal("150.25")
        response.body.message == "Position created successfully"
    }

    def "should handle create position validation error"() {
        given: "service throws IllegalArgumentException"
        def request = new CreatePositionRequest("INVALID", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))
        positionsService.createPosition(request) >> { throw new IllegalArgumentException("Symbol not found") }

        when: "calling create position endpoint"
        def response = positionsController.createPosition(request)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Symbol not found")
    }

    def "should handle create position database error"() {
        given: "service throws SQLException"
        def request = new CreatePositionRequest("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))
        positionsService.createPosition(request) >> { throw new SQLException("Database error") }

        when: "calling create position endpoint"
        def response = positionsController.createPosition(request)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to create position: Database error")
    }

    def "should get all positions successfully"() {
        given: "service returns positions"
        def position1 = createSamplePosition(1L, "AAPL")
        def position2 = createSamplePosition(2L, "MSFT")
        positionsService.getPositions(null, null, null) >> [position1, position2]

        when: "calling get all positions endpoint"
        def response = positionsController.getPositions(null, null, null)

        then: "should return successful response"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.size() == 2
        response.body.data[0].symbol == "AAPL"
        response.body.data[1].symbol == "MSFT"
        response.body.message == "Retrieved 2 positions"
    }

    def "should get positions with filters"() {
        given: "service returns filtered positions"
        def position = createSamplePosition(1L, "AAPL")
        positionsService.getPositions("AAPL", Position.Status.OPEN, Position.Side.BUY) >> [position]

        when: "calling get positions with filters"
        def response = positionsController.getPositions("AAPL", Position.Status.OPEN, Position.Side.BUY)

        then: "should return filtered positions"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.size() == 1
        response.body.data[0].symbol == "AAPL"
        response.body.message == "Retrieved 1 positions"
    }

    def "should get position by ID successfully"() {
        given: "service returns position"
        def position = createSamplePosition(1L, "AAPL")
        positionsService.getPositionById(1L) >> Optional.of(position)

        when: "calling get position endpoint"
        def response = positionsController.getPosition(1L)

        then: "should return position"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.symbol == "AAPL"
        response.body.message == "Position found"
    }

    def "should return 404 for non-existent position"() {
        given: "service returns empty optional"
        positionsService.getPositionById(999L) >> Optional.empty()

        when: "calling get position endpoint"
        def response = positionsController.getPosition(999L)

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Position not found: 999")
    }

    def "should update position successfully"() {
        given: "a valid update request"
        def updateRequest = new UpdatePositionRequest()
        updateRequest.setLastPrice(new BigDecimal("155.00"))
        updateRequest.setStatus(Position.Status.CLOSED)

        and: "service updates position successfully"
        def updatedPosition = createSamplePosition(1L, "AAPL")
        updatedPosition.setLastPrice(new BigDecimal("155.00"))
        updatedPosition.setStatus(Position.Status.CLOSED)
        positionsService.updatePosition(1L, updateRequest) >> updatedPosition

        when: "calling update position endpoint"
        def response = positionsController.updatePosition(1L, updateRequest)

        then: "should return updated position"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.lastPrice == new BigDecimal("155.00")
        response.body.data.status == Position.Status.CLOSED
        response.body.message == "Position updated successfully"
    }

    def "should handle update with no fields provided"() {
        given: "an empty update request"
        def updateRequest = new UpdatePositionRequest()

        when: "calling update position endpoint"
        def response = positionsController.updatePosition(1L, updateRequest)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message == "No update fields provided"
    }

    def "should update position price successfully"() {
        given: "service updates position price successfully"
        def updatedPosition = createSamplePosition(1L, "AAPL")
        updatedPosition.setLastPrice(new BigDecimal("155.00"))
        positionsService.updatePositionPrice(1L, new BigDecimal("155.00")) >> updatedPosition

        when: "calling update position price endpoint"
        def response = positionsController.updatePositionPrice(1L, new BigDecimal("155.00"))

        then: "should return updated position"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.lastPrice == new BigDecimal("155.00")
        response.body.message == "Position price updated successfully"
    }

    def "should handle invalid price in price update"() {
        when: "calling update position price with invalid price"
        def response = positionsController.updatePositionPrice(1L, new BigDecimal("-10.00"))

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message == "Price must be positive"
    }

    def "should close position successfully"() {
        given: "service closes position successfully"
        def closedPosition = createSamplePosition(1L, "AAPL")
        closedPosition.setStatus(Position.Status.CLOSED)
        positionsService.closePosition(1L) >> closedPosition

        when: "calling close position endpoint"
        def response = positionsController.closePosition(1L)

        then: "should return closed position"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.status == Position.Status.CLOSED
        response.body.message == "Position closed successfully"
    }

    def "should delete position successfully"() {
        given: "service deletes position successfully"
        positionsService.deletePosition(1L) >> true

        when: "calling delete position endpoint"
        def response = positionsController.deletePosition(1L)

        then: "should return success"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data == "Position 1 deleted"
        response.body.message == "Position deleted successfully"
    }

    def "should handle delete non-existent position"() {
        given: "service returns false for deletion"
        positionsService.deletePosition(999L) >> false

        when: "calling delete position endpoint"
        def response = positionsController.deletePosition(999L)

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Position not found: 999")
    }

    def "should get open positions successfully"() {
        given: "service returns open positions"
        def position1 = createSamplePosition(1L, "AAPL")
        def position2 = createSamplePosition(2L, "MSFT")
        positionsService.getOpenPositions() >> [position1, position2]

        when: "calling get open positions endpoint"
        def response = positionsController.getOpenPositions()

        then: "should return open positions"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.size() == 2
        response.body.message == "Retrieved 2 open positions"
    }

    def "should get positions to stop out successfully"() {
        given: "service returns positions to stop out"
        def position = createSamplePosition(1L, "AAPL")
        positionsService.getPositionsToStopOut() >> [position]

        when: "calling get positions to stop out endpoint"
        def response = positionsController.getPositionsToStopOut()

        then: "should return positions to stop out"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.size() == 1
        response.body.message == "Found 1 positions that should be stopped out"
    }

    def "should handle database errors gracefully"() {
        given: "service throws SQLException"
        positionsService.getPositions(null, null, null) >> { throw new SQLException("Database connection failed") }

        when: "calling get positions endpoint"
        def response = positionsController.getPositions(null, null, null)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to retrieve positions: Database connection failed")
    }

    def "should update close price successfully"() {
        given: "service updates close price successfully"
        def updatedPosition = createSamplePosition(1L, "AAPL")
        updatedPosition.setClosePrice(new BigDecimal("155.00"))
        updatedPosition.setPnlValue(new BigDecimal("475.00"))
        updatedPosition.setPnlPercent(new BigDecimal("0.0316"))
        positionsService.updateClosePrice(1L, new BigDecimal("155.00")) >> updatedPosition

        when: "calling update close price endpoint"
        def response = positionsController.updateClosePrice(1L, new BigDecimal("155.00"))

        then: "should return updated position with P&L"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.closePrice == new BigDecimal("155.00")
        response.body.data.pnlValue == new BigDecimal("475.00")
        response.body.data.pnlPercent == new BigDecimal("0.0316")
        response.body.message == "Close price updated successfully with P&L calculation"
    }

    def "should handle invalid close price"() {
        when: "calling update close price with invalid price"
        def response = positionsController.updateClosePrice(1L, new BigDecimal("-10.00"))

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message == "Close price must be positive"
    }

    def "should handle null close price"() {
        when: "calling update close price with null price"
        def response = positionsController.updateClosePrice(1L, null)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message == "Close price must be positive"
    }

    def "should handle close price update for non-existent position"() {
        given: "service throws IllegalArgumentException for non-existent position"
        positionsService.updateClosePrice(999L, new BigDecimal("155.00")) >> {
            throw new IllegalArgumentException("Position not found: 999")
        }

        when: "calling update close price endpoint"
        def response = positionsController.updateClosePrice(999L, new BigDecimal("155.00"))

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Position not found: 999")
    }

    def "should handle close price update database error"() {
        given: "service throws SQLException"
        positionsService.updateClosePrice(1L, new BigDecimal("155.00")) >> {
            throw new SQLException("Database error")
        }

        when: "calling update close price endpoint"
        def response = positionsController.updateClosePrice(1L, new BigDecimal("155.00"))

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to update close price: Database error")
    }

    private Position createSamplePosition(Long id, String symbol) {
        def position = new Position(symbol, new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))
        position.setId(id)
        position.setStatus(Position.Status.OPEN)
        position.setCreatedDate(LocalDateTime.now())
        position.setUpdatedDate(LocalDateTime.now())
        return position
    }
}
