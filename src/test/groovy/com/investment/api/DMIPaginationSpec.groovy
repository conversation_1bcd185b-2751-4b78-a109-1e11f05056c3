package com.investment.api

import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import com.investment.database.DatabaseManager
import com.investment.service.DMIService
import spock.lang.Specification
import spock.lang.Shared

/**
 * Test specification for DMI pagination functionality.
 * Verifies that pagination parameters work correctly and that responses
 * include proper pagination metadata.
 */
class DMIPaginationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    DMIService dmiService

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/dmi_pagination_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("*********************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
        
        dmiService = new DMIService(databaseManager)
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_pagination_test.duckdb").delete()
    }

    def setup() {
        // Clean up any existing test data
        try {
            databaseManager.connection.createStatement().execute("DELETE FROM ohlcv WHERE symbol LIKE 'PAG_%'")
            databaseManager.connection.createStatement().execute("DELETE FROM instruments WHERE symbol LIKE 'PAG_%'")
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }

    def "should validate pagination parameters correctly"() {
        when: "creating request with negative start index"
        def request = new DMIRequest()
        request.startIndex = -1
        request.validatePagination()

        then: "validation should fail"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Start index must be non-negative")

        when: "creating request with zero end index"
        request = new DMIRequest()
        request.endIndex = 0
        request.validatePagination()

        then: "validation should fail"
        exception = thrown(IllegalArgumentException)
        exception.message.contains("End index must be positive")

        when: "creating request with end index less than start index"
        request = new DMIRequest()
        request.startIndex = 10
        request.endIndex = 5
        request.validatePagination()

        then: "validation should fail"
        exception = thrown(IllegalArgumentException)
        exception.message.contains("End index (5) must be greater than start index (10)")

        when: "creating request with negative max symbols"
        request = new DMIRequest()
        request.maxSymbols = -1
        request.validatePagination()

        then: "validation should fail"
        exception = thrown(IllegalArgumentException)
        exception.message.contains("Max symbols must be non-negative")
    }

    def "should handle valid pagination parameters"() {
        when: "creating request with valid pagination parameters"
        def request = new DMIRequest()
        request.startIndex = 0
        request.endIndex = 10
        request.maxSymbols = 5

        then: "validation should pass"
        notThrown(IllegalArgumentException)
        request.validatePagination()
    }

    def "should return error response for invalid pagination parameters"() {
        given: "a request with invalid pagination parameters"
        def request = new DMIRequest()
        request.startIndex = 10
        request.endIndex = 5  // Invalid: end < start

        when: "calculating DMI"
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should return error response"
        response.status == "error"
        response.errors.size() == 1
        response.errors[0].contains("Invalid pagination parameters")
        response.errors[0].contains("End index (5) must be greater than start index (10)")
        response.processedSymbols == 0
        response.totalRecordsUpdated == 0
    }

    def "should include pagination metadata in response"() {
        given: "instruments with market cap data"
        // Create 5 test instruments with different market caps
        (1..5).each { i ->
            def symbol = "PAG_${String.format('%03d', i)}"
            def marketCap = new BigDecimal(i * 1000000000L) // $1B, $2B, $3B, $4B, $5B
            databaseManager.saveInstrumentWithDetails(symbol, "Test Company $i", "US_STOCK", 
                                                      marketCap, "United States", 2000, "Technology", "Software")
            
            // Add minimal OHLCV data
            def ohlcvData = createMinimalOHLCVData(symbol, 30)
            databaseManager.saveOHLCVData(ohlcvData)
        }

        when: "requesting DMI calculation with pagination"
        def request = new DMIRequest()
        request.startIndex = 1
        request.endIndex = 4
        request.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        request.dryRun = true

        DMIResponse response = dmiService.calculateDMI(request)

        then: "response should include correct pagination metadata"
        response.totalSymbolsAvailable == 5
        response.startIndex == 1
        response.endIndex == 4
        response.symbolsInRange == 3  // symbols at indices 1, 2, 3
        response.processedSymbols == 3
        response.status == "success"
        response.summary.contains("(range: 1-3 of 5 total)")
    }

    def "should process symbols in correct market cap order with pagination"() {
        given: "instruments with known market caps"
        // Create instruments with specific market caps for predictable ordering
        def instruments = [
            [symbol: "PAG_MEGA", marketCap: new BigDecimal("1000000000000")], // $1T (highest)
            [symbol: "PAG_LARGE", marketCap: new BigDecimal("500000000000")], // $500B
            [symbol: "PAG_MID", marketCap: new BigDecimal("100000000000")],   // $100B
            [symbol: "PAG_SMALL", marketCap: new BigDecimal("10000000000")],  // $10B
            [symbol: "PAG_MICRO", marketCap: new BigDecimal("1000000000")]    // $1B (lowest)
        ]

        instruments.each { inst ->
            databaseManager.saveInstrumentWithDetails(inst.symbol, "Test Company", "US_STOCK", 
                                                      inst.marketCap, "United States", 2000, "Technology", "Software")
            def ohlcvData = createMinimalOHLCVData(inst.symbol, 30)
            databaseManager.saveOHLCVData(ohlcvData)
        }

        when: "requesting middle symbols (indices 1-3)"
        def request = new DMIRequest()
        request.startIndex = 1
        request.endIndex = 4
        request.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        request.dryRun = true

        DMIResponse response = dmiService.calculateDMI(request)

        then: "should process symbols in market cap order"
        response.totalSymbolsAvailable == 5
        response.startIndex == 1
        response.endIndex == 4
        response.symbolsInRange == 3
        response.processedSymbols == 3
        
        // Verify the order by checking which symbols would be processed
        // Order should be: PAG_MEGA (0), PAG_LARGE (1), PAG_MID (2), PAG_SMALL (3), PAG_MICRO (4)
        // So indices 1-3 should process: PAG_LARGE, PAG_MID, PAG_SMALL
    }

    def "should handle pagination with maxSymbols limit"() {
        given: "instruments with OHLCV data"
        (1..10).each { i ->
            def symbol = "PAG_MAX_${String.format('%02d', i)}"
            def marketCap = new BigDecimal(i * 1000000000L)
            databaseManager.saveInstrumentWithDetails(symbol, "Test Company $i", "US_STOCK", 
                                                      marketCap, "United States", 2000, "Technology", "Software")
            def ohlcvData = createMinimalOHLCVData(symbol, 30)
            databaseManager.saveOHLCVData(ohlcvData)
        }

        when: "requesting with both pagination and maxSymbols limit"
        def request = new DMIRequest()
        request.startIndex = 2
        request.endIndex = 8
        request.maxSymbols = 3  // Should limit to 3 symbols even though range is 6
        request.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        request.dryRun = true

        DMIResponse response = dmiService.calculateDMI(request)

        then: "should respect both pagination and maxSymbols limit"
        response.totalSymbolsAvailable == 10
        response.startIndex == 2
        response.endIndex == 5  // startIndex + maxSymbols = 2 + 3 = 5
        response.symbolsInRange == 3
        response.processedSymbols == 3
    }

    def "should handle pagination beyond available symbols"() {
        given: "limited number of instruments"
        (1..3).each { i ->
            def symbol = "PAG_LIMIT_${i}"
            def marketCap = new BigDecimal(i * 1000000000L)
            databaseManager.saveInstrumentWithDetails(symbol, "Test Company $i", "US_STOCK", 
                                                      marketCap, "United States", 2000, "Technology", "Software")
            def ohlcvData = createMinimalOHLCVData(symbol, 30)
            databaseManager.saveOHLCVData(ohlcvData)
        }

        when: "requesting pagination beyond available symbols"
        def request = new DMIRequest()
        request.startIndex = 5  // Beyond available symbols
        request.endIndex = 10
        request.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        request.dryRun = true

        DMIResponse response = dmiService.calculateDMI(request)

        then: "should handle gracefully with no symbols processed"
        response.totalSymbolsAvailable == 3
        response.startIndex == 5
        response.endIndex == 5  // Adjusted to not exceed total
        response.symbolsInRange == 0
        response.processedSymbols == 0
        response.status == "success"
    }

    def "should maintain backward compatibility when no pagination parameters provided"() {
        given: "instruments with OHLCV data"
        (1..5).each { i ->
            def symbol = "PAG_COMPAT_${i}"
            def marketCap = new BigDecimal(i * 1000000000L)
            databaseManager.saveInstrumentWithDetails(symbol, "Test Company $i", "US_STOCK", 
                                                      marketCap, "United States", 2000, "Technology", "Software")
            def ohlcvData = createMinimalOHLCVData(symbol, 30)
            databaseManager.saveOHLCVData(ohlcvData)
        }

        when: "requesting DMI calculation without pagination parameters"
        def request = new DMIRequest()
        request.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        request.dryRun = true
        // No startIndex, endIndex specified (should use defaults)

        DMIResponse response = dmiService.calculateDMI(request)

        then: "should process all symbols (backward compatibility)"
        response.totalSymbolsAvailable == 5
        response.startIndex == 0  // Default start index
        response.endIndex == 5   // All symbols
        response.symbolsInRange == 5
        response.processedSymbols == 5
        response.status == "success"
    }

    def "should work with hybrid calculation method and pagination"() {
        given: "instruments with sufficient OHLCV data for hybrid calculation"
        (1..4).each { i ->
            def symbol = "PAG_HYBRID_${i}"
            def marketCap = new BigDecimal(i * 1000000000L)
            databaseManager.saveInstrumentWithDetails(symbol, "Test Company $i", "US_STOCK", 
                                                      marketCap, "United States", 2000, "Technology", "Software")
            def ohlcvData = createMinimalOHLCVData(symbol, 50) // More data for hybrid calculation
            databaseManager.saveOHLCVData(ohlcvData)
        }

        when: "requesting hybrid calculation with pagination"
        def request = new DMIRequest()
        request.startIndex = 1
        request.endIndex = 3
        request.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        request.dryRun = true

        DMIResponse response = dmiService.calculateDMI(request)

        then: "should work correctly with hybrid method"
        response.totalSymbolsAvailable == 4
        response.startIndex == 1
        response.endIndex == 3
        response.symbolsInRange == 2
        response.processedSymbols == 2
        response.status == "success"
        response.parameters.calculationMethod == DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
    }

    /**
     * Create minimal OHLCV data for testing.
     */
    private List createMinimalOHLCVData(String symbol, int days) {
        def data = []
        def baseDate = java.time.LocalDate.now().minusDays(days)

        (0..<days).each { i ->
            def date = baseDate.plusDays(i)
            def price = 100.0 + (i * 0.1)
            data.add(new com.investment.model.OHLCV(
                symbol, date, price, price + 1, price - 1, price + 0.5, 1000000L
            ))
        }

        return data
    }
}
