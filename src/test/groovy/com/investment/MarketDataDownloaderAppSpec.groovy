package com.investment

import com.investment.config.MarketDataDownloadConfig
import spock.lang.Specification

import java.time.LocalDate

/**
 * Spock specification for testing the MarketDataDownloaderApp.
 */
class MarketDataDownloaderAppSpec extends Specification {

    def "should parse command line arguments correctly"() {
        when: "parsing empty arguments"
        def config = MarketDataDownloaderApp.parseCommandLineArgs([] as String[])

        then: "should return default configuration"
        config != null
        config.continueOnError == true
        config.maxRetries == 3
        config.skipExistingData == true
    }

    def "should parse start date argument"() {
        when: "parsing start date argument"
        def args = ["--start-date", "2020-01-01"] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should set start date correctly"
        config.startDate == LocalDate.of(2020, 1, 1)
    }

    def "should parse end date argument"() {
        when: "parsing end date argument"
        def args = ["--end-date", "2023-12-31"] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should set end date correctly"
        config.endDate == LocalDate.of(2023, 12, 31)
    }

    def "should parse full history flag"() {
        when: "parsing full history flag"
        def args = ["--full-history"] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should configure for full history"
        config.startDate.year == MarketDataDownloadConfig.YAHOO_HISTORICAL_DATA_START_YEAR
        config.skipExistingData == false
    }

    def "should parse recent only flag"() {
        when: "parsing recent only flag"
        def args = ["--recent-only"] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should configure for recent data"
        config.startDate == LocalDate.now().minusDays(30)
        config.skipExistingData == true
    }

    def "should parse no skip existing flag"() {
        when: "parsing no skip existing flag"
        def args = ["--no-skip-existing"] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should disable skipping existing data"
        config.skipExistingData == false
    }

    def "should parse stop on error flag"() {
        when: "parsing stop on error flag"
        def args = ["--stop-on-error"] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should disable continue on error"
        config.continueOnError == false
    }

    def "should parse max retries argument"() {
        when: "parsing max retries argument"
        def args = ["--max-retries", "5"] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should set max retries correctly"
        config.maxRetries == 5
    }

    def "should parse multiple arguments"() {
        when: "parsing multiple arguments"
        def args = [
            "--start-date", "2020-01-01",
            "--end-date", "2023-12-31",
            "--max-retries", "5",
            "--stop-on-error"
        ] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should set all arguments correctly"
        config.startDate == LocalDate.of(2020, 1, 1)
        config.endDate == LocalDate.of(2023, 12, 31)
        config.maxRetries == 5
        config.continueOnError == false
    }

    def "should handle unknown arguments gracefully"() {
        when: "parsing unknown arguments"
        def args = ["--unknown-flag", "value"] as String[]
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should return default configuration"
        config != null
        config.continueOnError == true
        config.maxRetries == 3
    }

    def "should handle missing values for arguments"() {
        when: "parsing arguments with missing values"
        def args = ["--start-date"] as String[] // Missing date value
        def config = MarketDataDownloaderApp.parseCommandLineArgs(args)

        then: "should not crash and return configuration"
        config != null
        // Start date should remain default since parsing failed
    }
}
