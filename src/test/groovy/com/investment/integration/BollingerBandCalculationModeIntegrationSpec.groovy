package com.investment.integration

import com.investment.api.model.BollingerBandRequest
import com.investment.api.model.BollingerBandResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.BollingerBandService
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Integration test for Bollinger Band calculation modes.
 * This test demonstrates the different calculation modes in action.
 */
class BollingerBandCalculationModeIntegrationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager
    
    @Shared
    BollingerBandService bollingerBandService

    def setupSpec() {
        try {
            // Clean up any existing test database
            new File("./data/bb_calc_mode_test.duckdb").delete()

            // Initialize test database
            DatabaseManager.setDbUrl("*******************************************")
            databaseManager = new DatabaseManager()
            databaseManager.initDatabase()
            bollingerBandService = new BollingerBandService(databaseManager)

            // Create test data
            setupTestData()
        } catch (Exception e) {
            println "Setup failed: ${e.message}"
            throw e
        }
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/bb_calc_mode_test.duckdb").delete()
    }

    def "should demonstrate INCREMENTAL mode behavior"() {
        given: "initial calculation with some data"
        def initialRequest = new BollingerBandRequest()
        initialRequest.calculationMode = BollingerBandRequest.CalculationMode.FULL_RECALCULATION
        
        when: "performing initial full calculation"
        BollingerBandResponse initialResponse = bollingerBandService.calculateBollingerBands(initialRequest)
        
        then: "initial calculation should process all data"
        initialResponse.status == "success"
        initialResponse.processedSymbols > 0
        initialResponse.parameters.calculationMode == BollingerBandRequest.CalculationMode.FULL_RECALCULATION
        
        when: "adding new data points"
        addNewDataPoints()
        
        and: "performing incremental calculation"
        def incrementalRequest = new BollingerBandRequest()
        incrementalRequest.calculationMode = BollingerBandRequest.CalculationMode.INCREMENTAL
        BollingerBandResponse incrementalResponse = bollingerBandService.calculateBollingerBands(incrementalRequest)
        
        then: "incremental calculation should process only new data"
        incrementalResponse.status == "success"
        incrementalResponse.parameters.calculationMode == BollingerBandRequest.CalculationMode.INCREMENTAL
        incrementalResponse.totalRecordsUpdated < initialResponse.totalRecordsUpdated
    }

    def "should demonstrate FULL_RECALCULATION mode behavior"() {
        given: "existing Bollinger Band data"
        def request = new BollingerBandRequest()
        request.calculationMode = BollingerBandRequest.CalculationMode.FULL_RECALCULATION
        
        when: "performing full recalculation"
        BollingerBandResponse response = bollingerBandService.calculateBollingerBands(request)
        
        then: "should recalculate all data from scratch"
        response.status == "success"
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.FULL_RECALCULATION
        response.processedSymbols > 0
        response.totalRecordsUpdated > 0
    }

    def "should demonstrate SKIP_EXISTING mode behavior"() {
        given: "symbols with existing Bollinger Band data"
        def request = new BollingerBandRequest()
        request.calculationMode = BollingerBandRequest.CalculationMode.SKIP_EXISTING
        
        when: "performing calculation with skip existing mode"
        BollingerBandResponse response = bollingerBandService.calculateBollingerBands(request)
        
        then: "should skip symbols with existing data"
        response.status == "success"
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.SKIP_EXISTING
        response.skippedSymbols.size() > 0
    }

    def "should demonstrate backward compatibility with forceRecalculate"() {
        given: "legacy request with forceRecalculate=true"
        def request = new BollingerBandRequest()
        request.forceRecalculate = true
        
        when: "performing calculation"
        BollingerBandResponse response = bollingerBandService.calculateBollingerBands(request)
        
        then: "should map to FULL_RECALCULATION mode"
        response.status == "success"
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.FULL_RECALCULATION
    }

    def "should demonstrate dry run mode with different calculation modes"() {
        given: "dry run request with INCREMENTAL mode"
        def request = new BollingerBandRequest()
        request.dryRun = true
        request.calculationMode = BollingerBandRequest.CalculationMode.INCREMENTAL
        
        when: "performing dry run calculation"
        BollingerBandResponse response = bollingerBandService.calculateBollingerBands(request)
        
        then: "should report what would be updated without making changes"
        response.status == "success"
        response.dryRun == true
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.INCREMENTAL
        response.totalRecordsUpdated >= 0  // May be 0 if no new data to process
    }

    private void setupTestData() {
        // Create test instruments
        databaseManager.saveInstrument("AAPL", "Apple Inc.", "Technology")
        databaseManager.saveInstrument("MSFT", "Microsoft Corporation", "Technology")

        // Create OHLCV data for the past 30 days
        LocalDate startDate = LocalDate.now().minusDays(30)
        List<OHLCV> testData = []

        for (int i = 0; i < 25; i++) {
            LocalDate date = startDate.plusDays(i)

            // AAPL data
            testData.add(new OHLCV("AAPL", date, 150.0 + i, 155.0 + i, 148.0 + i, 152.0 + i, 1000000L))

            // MSFT data
            testData.add(new OHLCV("MSFT", date, 300.0 + i, 305.0 + i, 298.0 + i, 302.0 + i, 800000L))
        }

        databaseManager.saveOHLCVData(testData)
    }
    
    private void addNewDataPoints() {
        // Add 5 more days of data to simulate new data arrival
        LocalDate startDate = LocalDate.now().minusDays(5)
        List<OHLCV> newData = []
        
        for (int i = 0; i < 5; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // AAPL data
            newData.add(new OHLCV("AAPL", date, 175.0 + i, 180.0 + i, 173.0 + i, 177.0 + i, 1200000L))
            
            // MSFT data
            newData.add(new OHLCV("MSFT", date, 325.0 + i, 330.0 + i, 323.0 + i, 327.0 + i, 900000L))
        }
        
        databaseManager.saveOHLCVData(newData)
    }
}
