package com.investment.integration

import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.DMIService
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Integration test to verify that both PURE_JAVA and HYBRID_SQL_JAVA calculation methods
 * produce identical results for DMI calculations.
 */
class DMICalculationMethodIntegrationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    DMIService dmiService

    def setupSpec() {
        try {
            // Clean up any existing test database
            new File("./data/dmi_calculation_method_test.duckdb").delete()

            // Initialize test database
            DatabaseManager.setDbUrl("*****************************************************")
            databaseManager = new DatabaseManager()
            databaseManager.initDatabase()

            // Create DMIService with DatabaseManager
            dmiService = new DMIService(databaseManager)

            // Create test data
            setupTestData()
        } catch (Exception e) {
            println "Setup failed: ${e.message}"
            throw e
        }
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_calculation_method_test.duckdb").delete()
    }

    def "PURE_JAVA and HYBRID_SQL_JAVA methods should produce identical DMI results"() {
        given: "MSFT symbol with OHLCV data"
        def symbol = "MSFT"

        when: "calculating DMI using PURE_JAVA method"
        databaseManager.clearDMIData(symbol)
        def pureJavaRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        pureJavaRequest.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        pureJavaRequest.symbols = [symbol]
        
        DMIResponse pureJavaResponse = dmiService.calculateDMI(pureJavaRequest)
        
        then: "pure Java calculation should succeed"
        pureJavaResponse.status == "success"
        pureJavaResponse.processedSymbols == 1
        pureJavaResponse.totalRecordsUpdated > 0
        pureJavaResponse.parameters.calculationMethod == DMIRequest.CalculationMethod.PURE_JAVA
        
        when: "getting the results from PURE_JAVA calculation"
        def pureJavaData = getRecentDMIData(symbol, 10)
        println "Pure Java calculation results (last 10 records):"
        pureJavaData.each { ohlcv ->
            println "  ${ohlcv.date}: +DI=${String.format('%.6f', ohlcv.dmiPlusDI ?: 0.0)}, -DI=${String.format('%.6f', ohlcv.dmiMinusDI ?: 0.0)}, DX=${String.format('%.6f', ohlcv.dmiDX ?: 0.0)}, ADX=${String.format('%.6f', ohlcv.dmiADX ?: 0.0)}"
        }
        
        and: "calculating DMI using HYBRID_SQL_JAVA method"
        databaseManager.clearDMIData(symbol)
        def hybridRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        hybridRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        hybridRequest.symbols = [symbol]
        
        DMIResponse hybridResponse = dmiService.calculateDMI(hybridRequest)
        
        then: "hybrid calculation should succeed"
        hybridResponse.status == "success"
        hybridResponse.processedSymbols == 1
        hybridResponse.totalRecordsUpdated > 0
        hybridResponse.parameters.calculationMethod == DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        
        when: "getting the results from HYBRID_SQL_JAVA calculation"
        def hybridData = getRecentDMIData(symbol, 10)
        println "Hybrid calculation results (last 10 records):"
        hybridData.each { ohlcv ->
            println "  ${ohlcv.date}: +DI=${String.format('%.6f', ohlcv.dmiPlusDI ?: 0.0)}, -DI=${String.format('%.6f', ohlcv.dmiMinusDI ?: 0.0)}, DX=${String.format('%.6f', ohlcv.dmiDX ?: 0.0)}, ADX=${String.format('%.6f', ohlcv.dmiADX ?: 0.0)}"
        }
        
        then: "both methods should produce identical results"
        pureJavaData.size() == hybridData.size()
        
        // Compare each record with high precision
        for (int i = 0; i < pureJavaData.size(); i++) {
            def pureRecord = pureJavaData[i]
            def hybridRecord = hybridData[i]
            
            assert pureRecord.date == hybridRecord.date
            
            // Compare DMI values with tolerance for floating point precision
            def tolerance = 0.000001
            
            if (pureRecord.dmiPlusDI != null && hybridRecord.dmiPlusDI != null) {
                def plusDIDiff = Math.abs(pureRecord.dmiPlusDI - hybridRecord.dmiPlusDI)
                assert plusDIDiff < tolerance : "+DI mismatch for ${pureRecord.date}: pure=${pureRecord.dmiPlusDI}, hybrid=${hybridRecord.dmiPlusDI}, diff=${plusDIDiff}"
                
                def minusDIDiff = Math.abs(pureRecord.dmiMinusDI - hybridRecord.dmiMinusDI)
                assert minusDIDiff < tolerance : "-DI mismatch for ${pureRecord.date}: pure=${pureRecord.dmiMinusDI}, hybrid=${hybridRecord.dmiMinusDI}, diff=${minusDIDiff}"
                
                if (pureRecord.dmiDX != null && hybridRecord.dmiDX != null) {
                    def dxDiff = Math.abs(pureRecord.dmiDX - hybridRecord.dmiDX)
                    assert dxDiff < tolerance : "DX mismatch for ${pureRecord.date}: pure=${pureRecord.dmiDX}, hybrid=${hybridRecord.dmiDX}, diff=${dxDiff}"
                }
                
                if (pureRecord.dmiADX != null && hybridRecord.dmiADX != null) {
                    def adxDiff = Math.abs(pureRecord.dmiADX - hybridRecord.dmiADX)
                    assert adxDiff < tolerance : "ADX mismatch for ${pureRecord.date}: pure=${pureRecord.dmiADX}, hybrid=${hybridRecord.dmiADX}, diff=${adxDiff}"
                }
            }
        }
        
        println "✅ Both PURE_JAVA and HYBRID_SQL_JAVA methods produce identical DMI results!"
    }

    def "calculation method should be properly reflected in response parameters"() {
        given: "MSFT symbol with OHLCV data"
        def symbol = "MSFT"

        when: "calculating with PURE_JAVA method"
        def pureJavaRequest = new DMIRequest(14, true, DMIRequest.CalculationMode.INCREMENTAL)
        pureJavaRequest.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        pureJavaRequest.symbols = [symbol]
        
        DMIResponse pureJavaResponse = dmiService.calculateDMI(pureJavaRequest)
        
        then: "response should reflect PURE_JAVA method"
        pureJavaResponse.parameters.calculationMethod == DMIRequest.CalculationMethod.PURE_JAVA
        
        when: "calculating with HYBRID_SQL_JAVA method"
        def hybridRequest = new DMIRequest(14, true, DMIRequest.CalculationMode.INCREMENTAL)
        hybridRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        hybridRequest.symbols = [symbol]
        
        DMIResponse hybridResponse = dmiService.calculateDMI(hybridRequest)
        
        then: "response should reflect HYBRID_SQL_JAVA method"
        hybridResponse.parameters.calculationMethod == DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
    }

    /**
     * Get recent DMI data for testing
     */
    private List<OHLCV> getRecentDMIData(String symbol, int count) {
        def endDate = LocalDate.now()
        def startDate = endDate.minusDays(count + 10) // Get extra days to ensure we have enough data
        
        def data = databaseManager.getOHLCVData(symbol, startDate, endDate)
        return data.findAll { it.dmiPlusDI != null }
                  .sort { it.date }
                  .takeRight(count)
    }

    /**
     * Setup test data with realistic OHLCV data for MSFT
     */
    private void setupTestData() {
        // Create test instrument
        databaseManager.saveInstrument("MSFT", "Microsoft Corporation", "Technology")

        // Create realistic OHLCV data for MSFT for the past 60 days
        LocalDate startDate = LocalDate.now().minusDays(60)
        List<OHLCV> testData = []

        // Generate realistic price data with some volatility
        double basePrice = 400.0
        Random random = new Random(12345) // Fixed seed for reproducible tests

        for (int i = 0; i < 50; i++) {
            LocalDate date = startDate.plusDays(i)

            // Add some realistic price movement
            double priceChange = (random.nextGaussian() * 5.0) // Standard deviation of $5
            double open = basePrice + priceChange
            double close = open + (random.nextGaussian() * 3.0) // Intraday movement
            double high = Math.max(open, close) + Math.abs(random.nextGaussian() * 2.0)
            double low = Math.min(open, close) - Math.abs(random.nextGaussian() * 2.0)
            long volume = 15000000L + (long)(random.nextGaussian() * 5000000L)

            testData.add(new OHLCV("MSFT", date, open, high, low, close, Math.max(volume, 1000000L)))

            // Update base price for next day (trend)
            basePrice = close
        }

        databaseManager.saveOHLCVData(testData)
        println "Created ${testData.size()} OHLCV records for MSFT"
    }
}
