package com.investment.integration

import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.DMIService
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Integration test for DMI calculation algorithm accuracy and real-world scenarios.
 */
class DMICalculationIntegrationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    DMIService dmiService

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/dmi_calc_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("***************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
        dmiService = new DMIService(databaseManager)
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_calc_test.duckdb").delete()
    }

    def "should calculate DMI correctly for known test data"() {
        given: "a symbol with known OHLCV data"
        String symbol = "TEST_DMI"
        databaseManager.saveInstrument(symbol, "Test DMI Company", "Technology")
        
        and: "historical data with known price movements"
        List<OHLCV> testData = createKnownTestData(symbol)
        databaseManager.saveOHLCVData(testData)

        when: "calculating DMI with 14-day period"
        DMIRequest request = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        DMIResponse response = dmiService.calculateDMI(request)

        then: "calculation should complete successfully"
        response.status == "success"
        response.processedSymbols == 1
        response.totalRecordsUpdated > 0

        when: "retrieving the calculated data"
        List<OHLCV> calculatedData = databaseManager.getOHLCVData(symbol, 
                                                                  LocalDate.now().minusDays(35), 
                                                                  LocalDate.now())

        then: "should have DMI values for sufficient data points"
        def recordsWithDMI = calculatedData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() >= 2  // Should have DMI for at least the last few records
        
        and: "DMI values should be within valid ranges"
        recordsWithDMI.every { record ->
            record.dmiPlusDi >= 0 && record.dmiPlusDi <= 100 &&
            record.dmiMinusDi >= 0 && record.dmiMinusDi <= 100 &&
            (record.dmiAdx == null || (record.dmiAdx >= 0 && record.dmiAdx <= 100))
        }
        
        and: "ADX should generally be lower than DI values for trending markets"
        def lastRecord = recordsWithDMI.last()
        lastRecord.dmiAdx != null
    }

    def "should handle incremental calculation correctly"() {
        given: "a symbol with existing DMI data"
        String symbol = "INCR_TEST"
        databaseManager.saveInstrument(symbol, "Incremental Test Company", "Technology")
        
        and: "initial historical data"
        List<OHLCV> initialData = createTestDataRange(symbol, LocalDate.now().minusDays(40), 30)
        databaseManager.saveOHLCVData(initialData)
        
        and: "initial DMI calculation"
        DMIRequest initialRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        dmiService.calculateDMI(initialRequest)

        when: "adding new data points"
        List<OHLCV> newData = createTestDataRange(symbol, LocalDate.now().minusDays(9), 10)
        databaseManager.saveOHLCVData(newData)

        and: "performing incremental calculation"
        DMIRequest incrementalRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.INCREMENTAL)
        DMIResponse response = dmiService.calculateDMI(incrementalRequest)

        then: "incremental calculation should complete successfully"
        response.status == "success"
        response.processedSymbols == 1
        response.parameters.calculationMode == DMIRequest.CalculationMode.INCREMENTAL

        when: "retrieving all data"
        List<OHLCV> allData = databaseManager.getOHLCVData(symbol, 
                                                           LocalDate.now().minusDays(45), 
                                                           LocalDate.now())

        then: "should have DMI values for both old and new data"
        def recordsWithDMI = allData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() >= 15  // Should have DMI for most records
        
        and: "new data points should have DMI values"
        def recentRecords = allData.findAll { it.date.isAfter(LocalDate.now().minusDays(10)) }
        recentRecords.every { it.dmiPlusDi != null }
    }

    def "should calculate DMI with different periods correctly"() {
        given: "a symbol with sufficient data"
        String symbol = "PERIOD_TEST"
        databaseManager.saveInstrument(symbol, "Period Test Company", "Technology")
        
        and: "60 days of historical data"
        List<OHLCV> testData = createTestDataRange(symbol, LocalDate.now().minusDays(60), 60)
        databaseManager.saveOHLCVData(testData)

        when: "calculating DMI with 7-day period"
        DMIRequest request7 = new DMIRequest(7, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        request7.minDataPoints = 20
        DMIResponse response7 = dmiService.calculateDMI(request7)

        then: "7-day calculation should complete"
        response7.status == "success"
        response7.parameters.period == 7

        when: "calculating DMI with 21-day period"
        databaseManager.clearDMIData(symbol)
        DMIRequest request21 = new DMIRequest(21, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        request21.minDataPoints = 50
        DMIResponse response21 = dmiService.calculateDMI(request21)

        then: "21-day calculation should complete"
        response21.status == "success"
        response21.parameters.period == 21

        when: "comparing the results"
        List<OHLCV> data7 = databaseManager.getOHLCVData(symbol, LocalDate.now().minusDays(65), LocalDate.now())
        databaseManager.clearDMIData(symbol)
        dmiService.calculateDMI(request21)
        List<OHLCV> data21 = databaseManager.getOHLCVData(symbol, LocalDate.now().minusDays(65), LocalDate.now())

        then: "different periods should produce different DMI values"
        def records7WithDMI = data7.findAll { it.dmiPlusDi != null }
        def records21WithDMI = data21.findAll { it.dmiPlusDi != null }
        
        // 7-day period should have more records with DMI (starts calculating earlier)
        records7WithDMI.size() > records21WithDMI.size()
    }

    def "should handle edge cases and insufficient data"() {
        given: "a symbol with minimal data"
        String symbol = "EDGE_TEST"
        databaseManager.saveInstrument(symbol, "Edge Case Test Company", "Technology")
        
        and: "only 10 days of data (insufficient for 14-day DMI)"
        List<OHLCV> minimalData = createTestDataRange(symbol, LocalDate.now().minusDays(10), 10)
        databaseManager.saveOHLCVData(minimalData)

        when: "attempting to calculate DMI"
        DMIRequest request = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should report insufficient data"
        response.symbolsWithInsufficientData.contains(symbol)
        response.processedSymbols == 0
    }

    def "should handle dry run mode accurately"() {
        given: "a symbol with data"
        String symbol = "DRY_RUN_TEST"
        databaseManager.saveInstrument(symbol, "Dry Run Test Company", "Technology")
        
        and: "sufficient historical data"
        List<OHLCV> testData = createTestDataRange(symbol, LocalDate.now().minusDays(35), 35)
        databaseManager.saveOHLCVData(testData)

        when: "performing dry run calculation"
        DMIRequest dryRunRequest = new DMIRequest(14, true, DMIRequest.CalculationMode.FULL_RECALCULATION)
        DMIResponse dryRunResponse = dmiService.calculateDMI(dryRunRequest)

        then: "dry run should complete without updating data"
        dryRunResponse.status == "success"
        dryRunResponse.dryRun == true
        dryRunResponse.totalRecordsUpdated > 0  // Should report what would be updated

        when: "checking that no actual data was updated"
        List<OHLCV> dataAfterDryRun = databaseManager.getOHLCVData(symbol, 
                                                                   LocalDate.now().minusDays(40), 
                                                                   LocalDate.now())

        then: "should have no DMI data"
        dataAfterDryRun.every { it.dmiPlusDi == null && it.dmiMinusDi == null && it.dmiAdx == null }

        when: "performing actual calculation"
        DMIRequest actualRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        DMIResponse actualResponse = dmiService.calculateDMI(actualRequest)

        then: "actual calculation should update data"
        actualResponse.status == "success"
        actualResponse.dryRun == false
        
        when: "checking that data was actually updated"
        List<OHLCV> dataAfterActual = databaseManager.getOHLCVData(symbol, 
                                                                   LocalDate.now().minusDays(40), 
                                                                   LocalDate.now())

        then: "should have DMI data"
        def recordsWithDMI = dataAfterActual.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() > 0
    }

    def "should validate DMI mathematical properties"() {
        given: "a symbol with trending data"
        String symbol = "MATH_TEST"
        databaseManager.saveInstrument(symbol, "Math Test Company", "Technology")
        
        and: "data with clear upward trend"
        List<OHLCV> trendingData = createTrendingTestData(symbol)
        databaseManager.saveOHLCVData(trendingData)

        when: "calculating DMI"
        DMIRequest request = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        dmiService.calculateDMI(request)

        and: "retrieving calculated data"
        List<OHLCV> calculatedData = databaseManager.getOHLCVData(symbol, 
                                                                  LocalDate.now().minusDays(35), 
                                                                  LocalDate.now())

        then: "DMI values should follow mathematical properties"
        def recordsWithDMI = calculatedData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() > 0
        
        and: "for upward trending data, +DI should generally be higher than -DI"
        def recentRecords = recordsWithDMI.takeRight(5)
        def avgPlusDI = recentRecords.collect { it.dmiPlusDi }.sum() / recentRecords.size()
        def avgMinusDI = recentRecords.collect { it.dmiMinusDi }.sum() / recentRecords.size()
        avgPlusDI > avgMinusDI
        
        and: "ADX should be positive and reasonable when present"
        recentRecords.findAll { it.dmiAdx != null }.every { it.dmiAdx > 0 && it.dmiAdx < 80 }
    }

    // Helper methods for creating test data
    private List<OHLCV> createKnownTestData(String symbol) {
        List<OHLCV> data = []
        double basePrice = 100.0
        
        for (int i = 30; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i)
            double open = basePrice + (Math.random() - 0.5) * 2
            double close = open + (Math.random() - 0.3) * 3  // Slight upward bias
            double high = Math.max(open, close) + Math.random() * 2
            double low = Math.min(open, close) - Math.random() * 2
            long volume = (long) (1000000 + Math.random() * 500000)
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            basePrice = close // Use previous close as base for next day
        }
        
        return data
    }

    private List<OHLCV> createTestDataRange(String symbol, LocalDate startDate, int days) {
        List<OHLCV> data = []
        double basePrice = 150.0
        
        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            double open = basePrice + (Math.random() - 0.5) * 4
            double close = open + (Math.random() - 0.5) * 6
            double high = Math.max(open, close) + Math.random() * 3
            double low = Math.min(open, close) - Math.random() * 3
            long volume = (long) (800000 + Math.random() * 600000)
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            basePrice = close
        }
        
        return data
    }

    private List<OHLCV> createTrendingTestData(String symbol) {
        List<OHLCV> data = []
        double basePrice = 100.0
        
        for (int i = 30; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i)
            double trend = (30 - i) * 0.5  // Upward trend
            double open = basePrice + trend + (Math.random() - 0.5) * 1
            double close = open + trend * 0.1 + (Math.random() - 0.2) * 2  // Upward bias
            double high = Math.max(open, close) + Math.random() * 1.5
            double low = Math.min(open, close) - Math.random() * 1
            long volume = (long) (1200000 + Math.random() * 400000)
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            basePrice = close
        }
        
        return data
    }
}
