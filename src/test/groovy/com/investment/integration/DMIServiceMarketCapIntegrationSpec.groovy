package com.investment.integration

import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.DMIService
import spock.lang.Specification
import spock.lang.Shared

import java.math.BigDecimal
import java.time.LocalDate

/**
 * Integration test specification to verify that DMI service uses market cap ordering
 * and that the new ordering functionality works correctly with real DMI calculations.
 */
class DMIServiceMarketCapIntegrationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    DMIService dmiService

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/dmi_market_cap_integration_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("*********************************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
        
        dmiService = new DMIService(databaseManager)
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_market_cap_integration_test.duckdb").delete()
    }

    def setup() {
        // Clean up any existing test data
        try {
            databaseManager.connection.createStatement().execute("DELETE FROM ohlcv WHERE symbol LIKE 'INT_%'")
            databaseManager.connection.createStatement().execute("DELETE FROM instruments WHERE symbol LIKE 'INT_%'")
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }

    def "should process symbols in market cap order during DMI calculation"() {
        given: "instruments with different market caps and OHLCV data"
        // Create instruments with varying market caps
        databaseManager.saveInstrumentWithDetails("INT_SMALL", "Small Cap Company", "US_STOCK", 
                                                  new BigDecimal("1000000000"), "United States", 2010, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("INT_LARGE", "Large Cap Company", "US_STOCK", 
                                                  new BigDecimal("500000000000"), "United States", 2000, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("INT_MID", "Mid Cap Company", "US_STOCK", 
                                                  new BigDecimal("10000000000"), "United States", 2005, "Technology", "Software")

        and: "sufficient OHLCV data for DMI calculation"
        LocalDate startDate = LocalDate.now().minusDays(50)
        ["INT_SMALL", "INT_LARGE", "INT_MID"].each { symbol ->
            List<OHLCV> data = createRealisticOHLCVData(symbol, 50, startDate)
            databaseManager.saveOHLCVData(data)
        }

        when: "performing DMI calculation with default settings"
        DMIRequest request = new DMIRequest()
        request.period = 14
        request.calculationMode = DMIRequest.CalculationMode.FULL_RECALCULATION
        request.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        request.dryRun = false
        
        DMIResponse response = dmiService.calculateDMI(request)

        then: "calculation should be successful"
        response.status == "success"
        response.processedSymbols == 3
        response.totalRecordsUpdated > 0

        when: "checking the order in which symbols are retrieved"
        List<String> symbolsInOrder = databaseManager.getSymbolsWithOhlcvData()
        def integrationSymbols = symbolsInOrder.findAll { it.startsWith("INT_") }

        then: "symbols should be ordered by market cap (descending)"
        integrationSymbols.size() == 3
        integrationSymbols[0] == "INT_LARGE"  // $500B (highest)
        integrationSymbols[1] == "INT_MID"    // $10B (middle)
        integrationSymbols[2] == "INT_SMALL"  // $1B (lowest)

        and: "all symbols should have DMI data calculated"
        integrationSymbols.each { symbol ->
            List<OHLCV> results = databaseManager.getOHLCVData(symbol, startDate, startDate.plusDays(49))
            def recordsWithDMI = results.findAll { it.dmiPlusDi != null && it.dmiMinusDi != null && it.dmiAdx != null }
            assert recordsWithDMI.size() >= 20 // Should have DMI values for most recent dates
        }
    }

    def "should verify market cap ordering affects processing order but not calculation accuracy"() {
        given: "two sets of identical instruments with different market caps"
        // Set A: Higher market cap versions
        databaseManager.saveInstrumentWithDetails("INT_A_HIGH", "Company A High Cap", "US_STOCK", 
                                                  new BigDecimal("100000000000"), "United States", 2000, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("INT_B_HIGH", "Company B High Cap", "US_STOCK", 
                                                  new BigDecimal("200000000000"), "United States", 2000, "Technology", "Software")
        
        // Set B: Lower market cap versions (same companies, different market caps)
        databaseManager.saveInstrumentWithDetails("INT_A_LOW", "Company A Low Cap", "US_STOCK", 
                                                  new BigDecimal("5000000000"), "United States", 2000, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("INT_B_LOW", "Company B Low Cap", "US_STOCK", 
                                                  new BigDecimal("10000000000"), "United States", 2000, "Technology", "Software")

        and: "identical OHLCV data for corresponding pairs"
        LocalDate startDate = LocalDate.now().minusDays(40)
        List<OHLCV> baseDataA = createRealisticOHLCVData("BASE_A", 40, startDate)
        List<OHLCV> baseDataB = createRealisticOHLCVData("BASE_B", 40, startDate)
        
        // Create identical data for high and low cap versions
        List<OHLCV> dataAHigh = baseDataA.collect { ohlcv ->
            new OHLCV("INT_A_HIGH", ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> dataALow = baseDataA.collect { ohlcv ->
            new OHLCV("INT_A_LOW", ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> dataBHigh = baseDataB.collect { ohlcv ->
            new OHLCV("INT_B_HIGH", ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> dataBLow = baseDataB.collect { ohlcv ->
            new OHLCV("INT_B_LOW", ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        
        databaseManager.saveOHLCVData(dataAHigh)
        databaseManager.saveOHLCVData(dataALow)
        databaseManager.saveOHLCVData(dataBHigh)
        databaseManager.saveOHLCVData(dataBLow)

        when: "performing DMI calculation"
        DMIRequest request = new DMIRequest()
        request.period = 14
        request.calculationMode = DMIRequest.CalculationMode.FULL_RECALCULATION
        request.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        request.dryRun = false
        
        DMIResponse response = dmiService.calculateDMI(request)

        then: "calculation should be successful for all symbols"
        response.status == "success"
        response.processedSymbols == 4

        when: "checking processing order"
        List<String> symbolsInOrder = databaseManager.getSymbolsWithOhlcvData()
        def testSymbols = symbolsInOrder.findAll { it.startsWith("INT_") && (it.contains("HIGH") || it.contains("LOW")) }

        then: "symbols should be ordered by market cap (descending)"
        testSymbols.size() == 4
        testSymbols[0] == "INT_B_HIGH"  // $200B (highest)
        testSymbols[1] == "INT_A_HIGH"  // $100B
        testSymbols[2] == "INT_B_LOW"   // $10B
        testSymbols[3] == "INT_A_LOW"   // $5B (lowest)

        when: "retrieving DMI results for identical data pairs"
        List<OHLCV> resultsAHigh = databaseManager.getOHLCVData("INT_A_HIGH", startDate, startDate.plusDays(39))
        List<OHLCV> resultsALow = databaseManager.getOHLCVData("INT_A_LOW", startDate, startDate.plusDays(39))
        List<OHLCV> resultsBHigh = databaseManager.getOHLCVData("INT_B_HIGH", startDate, startDate.plusDays(39))
        List<OHLCV> resultsBLow = databaseManager.getOHLCVData("INT_B_LOW", startDate, startDate.plusDays(39))

        then: "DMI calculations should be identical for pairs with identical OHLCV data"
        resultsAHigh.size() == resultsALow.size()
        resultsBHigh.size() == resultsBLow.size()
        
        // Compare DMI values for identical data pairs
        for (int i = 0; i < resultsAHigh.size(); i++) {
            def highRecord = resultsAHigh[i]
            def lowRecord = resultsALow[i]
            
            // DMI values should be identical (within floating point precision)
            if (highRecord.dmiPlusDi != null && lowRecord.dmiPlusDi != null) {
                Math.abs(highRecord.dmiPlusDi - lowRecord.dmiPlusDi) < 1e-10
            }
            if (highRecord.dmiMinusDi != null && lowRecord.dmiMinusDi != null) {
                Math.abs(highRecord.dmiMinusDi - lowRecord.dmiMinusDi) < 1e-10
            }
            if (highRecord.dmiAdx != null && lowRecord.dmiAdx != null) {
                Math.abs(highRecord.dmiAdx - lowRecord.dmiAdx) < 1e-10
            }
        }
    }

    def "should handle mixed calculation methods with market cap ordering"() {
        given: "instruments with market cap data"
        databaseManager.saveInstrumentWithDetails("INT_HYBRID_A", "Hybrid Test A", "US_STOCK", 
                                                  new BigDecimal("300000000000"), "United States", 2000, "Technology", "Software")
        databaseManager.saveInstrumentWithDetails("INT_HYBRID_B", "Hybrid Test B", "US_STOCK", 
                                                  new BigDecimal("150000000000"), "United States", 2005, "Technology", "Software")

        and: "OHLCV data for both symbols"
        LocalDate startDate = LocalDate.now().minusDays(35)
        List<OHLCV> dataA = createRealisticOHLCVData("INT_HYBRID_A", 35, startDate)
        List<OHLCV> dataB = createRealisticOHLCVData("INT_HYBRID_B", 35, startDate)
        databaseManager.saveOHLCVData(dataA)
        databaseManager.saveOHLCVData(dataB)

        when: "performing DMI calculation with hybrid method"
        DMIRequest request = new DMIRequest()
        request.period = 14
        request.calculationMode = DMIRequest.CalculationMode.FULL_RECALCULATION
        request.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        request.dryRun = false
        
        DMIResponse response = dmiService.calculateDMI(request)

        then: "calculation should be successful"
        response.status == "success"
        response.processedSymbols == 2
        response.parameters.calculationMethod == DMIRequest.CalculationMethod.HYBRID_SQL_JAVA

        when: "checking symbol order"
        List<String> symbolsInOrder = databaseManager.getSymbolsWithOhlcvData()
        def hybridSymbols = symbolsInOrder.findAll { it.startsWith("INT_HYBRID_") }

        then: "symbols should still be ordered by market cap"
        hybridSymbols.size() == 2
        hybridSymbols[0] == "INT_HYBRID_A"  // $300B (higher)
        hybridSymbols[1] == "INT_HYBRID_B"  // $150B (lower)

        and: "both symbols should have valid DMI data"
        hybridSymbols.each { symbol ->
            List<OHLCV> results = databaseManager.getOHLCVData(symbol, startDate, startDate.plusDays(34))
            def recordsWithDMI = results.findAll { it.dmiPlusDi != null && it.dmiMinusDi != null && it.dmiAdx != null }
            assert recordsWithDMI.size() >= 7 // Should have DMI values for recent dates (35 days - 28 minimum = 7)
        }
    }

    /**
     * Create realistic OHLCV data with proper price movements for DMI calculation.
     */
    private List<OHLCV> createRealisticOHLCVData(String symbol, int days, LocalDate startDate) {
        List<OHLCV> data = []
        double basePrice = 100.0
        Random random = new Random(symbol.hashCode()) // Deterministic based on symbol

        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements with trends and volatility
            double change = (random.nextGaussian() * 0.02) // 2% daily volatility
            double open = basePrice * (1 + change)
            double close = open * (1 + (random.nextGaussian() * 0.015)) // 1.5% intraday volatility
            double high = Math.max(open, close) * (1 + Math.abs(random.nextGaussian() * 0.01))
            double low = Math.min(open, close) * (1 - Math.abs(random.nextGaussian() * 0.01))
            long volume = 1000000L + (long)(random.nextGaussian() * 200000)

            data.add(new OHLCV(symbol, date, open, high, low, close, Math.max(volume, 100000L)))
            basePrice = close // Use close as next day's base
        }

        return data
    }
}
