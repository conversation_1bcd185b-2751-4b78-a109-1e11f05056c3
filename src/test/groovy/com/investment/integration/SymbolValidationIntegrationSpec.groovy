package com.investment.integration

import com.investment.database.DatabaseManager
import com.investment.service.SymbolValidationService
import spock.lang.Specification
import spock.lang.Subject

/**
 * Integration test for symbol validation functionality.
 * Tests the complete flow from database to SEC validation.
 */
class SymbolValidationIntegrationSpec extends Specification {

    @Subject
    SymbolValidationService symbolValidationService

    DatabaseManager databaseManager
    String testDbUrl = "******************************************"

    def setup() {
        // Clean up any existing test database
        new File("./data/integration_test.duckdb").delete()
        
        // Setup test database
        DatabaseManager.setDbUrl(testDbUrl)
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
        
        // Create service
        symbolValidationService = new SymbolValidationService(databaseManager)
    }

    def cleanup() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/integration_test.duckdb").delete()
    }

    def "should validate symbols against SEC data in dry run mode"() {
        given: "a database with some test symbols"
        // Add some valid symbols (these should exist in SEC data)
        databaseManager.saveInstrument("AAPL", "Apple Inc.", "US_STOCK")
        databaseManager.saveInstrument("MSFT", "Microsoft Corporation", "US_STOCK")
        
        // Add some likely invalid symbols
        databaseManager.saveInstrument("INVALID123", "Invalid Company", "US_STOCK")
        databaseManager.saveInstrument("FAKE456", "Fake Company", "US_STOCK")

        when: "validating symbols in dry run mode"
        def response = symbolValidationService.validateSymbols(true, false)

        then: "should return validation results"
        response != null
        response.totalSymbolsInDatabase == 4
        response.dryRun == true
        response.deletedSymbols == 0  // No actual deletions in dry run
        response.summary.contains("DRY RUN")
        
        and: "should identify some invalid symbols"
        response.invalidSymbols >= 2  // At least our fake symbols should be invalid
        response.invalidSymbolsList.contains("INVALID123")
        response.invalidSymbolsList.contains("FAKE456")
    }

    def "should get cache status information"() {
        when: "getting cache status"
        def status = symbolValidationService.getCacheStatus()

        then: "should return cache information"
        status != null
        status.containsKey("cacheDirectory")
        status.containsKey("cacheFile")
        status.containsKey("cacheExpiry")
        status.containsKey("lastCacheUpdate")
        status.containsKey("cachedTickersCount")
        status.containsKey("cacheFileExists")
        
        status.cacheDirectory == "./data/sec_cache"
        status.cacheFile == "company_tickers.json"
    }

    def "should handle empty database gracefully"() {
        when: "validating symbols on empty database"
        def response = symbolValidationService.validateSymbols(true, false)

        then: "should handle empty case gracefully"
        response != null
        response.totalSymbolsInDatabase == 0
        response.validSymbols == 0
        response.invalidSymbols == 0
        response.invalidSymbolsList.isEmpty()
        response.deletedSymbols == 0
        response.deletedOhlcvRecords == 0
        response.dryRun == true
    }
}
