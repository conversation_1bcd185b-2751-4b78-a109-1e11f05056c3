package com.investment.integration

import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.DMIService
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * End-to-end test for DMI functionality demonstrating real-world usage.
 */
class DMIEndToEndSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    DMIService dmiService

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/dmi_e2e_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("**************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
        dmiService = new DMIService(databaseManager)
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_e2e_test.duckdb").delete()
    }

    def "should demonstrate complete DMI workflow from data ingestion to calculation"() {
        given: "a symbol with realistic historical data"
        String symbol = "DEMO_STOCK"
        databaseManager.saveInstrument(symbol, "Demo Stock Company", "Technology")
        
        and: "40 days of realistic OHLCV data"
        List<OHLCV> historicalData = createRealisticTestData(symbol, 40)
        databaseManager.saveOHLCVData(historicalData)

        when: "performing DMI calculation with default parameters"
        DMIRequest request = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        DMIResponse response = dmiService.calculateDMI(request)

        then: "calculation should complete successfully"
        response.status == "success"
        response.processedSymbols == 1
        response.totalRecordsUpdated > 0
        response.dryRun == false
        response.parameters.period == 14
        response.parameters.calculationMode == DMIRequest.CalculationMode.FULL_RECALCULATION

        when: "retrieving the calculated data"
        List<OHLCV> calculatedData = databaseManager.getOHLCVData(symbol, 
                                                                  LocalDate.now().minusDays(45), 
                                                                  LocalDate.now())

        then: "should have DMI values for appropriate records"
        calculatedData.size() == 40
        def recordsWithDMI = calculatedData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() >= 10  // Should have DMI for at least the last 10+ records
        
        and: "DMI values should be mathematically valid"
        recordsWithDMI.every { record ->
            record.dmiPlusDi >= 0 && record.dmiPlusDi <= 100 &&
            record.dmiMinusDi >= 0 && record.dmiMinusDi <= 100 &&
            (record.dmiAdx == null || (record.dmiAdx >= 0 && record.dmiAdx <= 100))
        }
        
        and: "should have some ADX values for the most recent records"
        def recordsWithADX = calculatedData.findAll { it.dmiAdx != null }
        recordsWithADX.size() >= 5  // Should have ADX for at least the last few records
    }

    def "should handle dry run mode correctly"() {
        given: "a symbol with data"
        String symbol = "DRY_RUN_DEMO"
        databaseManager.saveInstrument(symbol, "Dry Run Demo Company", "Finance")
        
        and: "sufficient historical data"
        List<OHLCV> testData = createRealisticTestData(symbol, 35)
        databaseManager.saveOHLCVData(testData)

        when: "performing dry run calculation"
        DMIRequest dryRunRequest = new DMIRequest(14, true, DMIRequest.CalculationMode.FULL_RECALCULATION)
        DMIResponse dryRunResponse = dmiService.calculateDMI(dryRunRequest)

        then: "dry run should complete without updating data"
        dryRunResponse.status == "success"
        dryRunResponse.dryRun == true
        dryRunResponse.totalRecordsUpdated > 0  // Should report what would be updated
        dryRunResponse.processedSymbols == 1

        when: "checking that no actual data was updated"
        List<OHLCV> dataAfterDryRun = databaseManager.getOHLCVData(symbol, 
                                                                   LocalDate.now().minusDays(40), 
                                                                   LocalDate.now())

        then: "should have no DMI data"
        dataAfterDryRun.every { it.dmiPlusDi == null && it.dmiMinusDi == null && it.dmiAdx == null }

        when: "performing actual calculation"
        DMIRequest actualRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        DMIResponse actualResponse = dmiService.calculateDMI(actualRequest)

        then: "actual calculation should update data"
        actualResponse.status == "success"
        actualResponse.dryRun == false
        actualResponse.totalRecordsUpdated > 0
        
        when: "checking that data was actually updated"
        List<OHLCV> dataAfterActual = databaseManager.getOHLCVData(symbol, 
                                                                   LocalDate.now().minusDays(40), 
                                                                   LocalDate.now())

        then: "should have DMI data"
        def recordsWithDMI = dataAfterActual.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() > 0
    }

    def "should handle insufficient data gracefully"() {
        given: "a symbol with minimal data"
        String symbol = "MINIMAL_DATA"
        databaseManager.saveInstrument(symbol, "Minimal Data Company", "Technology")
        
        and: "only 15 days of data (insufficient for 14-day DMI with 28 minimum)"
        List<OHLCV> minimalData = createRealisticTestData(symbol, 15)
        databaseManager.saveOHLCVData(minimalData)

        when: "attempting to calculate DMI"
        DMIRequest request = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        DMIResponse response = dmiService.calculateDMI(request)

        then: "should report insufficient data"
        response.symbolsWithInsufficientData.contains(symbol)
        response.processedSymbols == 0
        response.status == "success"  // Still success, just no symbols processed
    }

    def "should validate DMI calculation accuracy with known data patterns"() {
        given: "a symbol with trending upward data"
        String symbol = "TRENDING_UP"
        databaseManager.saveInstrument(symbol, "Trending Up Company", "Technology")
        
        and: "data with clear upward trend"
        List<OHLCV> trendingData = createUpwardTrendingData(symbol, 35)
        databaseManager.saveOHLCVData(trendingData)

        when: "calculating DMI"
        DMIRequest request = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        dmiService.calculateDMI(request)

        and: "retrieving calculated data"
        List<OHLCV> calculatedData = databaseManager.getOHLCVData(symbol, 
                                                                  LocalDate.now().minusDays(40), 
                                                                  LocalDate.now())

        then: "DMI values should reflect the upward trend"
        def recordsWithDMI = calculatedData.findAll { it.dmiPlusDi != null }
        recordsWithDMI.size() > 0
        
        and: "for upward trending data, +DI should generally be higher than -DI"
        def recentRecords = recordsWithDMI.takeRight(Math.min(5, recordsWithDMI.size()))
        if (recentRecords.size() > 0) {
            def avgPlusDI = recentRecords.collect { it.dmiPlusDi }.sum() / recentRecords.size()
            def avgMinusDI = recentRecords.collect { it.dmiMinusDi }.sum() / recentRecords.size()
            avgPlusDI > avgMinusDI
        }
        
        and: "ADX should be present for recent records and within reasonable range"
        def recordsWithADX = recentRecords.findAll { it.dmiAdx != null }
        if (recordsWithADX.size() > 0) {
            recordsWithADX.every { it.dmiAdx > 0 && it.dmiAdx < 80 }
        }
    }

    def "should support different calculation periods"() {
        given: "a symbol with sufficient data"
        String symbol = "PERIOD_TEST"
        databaseManager.saveInstrument(symbol, "Period Test Company", "Finance")
        
        and: "60 days of historical data"
        List<OHLCV> testData = createRealisticTestData(symbol, 60)
        databaseManager.saveOHLCVData(testData)

        when: "calculating DMI with 7-day period"
        DMIRequest request7 = new DMIRequest(7, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        request7.minDataPoints = 20  // Adjust minimum for shorter period
        DMIResponse response7 = dmiService.calculateDMI(request7)

        then: "7-day calculation should complete"
        response7.status == "success"
        response7.parameters.period == 7
        response7.processedSymbols == 1

        when: "calculating DMI with 21-day period"
        databaseManager.clearDMIData(symbol)
        DMIRequest request21 = new DMIRequest(21, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        request21.minDataPoints = 50  // Adjust minimum for longer period
        DMIResponse response21 = dmiService.calculateDMI(request21)

        then: "21-day calculation should complete"
        response21.status == "success"
        response21.parameters.period == 21
        response21.processedSymbols == 1

        and: "both calculations should produce valid results"
        response7.totalRecordsUpdated > 0
        response21.totalRecordsUpdated > 0
    }

    // Helper methods for creating test data
    private List<OHLCV> createRealisticTestData(String symbol, int days) {
        List<OHLCV> data = []
        double basePrice = 100.0
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i)
            double volatility = 0.02 + Math.random() * 0.03  // 2-5% daily volatility
            double open = basePrice * (1 + (Math.random() - 0.5) * volatility)
            double close = open * (1 + (Math.random() - 0.5) * volatility)
            double high = Math.max(open, close) * (1 + Math.random() * volatility * 0.5)
            double low = Math.min(open, close) * (1 - Math.random() * volatility * 0.5)
            long volume = (long) (1000000 + Math.random() * 2000000)
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            basePrice = close // Use previous close as base for next day
        }
        
        return data
    }

    private List<OHLCV> createUpwardTrendingData(String symbol, int days) {
        List<OHLCV> data = []
        double basePrice = 100.0
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i)
            double trend = (days - i) * 0.3  // Gradual upward trend
            double volatility = 0.015  // Lower volatility for clearer trend
            double open = basePrice + trend + (Math.random() - 0.5) * volatility * basePrice
            double close = open + trend * 0.1 + (Math.random() - 0.3) * volatility * basePrice  // Upward bias
            double high = Math.max(open, close) + Math.random() * volatility * basePrice * 0.5
            double low = Math.min(open, close) - Math.random() * volatility * basePrice * 0.3
            long volume = (long) (1500000 + Math.random() * 1000000)
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            basePrice = close
        }
        
        return data
    }
}
