package com.investment.integration

import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.DMIService
import com.investment.process.AsyncProcessExecutor
import com.investment.process.ProcessManager
import spock.lang.Shared
import spock.lang.Specification

import java.time.LocalDate

/**
 * Integration test specifically for verifying the DMI incremental calculation fix.
 * This test reproduces the original "insufficient data" issue and verifies the fix.
 */
class DMIIncrementalFixSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    DMIService dmiService

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/dmi_incremental_fix_test.duckdb").delete()

        // Initialize test database
        DatabaseManager.setDbUrl("**************************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()

        // Create mock process executor for testing
        ProcessManager processManager = new ProcessManager()
        AsyncProcessExecutor processExecutor = new AsyncProcessExecutor(processManager)

        dmiService = new DMIService(databaseManager, processExecutor)
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_incremental_fix_test.duckdb").delete()
    }

    def "should fix incremental DMI calculation for symbols with sufficient data"() {
        given: "a symbol with plenty of historical data (1000+ records)"
        String symbol = "AAPL_FIX_TEST"
        databaseManager.saveInstrument(symbol, "Apple Inc Fix Test", "Technology")
        
        and: "1000 days of historical OHLCV data"
        List<OHLCV> historicalData = createLargeHistoricalDataset(symbol, 1000)
        databaseManager.saveOHLCVData(historicalData)
        
        and: "verify we have sufficient data"
        int totalRecords = databaseManager.countOhlcvRecords(symbol)
        assert totalRecords >= 1000

        when: "performing initial full DMI calculation"
        DMIRequest initialRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        initialRequest.symbols = [symbol]
        DMIResponse initialResponse = dmiService.calculateDMI(initialRequest)

        then: "initial calculation should succeed"
        initialResponse.status == "success"
        initialResponse.processedSymbols == 1
        initialResponse.totalRecordsUpdated > 0
        !initialResponse.symbolsWithInsufficientData.contains(symbol)

        when: "adding new data and performing incremental calculation"
        List<OHLCV> newData = createAdditionalData(symbol, historicalData.last().date.plusDays(1), 10)
        databaseManager.saveOHLCVData(newData)
        
        and: "performing incremental DMI calculation (this was failing before the fix)"
        DMIRequest incrementalRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.INCREMENTAL)
        incrementalRequest.symbols = [symbol]
        DMIResponse incrementalResponse = dmiService.calculateDMI(incrementalRequest)

        then: "incremental calculation should succeed without 'insufficient data' errors"
        incrementalResponse.status == "success"
        incrementalResponse.processedSymbols == 1
        incrementalResponse.totalRecordsUpdated > 0
        !incrementalResponse.symbolsWithInsufficientData.contains(symbol)
        
        and: "should not report insufficient data even though we have 1000+ records"
        incrementalResponse.symbolsWithInsufficientData.isEmpty()
    }

    def "should handle hybrid incremental DMI calculation correctly"() {
        given: "a symbol with plenty of historical data"
        String symbol = "MSFT_HYBRID_FIX"
        databaseManager.saveInstrument(symbol, "Microsoft Hybrid Fix Test", "Technology")
        
        and: "500 days of historical OHLCV data"
        List<OHLCV> historicalData = createLargeHistoricalDataset(symbol, 500)
        databaseManager.saveOHLCVData(historicalData)

        when: "performing initial full hybrid DMI calculation"
        DMIRequest initialRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        initialRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        initialRequest.symbols = [symbol]
        DMIResponse initialResponse = dmiService.calculateDMI(initialRequest)

        then: "initial calculation should succeed"
        initialResponse.status == "success"
        initialResponse.processedSymbols == 1

        when: "adding new data and performing incremental hybrid calculation"
        List<OHLCV> newData = createAdditionalData(symbol, historicalData.last().date.plusDays(1), 5)
        databaseManager.saveOHLCVData(newData)
        
        and: "performing incremental hybrid DMI calculation"
        DMIRequest incrementalRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.INCREMENTAL)
        incrementalRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        incrementalRequest.symbols = [symbol]
        DMIResponse incrementalResponse = dmiService.calculateDMI(incrementalRequest)

        then: "incremental hybrid calculation should succeed"
        incrementalResponse.status == "success"
        incrementalResponse.processedSymbols == 1
        !incrementalResponse.symbolsWithInsufficientData.contains(symbol)
    }

    def "should demonstrate the fix with realistic scenario"() {
        given: "a symbol representing the original problem scenario"
        String symbol = "ORIGINAL_ISSUE"
        databaseManager.saveInstrument(symbol, "Original Issue Test", "Technology")
        
        and: "exactly 1000 records as mentioned in the issue"
        List<OHLCV> historicalData = createLargeHistoricalDataset(symbol, 1000)
        databaseManager.saveOHLCVData(historicalData)
        
        and: "verify record count matches the issue description"
        int recordCount = databaseManager.countOhlcvRecords(symbol)
        assert recordCount == 1000

        when: "performing full calculation first"
        DMIRequest fullRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.FULL_RECALCULATION)
        fullRequest.symbols = [symbol]
        dmiService.calculateDMI(fullRequest)

        and: "then performing incremental calculation (the problematic scenario)"
        DMIRequest incrementalRequest = new DMIRequest(14, false, DMIRequest.CalculationMode.INCREMENTAL)
        incrementalRequest.symbols = [symbol]
        DMIResponse response = dmiService.calculateDMI(incrementalRequest)

        then: "should NOT report 'Insufficient data for DMI calculation' error"
        response.status == "success"
        !response.symbolsWithInsufficientData.contains(symbol)
        
        and: "should process the symbol successfully"
        response.processedSymbols >= 0  // May be 0 if no new data to process, but shouldn't fail
    }

    // Helper methods
    private List<OHLCV> createLargeHistoricalDataset(String symbol, int days) {
        List<OHLCV> data = []
        LocalDate startDate = LocalDate.now().minusDays(days)
        double basePrice = 150.0
        
        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements
            double dailyChange = (Math.random() - 0.5) * 4.0 // +/- 2% daily change
            basePrice = Math.max(basePrice + dailyChange, 10.0) // Minimum price of $10
            
            double open = basePrice + (Math.random() - 0.5) * 1.0
            double close = basePrice + (Math.random() - 0.5) * 1.0
            double high = Math.max(open, close) + Math.random() * 2.0
            double low = Math.min(open, close) - Math.random() * 2.0
            long volume = (long) (1000000 + Math.random() * 5000000)
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
        }
        
        return data
    }
    
    private List<OHLCV> createAdditionalData(String symbol, LocalDate startDate, int days) {
        List<OHLCV> data = []
        double basePrice = 150.0
        
        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            double dailyChange = (Math.random() - 0.5) * 4.0
            basePrice = Math.max(basePrice + dailyChange, 10.0)
            
            double open = basePrice + (Math.random() - 0.5) * 1.0
            double close = basePrice + (Math.random() - 0.5) * 1.0
            double high = Math.max(open, close) + Math.random() * 2.0
            double low = Math.min(open, close) - Math.random() * 2.0
            long volume = (long) (1000000 + Math.random() * 5000000)
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
        }
        
        return data
    }
}
