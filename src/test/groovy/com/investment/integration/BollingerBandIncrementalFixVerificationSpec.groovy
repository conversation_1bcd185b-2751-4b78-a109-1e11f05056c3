package com.investment.integration

import com.investment.api.model.BollingerBandRequest
import com.investment.api.model.BollingerBandResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.BollingerBandService
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Integration test to verify that the INCREMENTAL mode fix produces identical results to FULL_RECALCULATION mode.
 * This test specifically addresses the issue where INCREMENTAL mode was producing incorrect Bollinger Band values
 * due to limited historical context in window function calculations.
 */
class BollingerBandIncrementalFixVerificationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    BollingerBandService bollingerBandService

    def setupSpec() {
        try {
            // Clean up any existing test database
            new File("./data/bb_incremental_fix_verification_test.duckdb").delete()

            // Initialize test database
            DatabaseManager.setDbUrl("**************************************************************")
            databaseManager = new DatabaseManager()
            databaseManager.initDatabase()
            bollingerBandService = new BollingerBandService(databaseManager)

            // Create test data - we need real MSFT data for this test
            setupTestData()
        } catch (Exception e) {
            println "Setup failed: ${e.message}"
            throw e
        }
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/bb_incremental_fix_verification_test.duckdb").delete()
    }

    def "INCREMENTAL mode should produce identical results to FULL_RECALCULATION mode"() {
        given: "MSFT symbol with existing data"
        def symbol = "MSFT"

        // Clean up any existing Bollinger Band data first
        databaseManager.clearBollingerBandData(symbol)

        when: "performing FULL_RECALCULATION first"
        def fullRequest = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.FULL_RECALCULATION)
        fullRequest.symbols = [symbol]
        
        BollingerBandResponse fullResponse = bollingerBandService.calculateBollingerBands(fullRequest)
        
        then: "full calculation should succeed"
        fullResponse.status == "success"
        fullResponse.processedSymbols == 1
        fullResponse.totalRecordsUpdated > 0
        
        when: "getting the results from FULL_RECALCULATION"
        def fullCalculationData = getRecentOHLCVData(symbol, 10)
        println "Full calculation results (last 10 records):"
        fullCalculationData.each { ohlcv ->
            println "  ${ohlcv.date}: bb_middle=${String.format('%.6f', ohlcv.bbMiddleBand)}, bb_std_dev=${String.format('%.6f', ohlcv.bbStdDev)}, bb_upper=${String.format('%.6f', ohlcv.bbUpperBand)}, bb_lower=${String.format('%.6f', ohlcv.bbLowerBand)}"
        }
        
        and: "clearing Bollinger Band data and recalculating with FULL_RECALCULATION again"
        databaseManager.clearBollingerBandData(symbol)
        BollingerBandResponse fullResponse2 = bollingerBandService.calculateBollingerBands(fullRequest)
        
        then: "second full calculation should produce identical results"
        fullResponse2.status == "success"
        fullResponse2.processedSymbols == 1
        
        when: "performing INCREMENTAL calculation"
        def incrementalRequest = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        incrementalRequest.symbols = [symbol]
        
        BollingerBandResponse incrementalResponse = bollingerBandService.calculateBollingerBands(incrementalRequest)
        
        then: "incremental calculation should succeed"
        incrementalResponse.status == "success"
        incrementalResponse.processedSymbols == 1
        incrementalResponse.totalRecordsUpdated > 0
        
        when: "getting the results from INCREMENTAL calculation"
        def incrementalCalculationData = getRecentOHLCVData(symbol, 10)
        println "Incremental calculation results (last 10 records):"
        incrementalCalculationData.each { ohlcv ->
            println "  ${ohlcv.date}: bb_middle=${String.format('%.6f', ohlcv.bbMiddleBand)}, bb_std_dev=${String.format('%.6f', ohlcv.bbStdDev)}, bb_upper=${String.format('%.6f', ohlcv.bbUpperBand)}, bb_lower=${String.format('%.6f', ohlcv.bbLowerBand)}"
        }
        
        then: "INCREMENTAL results should match FULL_RECALCULATION results exactly"
        fullCalculationData.size() == incrementalCalculationData.size()
        
        // Compare each record with high precision
        for (int i = 0; i < fullCalculationData.size(); i++) {
            def fullRecord = fullCalculationData[i]
            def incrementalRecord = incrementalCalculationData[i]
            
            assert fullRecord.date == incrementalRecord.date
            assert Math.abs(fullRecord.bbMiddleBand - incrementalRecord.bbMiddleBand) < 0.000001 : 
                "bb_middle_band mismatch for ${fullRecord.date}: full=${fullRecord.bbMiddleBand}, incremental=${incrementalRecord.bbMiddleBand}"
            assert Math.abs(fullRecord.bbStdDev - incrementalRecord.bbStdDev) < 0.000001 : 
                "bb_std_dev mismatch for ${fullRecord.date}: full=${fullRecord.bbStdDev}, incremental=${incrementalRecord.bbStdDev}"
            assert Math.abs(fullRecord.bbUpperBand - incrementalRecord.bbUpperBand) < 0.000001 : 
                "bb_upper_band mismatch for ${fullRecord.date}: full=${fullRecord.bbUpperBand}, incremental=${incrementalRecord.bbUpperBand}"
            assert Math.abs(fullRecord.bbLowerBand - incrementalRecord.bbLowerBand) < 0.000001 : 
                "bb_lower_band mismatch for ${fullRecord.date}: full=${fullRecord.bbLowerBand}, incremental=${incrementalRecord.bbLowerBand}"
        }
        
        println "✅ INCREMENTAL mode fix verified: All values match FULL_RECALCULATION mode exactly!"
    }

    def "INCREMENTAL mode should only update records from N-1 date onwards"() {
        given: "MSFT symbol with existing Bollinger Band data"
        def symbol = "MSFT"

        // Clean up any existing Bollinger Band data first
        databaseManager.clearBollingerBandData(symbol)

        // Perform full calculation first
        def fullRequest = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.FULL_RECALCULATION)
        fullRequest.symbols = [symbol]
        bollingerBandService.calculateBollingerBands(fullRequest)
        
        when: "getting the N-1 reference date"
        def lastCalculatedDate = databaseManager.getLastBollingerBandCalculationDate(symbol)
        println "N-1 reference date: ${lastCalculatedDate}"
        
        and: "modifying a record before the N-1 date to detect if it gets recalculated"
        def testDate = lastCalculatedDate.minusDays(2) // N-3 date (should NOT be recalculated)
        def originalValue = setTestBollingerBandValue(symbol, testDate, 999.999)
        println "Set test value 999.999 for date ${testDate} (should NOT be recalculated)"
        
        and: "performing INCREMENTAL calculation"
        def incrementalRequest = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        incrementalRequest.symbols = [symbol]
        bollingerBandService.calculateBollingerBands(incrementalRequest)
        
        then: "the test value should remain unchanged (proving only N-1 onwards were updated)"
        def testValue = getBollingerBandValue(symbol, testDate)
        testValue == 999.999
        println "✅ Test value preserved: ${testValue} (INCREMENTAL mode correctly only updated from N-1 onwards)"
    }

    /**
     * Get recent OHLCV data with Bollinger Bands for testing
     */
    private List<OHLCV> getRecentOHLCVData(String symbol, int count) {
        def endDate = LocalDate.now()
        def startDate = endDate.minusDays(count + 10) // Get extra days to ensure we have enough data

        def data = databaseManager.getOHLCVData(symbol, startDate, endDate)
        return data.findAll { it.bbMiddleBand != null }
                  .sort { it.date }
                  .takeRight(count)
    }

    /**
     * Set a test Bollinger Band value for a specific date and return the original value
     */
    private Double setTestBollingerBandValue(String symbol, LocalDate date, double testValue) {
        def sql = "SELECT bb_middle_band FROM ohlcv WHERE symbol = ? AND date = ?"
        def originalValue = null

        try {
            def pstmt = databaseManager.connection.prepareStatement(sql)
            pstmt.setString(1, symbol)
            pstmt.setDate(2, java.sql.Date.valueOf(date))
            def rs = pstmt.executeQuery()
            if (rs.next()) {
                originalValue = rs.getDouble("bb_middle_band")
            }
            rs.close()
            pstmt.close()
        } catch (Exception e) {
            println "Error getting original value: ${e.message}"
        }

        def updateSql = "UPDATE ohlcv SET bb_middle_band = ? WHERE symbol = ? AND date = ?"
        try {
            def pstmt = databaseManager.connection.prepareStatement(updateSql)
            pstmt.setDouble(1, testValue)
            pstmt.setString(2, symbol)
            pstmt.setDate(3, java.sql.Date.valueOf(date))
            pstmt.executeUpdate()
            pstmt.close()
        } catch (Exception e) {
            println "Error setting test value: ${e.message}"
        }

        return originalValue
    }

    /**
     * Get Bollinger Band middle band value for a specific date
     */
    private Double getBollingerBandValue(String symbol, LocalDate date) {
        def sql = "SELECT bb_middle_band FROM ohlcv WHERE symbol = ? AND date = ?"
        try {
            def pstmt = databaseManager.connection.prepareStatement(sql)
            pstmt.setString(1, symbol)
            pstmt.setDate(2, java.sql.Date.valueOf(date))
            def rs = pstmt.executeQuery()
            def value = null
            if (rs.next()) {
                value = rs.getDouble("bb_middle_band")
            }
            rs.close()
            pstmt.close()
            return value
        } catch (Exception e) {
            println "Error getting Bollinger Band value: ${e.message}"
            return null
        }
    }

    /**
     * Setup test data with realistic OHLCV data for MSFT
     */
    private void setupTestData() {
        // Create test instrument
        databaseManager.saveInstrument("MSFT", "Microsoft Corporation", "Technology")

        // Create realistic OHLCV data for MSFT for the past 60 days
        LocalDate startDate = LocalDate.now().minusDays(60)
        List<OHLCV> testData = []

        // Generate realistic price data with some volatility
        double basePrice = 400.0
        Random random = new Random(12345) // Fixed seed for reproducible tests

        for (int i = 0; i < 50; i++) {
            LocalDate date = startDate.plusDays(i)

            // Add some realistic price movement
            double priceChange = (random.nextGaussian() * 5.0) // Standard deviation of $5
            double open = basePrice + priceChange
            double close = open + (random.nextGaussian() * 3.0) // Intraday movement
            double high = Math.max(open, close) + Math.abs(random.nextGaussian() * 2.0)
            double low = Math.min(open, close) - Math.abs(random.nextGaussian() * 2.0)
            long volume = 15000000L + (long)(random.nextGaussian() * 5000000L)

            testData.add(new OHLCV("MSFT", date, open, high, low, close, Math.max(volume, 1000000L)))

            // Update base price for next day (trend)
            basePrice = close
        }

        databaseManager.saveOHLCVData(testData)
        println "Created ${testData.size()} OHLCV records for MSFT"
    }
}
