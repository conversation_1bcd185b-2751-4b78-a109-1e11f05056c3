package com.investment.integration

import com.investment.database.DatabaseManager
import com.investment.model.Position
import com.investment.service.PositionsService
import com.investment.service.RiskManagementService
import com.investment.service.OHLCVService
import spock.lang.Specification
import spock.lang.Shared

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Integration test for enhanced risk management system.
 * Tests the complete flow from position creation to risk management calculations.
 */
class EnhancedRiskManagementIntegrationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    PositionsService positionsService

    @Shared
    RiskManagementService riskManagementService

    @Shared
    OHLCVService ohlcvService

    def setupSpec() {
        // Initialize database manager with in-memory database for testing
        databaseManager = new DatabaseManager("jdbc:duckdb::memory:", "", "")
        databaseManager.initializeDatabase()

        // Initialize services
        ohlcvService = new OHLCVService()
        ohlcvService.dbManager = databaseManager

        riskManagementService = new RiskManagementService()
        riskManagementService.databaseManager = databaseManager

        positionsService = new PositionsService(databaseManager, ohlcvService, riskManagementService)

        // Create test instrument
        databaseManager.createInstrument("AAPL", "Apple Inc.", "US_STOCK", null, null, null)
    }

    def cleanupSpec() {
        if (databaseManager != null) {
            databaseManager.close()
        }
    }

    def "should create position with enhanced risk management and calculate effective stop values"() {
        given: "a new position request"
        def createRequest = new com.investment.api.model.CreatePositionRequest()
        createRequest.symbol = "AAPL"
        createRequest.position = new BigDecimal("100")
        createRequest.side = Position.Side.BUY
        createRequest.tradePrice = new BigDecimal("150.00")
        createRequest.riskUnit = new BigDecimal("1000.00")
        createRequest.stopPercent = new BigDecimal("0.05") // 5% fallback
        createRequest.bbmbAdjPercent = new BigDecimal("0.02") // 2% BB adjustment

        when: "creating the position"
        def position = positionsService.createPosition(createRequest)

        then: "position should be created successfully"
        position != null
        position.id != null
        position.symbol == "AAPL"
        position.status == Position.Status.OPEN

        when: "initializing enhanced risk management"
        def aggressivePercent = new BigDecimal("0.06") // 6% aggressive
        def conservativePercent = new BigDecimal("0.03") // 3% conservative
        def enhancedPosition = positionsService.initializeEnhancedRiskManagement(
            position.id, aggressivePercent, conservativePercent)

        then: "enhanced risk parameters should be set"
        enhancedPosition.aggressiveStopPercent == aggressivePercent
        enhancedPosition.conservativeStopPercent == conservativePercent
        enhancedPosition.riskMode == Position.RiskMode.AGGRESSIVE // Initial mode

        when: "updating position with market price"
        def currentPrice = new BigDecimal("155.00")
        def updatedPosition = positionsService.updatePositionPrice(position.id, currentPrice)

        then: "position should be updated with enhanced risk calculations"
        updatedPosition.lastPrice == currentPrice
        updatedPosition.highestAfterTrade == currentPrice // Should track highest price
        updatedPosition.effectiveStopValue != null
        updatedPosition.riskMode != null

        and: "should use aggressive mode for new position (within initial 2-day period)"
        updatedPosition.riskMode == Position.RiskMode.AGGRESSIVE
        // Aggressive stop: 155 * (1 - 0.06) = 145.70
        updatedPosition.effectiveStopValue.compareTo(new BigDecimal("145.70")) == 0
    }

    def "should handle database schema migration for enhanced risk management fields"() {
        when: "checking database schema version"
        def currentVersion = databaseManager.getCurrentSchemaVersion()

        then: "should be at version 10 or higher (includes enhanced risk management columns)"
        currentVersion >= 10

        when: "querying position with enhanced fields"
        def positions = databaseManager.getPositions(null, null, null)

        then: "should be able to retrieve enhanced risk management fields without errors"
        positions != null
        // The query should not fail even if enhanced fields are null
    }

    def "should persist and retrieve enhanced risk management data correctly"() {
        given: "a position with enhanced risk management data"
        def position = new Position("AAPL", new BigDecimal("50"), Position.Side.BUY, new BigDecimal("160.00"))
        position.setAggressiveStopPercent(new BigDecimal("0.07"))
        position.setConservativeStopPercent(new BigDecimal("0.04"))
        position.setRiskMode(Position.RiskMode.CONSERVATIVE)
        position.setConservativePeriodEndDate(LocalDateTime.now().plusDays(2))

        when: "creating position in database"
        def positionId = databaseManager.createPosition(
            position.symbol,
            position.position,
            position.side.name(),
            position.status.name(),
            position.tradePrice,
            position.tradeValue,
            position.initPortfolioNetValue,
            position.riskUnit,
            position.stopPercent,
            position.bbmbAdjPercent,
            position.aggressiveStopPercent,
            position.conservativeStopPercent,
            position.riskMode.name()
        )

        then: "position should be created with ID"
        positionId != null

        when: "retrieving position from database"
        def retrievedData = databaseManager.getPositionById(positionId)

        then: "enhanced risk management fields should be persisted correctly"
        retrievedData != null
        retrievedData.get("aggressive_stop_percent") == position.aggressiveStopPercent
        retrievedData.get("conservative_stop_percent") == position.conservativeStopPercent
        retrievedData.get("risk_mode") == position.riskMode.name()
        retrievedData.get("conservative_period_end_date") != null
    }

    def "should handle position updates with enhanced risk management fields"() {
        given: "an existing position"
        def createRequest = new com.investment.api.model.CreatePositionRequest()
        createRequest.symbol = "AAPL"
        createRequest.position = new BigDecimal("75")
        createRequest.side = Position.Side.BUY
        createRequest.tradePrice = new BigDecimal("140.00")

        def position = positionsService.createPosition(createRequest)
        def enhancedPosition = positionsService.initializeEnhancedRiskManagement(
            position.id, new BigDecimal("0.05"), new BigDecimal("0.025"))

        when: "updating position with new market data"
        def newPrice = new BigDecimal("145.00")
        def updatedPosition = positionsService.updatePositionPrice(position.id, newPrice)

        then: "enhanced risk management should be applied"
        updatedPosition.lastPrice == newPrice
        updatedPosition.riskMode != null
        updatedPosition.effectiveStopValue != null

        when: "updating position multiple times to simulate market movement"
        def price2 = new BigDecimal("148.00")
        def price3 = new BigDecimal("152.00")
        
        positionsService.updatePositionPrice(position.id, price2)
        def finalPosition = positionsService.updatePositionPrice(position.id, price3)

        then: "highest after trade should be tracked correctly"
        finalPosition.highestAfterTrade == price3
        finalPosition.lastPrice == price3

        and: "effective stop value should be calculated based on highest price"
        // Should use aggressive mode: 152 * (1 - 0.05) = 144.40
        finalPosition.effectiveStopValue.compareTo(new BigDecimal("144.400000")) == 0
    }

    def "should handle error conditions gracefully"() {
        when: "trying to initialize risk management for non-existent position"
        positionsService.initializeEnhancedRiskManagement(
            999999L, new BigDecimal("0.05"), new BigDecimal("0.03"))

        then: "should throw appropriate exception"
        thrown(IllegalArgumentException)

        when: "calculating enhanced stop value with null parameters"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(null)

        then: "should return null gracefully"
        result == null
    }

    def "should support both enhanced and legacy risk management modes"() {
        given: "a position without enhanced risk parameters (legacy mode)"
        def createRequest = new com.investment.api.model.CreatePositionRequest()
        createRequest.symbol = "AAPL"
        createRequest.position = new BigDecimal("100")
        createRequest.side = Position.Side.BUY
        createRequest.tradePrice = new BigDecimal("150.00")
        createRequest.stopPercent = new BigDecimal("0.04") // 4% traditional stop

        def legacyPosition = positionsService.createPosition(createRequest)

        when: "updating position price without enhanced risk management"
        def updatedLegacyPosition = positionsService.updatePositionPrice(
            legacyPosition.id, new BigDecimal("155.00"))

        then: "should fall back to standard risk management"
        updatedLegacyPosition.aggressiveStopPercent == null
        updatedLegacyPosition.conservativeStopPercent == null
        updatedLegacyPosition.riskMode == null
        updatedLegacyPosition.effectiveStopValue != null // Should still calculate using standard logic

        when: "upgrading to enhanced risk management"
        def upgradedPosition = positionsService.initializeEnhancedRiskManagement(
            legacyPosition.id, new BigDecimal("0.06"), new BigDecimal("0.035"))

        then: "should now use enhanced risk management"
        upgradedPosition.aggressiveStopPercent != null
        upgradedPosition.conservativeStopPercent != null
        upgradedPosition.riskMode == Position.RiskMode.AGGRESSIVE
    }
}
