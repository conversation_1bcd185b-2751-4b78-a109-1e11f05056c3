package com.investment.integration

import com.investment.api.model.BollingerBandRequest
import com.investment.api.model.BollingerBandResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.service.BollingerBandService
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Integration test to verify the fix for Bollinger Bands INCREMENTAL mode issues:
 * 1. Incorrect scope of recalculation (including dates that shouldn't be recalculated)
 * 2. Incorrect calculation results (mathematical differences from FULL_RECALCULATION)
 */
class BollingerBandIncrementalFixSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    BollingerBandService bollingerBandService

    def setupSpec() {
        try {
            // Clean up any existing test database
            new File("./data/bb_incremental_fix_test.duckdb").delete()

            // Initialize test database
            DatabaseManager.setDbUrl("*************************************************")
            databaseManager = new DatabaseManager()
            databaseManager.initDatabase()
            bollingerBandService = new BollingerBandService(databaseManager)

            // Create test data - we need real MSFT data for this test
            setupTestData()
        } catch (Exception e) {
            println "Setup failed: ${e.message}"
            throw e
        }
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/bb_incremental_fix_test.duckdb").delete()
    }

    def "should fix INCREMENTAL mode scope and calculation issues"() {
        given: "MSFT symbol with OHLCV data"
        def symbol = "MSFT"

        // Clean up any existing Bollinger Band data first
        databaseManager.clearBollingerBandData(symbol)

        // Verify we have sufficient OHLCV data for MSFT
        def recordCount = databaseManager.countOhlcvRecords(symbol)
        println "MSFT has ${recordCount} OHLCV records"
        assert recordCount >= 40 : "Need at least 40 records for this test"

        when: "performing FULL_RECALCULATION to establish baseline"
        def fullRequest = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.FULL_RECALCULATION)
        fullRequest.symbols = [symbol]
        
        BollingerBandResponse fullResponse = bollingerBandService.calculateBollingerBands(fullRequest)
        
        then: "full calculation should succeed"
        fullResponse.status == "success"
        fullResponse.processedSymbols == 1
        fullResponse.totalRecordsUpdated > 0
        
        when: "getting the baseline results from FULL_RECALCULATION"
        def fullCalculationData = getRecentOHLCVData(symbol, 5)
        println "Full calculation results (last 5 records):"
        fullCalculationData.each { ohlcv ->
            println "  ${ohlcv.date}: bb_middle=${ohlcv.bbMiddleBand}, bb_std_dev=${ohlcv.bbStdDev}, bb_upper=${ohlcv.bbUpperBand}, bb_lower=${ohlcv.bbLowerBand}"
        }
        
        and: "clearing Bollinger Band data and recalculating with FULL_RECALCULATION again"
        databaseManager.clearBollingerBandData(symbol)
        BollingerBandResponse fullResponse2 = bollingerBandService.calculateBollingerBands(fullRequest)
        
        then: "second full calculation should produce identical results"
        fullResponse2.status == "success"
        fullResponse2.processedSymbols == 1
        
        when: "getting the N-1 reference date for incremental calculation"
        def lastCalculatedDate = databaseManager.getLastBollingerBandCalculationDate(symbol)
        println "N-1 reference date for incremental calculation: ${lastCalculatedDate}"
        
        and: "performing INCREMENTAL calculation"
        def incrementalRequest = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        incrementalRequest.symbols = [symbol]
        
        BollingerBandResponse incrementalResponse = bollingerBandService.calculateBollingerBands(incrementalRequest)
        
        then: "incremental calculation should succeed"
        incrementalResponse.status == "success"
        incrementalResponse.processedSymbols == 1
        incrementalResponse.totalRecordsUpdated > 0
        
        when: "getting the results from INCREMENTAL calculation"
        def incrementalCalculationData = getRecentOHLCVData(symbol, 5)
        println "Incremental calculation results (last 5 records):"
        incrementalCalculationData.each { ohlcv ->
            println "  ${ohlcv.date}: bb_middle=${ohlcv.bbMiddleBand}, bb_std_dev=${ohlcv.bbStdDev}, bb_upper=${ohlcv.bbUpperBand}, bb_lower=${ohlcv.bbLowerBand}"
        }
        
        then: "INCREMENTAL results should match FULL_RECALCULATION results"
        fullCalculationData.size() == incrementalCalculationData.size()
        
        for (int i = 0; i < fullCalculationData.size(); i++) {
            def fullRecord = fullCalculationData[i]
            def incrementalRecord = incrementalCalculationData[i]
            
            assert fullRecord.date == incrementalRecord.date : "Dates should match at index ${i}"
            
            // Compare Bollinger Band values with tolerance for floating point precision
            def tolerance = 0.000001
            
            if (fullRecord.bbMiddleBand != null && incrementalRecord.bbMiddleBand != null) {
                def middleDiff = Math.abs(fullRecord.bbMiddleBand - incrementalRecord.bbMiddleBand)
                assert middleDiff < tolerance : "Middle band mismatch at ${fullRecord.date}: full=${fullRecord.bbMiddleBand}, incremental=${incrementalRecord.bbMiddleBand}, diff=${middleDiff}"
                
                def stdDevDiff = Math.abs(fullRecord.bbStdDev - incrementalRecord.bbStdDev)
                assert stdDevDiff < tolerance : "Std dev mismatch at ${fullRecord.date}: full=${fullRecord.bbStdDev}, incremental=${incrementalRecord.bbStdDev}, diff=${stdDevDiff}"
                
                def upperDiff = Math.abs(fullRecord.bbUpperBand - incrementalRecord.bbUpperBand)
                assert upperDiff < tolerance : "Upper band mismatch at ${fullRecord.date}: full=${fullRecord.bbUpperBand}, incremental=${incrementalRecord.bbUpperBand}, diff=${upperDiff}"
                
                def lowerDiff = Math.abs(fullRecord.bbLowerBand - incrementalRecord.bbLowerBand)
                assert lowerDiff < tolerance : "Lower band mismatch at ${fullRecord.date}: full=${fullRecord.bbLowerBand}, incremental=${incrementalRecord.bbLowerBand}, diff=${lowerDiff}"
            }
        }
        
        println "✅ INCREMENTAL calculation results match FULL_RECALCULATION results!"
    }

    def "should only update records from N-1 date onwards in INCREMENTAL mode"() {
        given: "MSFT symbol with existing Bollinger Band data"
        def symbol = "MSFT"

        // Clean up any existing Bollinger Band data first
        databaseManager.clearBollingerBandData(symbol)

        // Perform full calculation first
        def fullRequest = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.FULL_RECALCULATION)
        fullRequest.symbols = [symbol]
        bollingerBandService.calculateBollingerBands(fullRequest)
        
        when: "getting the N-1 reference date"
        def lastCalculatedDate = databaseManager.getLastBollingerBandCalculationDate(symbol)
        println "N-1 reference date: ${lastCalculatedDate}"
        
        and: "modifying a record before the N-1 date to detect if it gets recalculated"
        def testDate = lastCalculatedDate.minusDays(2) // N-3 date (should NOT be recalculated)
        def originalValue = setTestBollingerBandValue(symbol, testDate, 999.999)
        println "Set test value 999.999 for date ${testDate} (should NOT be recalculated)"
        
        and: "performing INCREMENTAL calculation"
        def incrementalRequest = new BollingerBandRequest(20, 2.0, false, BollingerBandRequest.CalculationMode.INCREMENTAL)
        incrementalRequest.symbols = [symbol]
        bollingerBandService.calculateBollingerBands(incrementalRequest)
        
        then: "the test value should remain unchanged (proving the date was not recalculated)"
        def afterValue = getBollingerBandValue(symbol, testDate)
        assert Math.abs(afterValue - 999.999) < 0.001 : "Test value should remain unchanged at date ${testDate}, but was ${afterValue}"
        
        println "✅ INCREMENTAL mode correctly skipped recalculating date ${testDate}"
        
        cleanup: "restore original value"
        if (originalValue != null) {
            setTestBollingerBandValue(symbol, testDate, originalValue)
        }
    }

    /**
     * Get recent OHLCV data with Bollinger Bands for testing
     */
    private List<OHLCV> getRecentOHLCVData(String symbol, int count) {
        def endDate = LocalDate.now()
        def startDate = endDate.minusDays(count + 10) // Get extra days to ensure we have enough data
        
        def data = databaseManager.getOHLCVData(symbol, startDate, endDate)
        return data.findAll { it.bbMiddleBand != null }
                  .sort { it.date }
                  .takeRight(count)
    }

    /**
     * Set a test Bollinger Band value for a specific date and return the original value
     */
    private Double setTestBollingerBandValue(String symbol, LocalDate date, double testValue) {
        def sql = "SELECT bb_middle_band FROM ohlcv WHERE symbol = ? AND date = ?"
        def originalValue = null
        
        try {
            def pstmt = databaseManager.connection.prepareStatement(sql)
            pstmt.setString(1, symbol)
            pstmt.setDate(2, java.sql.Date.valueOf(date))
            def rs = pstmt.executeQuery()
            if (rs.next()) {
                originalValue = rs.getDouble("bb_middle_band")
            }
            rs.close()
            pstmt.close()
        } catch (Exception e) {
            println "Error getting original value: ${e.message}"
        }
        
        def updateSql = "UPDATE ohlcv SET bb_middle_band = ? WHERE symbol = ? AND date = ?"
        try {
            def pstmt = databaseManager.connection.prepareStatement(updateSql)
            pstmt.setDouble(1, testValue)
            pstmt.setString(2, symbol)
            pstmt.setDate(3, java.sql.Date.valueOf(date))
            pstmt.executeUpdate()
            pstmt.close()
        } catch (Exception e) {
            println "Error setting test value: ${e.message}"
        }
        
        return originalValue
    }

    /**
     * Get Bollinger Band middle band value for a specific date
     */
    private Double getBollingerBandValue(String symbol, LocalDate date) {
        def sql = "SELECT bb_middle_band FROM ohlcv WHERE symbol = ? AND date = ?"
        try {
            def pstmt = databaseManager.connection.prepareStatement(sql)
            pstmt.setString(1, symbol)
            pstmt.setDate(2, java.sql.Date.valueOf(date))
            def rs = pstmt.executeQuery()
            def value = null
            if (rs.next()) {
                value = rs.getDouble("bb_middle_band")
            }
            rs.close()
            pstmt.close()
            return value
        } catch (Exception e) {
            println "Error getting Bollinger Band value: ${e.message}"
            return null
        }
    }

    /**
     * Setup test data with realistic OHLCV data for MSFT
     */
    private void setupTestData() {
        // Create test instrument
        databaseManager.saveInstrument("MSFT", "Microsoft Corporation", "Technology")

        // Create realistic OHLCV data for MSFT for the past 60 days
        LocalDate startDate = LocalDate.now().minusDays(60)
        List<OHLCV> testData = []

        // Generate realistic price data with some volatility
        double basePrice = 400.0
        Random random = new Random(12345) // Fixed seed for reproducible tests

        for (int i = 0; i < 50; i++) {
            LocalDate date = startDate.plusDays(i)

            // Add some realistic price movement
            double priceChange = (random.nextGaussian() * 5.0) // Standard deviation of $5
            double open = basePrice + priceChange
            double close = open + (random.nextGaussian() * 3.0) // Intraday movement
            double high = Math.max(open, close) + Math.abs(random.nextGaussian() * 2.0)
            double low = Math.min(open, close) - Math.abs(random.nextGaussian() * 2.0)
            long volume = 15000000L + (long)(random.nextGaussian() * 5000000L)

            testData.add(new OHLCV("MSFT", date, open, high, low, close, Math.max(volume, 1000000L)))

            // Update base price for next day (trend)
            basePrice = close
        }

        databaseManager.saveOHLCVData(testData)
        println "Created ${testData.size()} OHLCV records for MSFT"
    }
}
