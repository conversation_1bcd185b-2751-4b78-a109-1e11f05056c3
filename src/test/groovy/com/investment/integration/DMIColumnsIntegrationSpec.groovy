package com.investment.integration

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Integration test demonstrating the new DMI columns functionality.
 * This test shows how to work with the DMI columns in real-world scenarios.
 */
class DMIColumnsIntegrationSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/dmi_integration_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("**********************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_integration_test.duckdb").delete()
    }

    def "should demonstrate complete DMI workflow"() {
        given: "a symbol with historical price data"
        String symbol = "DEMO"
        databaseManager.saveInstrument(symbol, "Demo Company", "Technology")
        
        and: "historical OHLCV data without technical indicators"
        List<OHLCV> historicalData = createHistoricalData(symbol, 30)
        databaseManager.saveOHLCVData(historicalData)

        when: "retrieving the historical data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 LocalDate.now().minusDays(35), 
                                                                 LocalDate.now())

        then: "should have historical data without DMI values"
        retrievedData.size() == 30
        retrievedData.every { it.dmiPlusDi == null && it.dmiMinusDi == null && it.dmiAdx == null }

        when: "calculating and updating with DMI values"
        List<OHLCV> dataWithDMI = addDMICalculations(retrievedData)
        databaseManager.saveOHLCVData(dataWithDMI)

        and: "retrieving the updated data"
        List<OHLCV> updatedData = databaseManager.getOHLCVData(symbol, 
                                                               LocalDate.now().minusDays(35), 
                                                               LocalDate.now())

        then: "should have DMI values for recent data points"
        updatedData.size() == 30
        def recentData = updatedData.findAll { it.dmiPlusDi != null }
        recentData.size() >= 16  // DMI typically needs 14+ periods, so 30-14=16 records
        recentData.every { 
            it.dmiPlusDi >= 0 && it.dmiPlusDi <= 100 &&
            it.dmiMinusDi >= 0 && it.dmiMinusDi <= 100 &&
            it.dmiAdx >= 0 && it.dmiAdx <= 100
        }
    }

    def "should handle mixed technical indicators"() {
        given: "a symbol with both Bollinger Band and DMI data"
        String symbol = "MIXED"
        databaseManager.saveInstrument(symbol, "Mixed Indicators Company", "Finance")
        
        and: "OHLCV data with both BB and DMI values"
        List<OHLCV> mixedData = [
            new OHLCV(symbol, LocalDate.now(), 100.0, 105.0, 98.0, 102.0, 1000000L,
                     101.5, 2.1, 105.7, 97.3,  // BB values
                     28.5, 18.2, 42.8),        // DMI values
            new OHLCV(symbol, LocalDate.now().minusDays(1), 98.0, 103.0, 96.0, 100.0, 900000L,
                     100.2, 2.3, 104.8, 95.6,  // BB values
                     26.1, 20.4, 38.9),        // DMI values
            new OHLCV(symbol, LocalDate.now().minusDays(2), 96.0, 101.0, 94.0, 98.0, 800000L,
                     98.8, 2.5, 103.8, 93.8,   // BB values
                     23.7, 22.8, 35.2)         // DMI values
        ]
        
        when: "saving the mixed data"
        databaseManager.saveOHLCVData(mixedData)

        and: "retrieving the data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 LocalDate.now().minusDays(3), 
                                                                 LocalDate.now())

        then: "should have both BB and DMI values"
        retrievedData.size() == 3
        retrievedData.every { record ->
            // Bollinger Band values
            record.bbMiddleBand != null &&
            record.bbStdDev != null &&
            record.bbUpperBand != null &&
            record.bbLowerBand != null &&
            // DMI values
            record.dmiPlusDi != null &&
            record.dmiMinusDi != null &&
            record.dmiAdx != null
        }
        
        and: "values should be within expected ranges"
        retrievedData.every { record ->
            record.dmiPlusDi >= 0 && record.dmiPlusDi <= 100 &&
            record.dmiMinusDi >= 0 && record.dmiMinusDi <= 100 &&
            record.dmiAdx >= 0 && record.dmiAdx <= 100
        }
    }

    def "should demonstrate incremental DMI updates"() {
        given: "existing data without DMI values"
        String symbol = "INCR"
        databaseManager.saveInstrument(symbol, "Incremental Updates Company", "Technology")
        
        List<OHLCV> initialData = [
            new OHLCV(symbol, LocalDate.now().minusDays(2), 150.0, 155.0, 148.0, 152.0, 1200000L),
            new OHLCV(symbol, LocalDate.now().minusDays(1), 152.0, 157.0, 150.0, 154.0, 1100000L)
        ]
        databaseManager.saveOHLCVData(initialData)

        when: "adding new data with DMI calculations"
        List<OHLCV> newData = [
            new OHLCV(symbol, LocalDate.now(), 154.0, 159.0, 152.0, 156.0, 1300000L,
                     null, null, null, null,  // No BB data
                     32.1, 22.8, 45.5)        // DMI data
        ]
        databaseManager.saveOHLCVData(newData)

        and: "updating existing records with DMI calculations"
        List<OHLCV> updatedData = [
            new OHLCV(symbol, LocalDate.now().minusDays(2), 150.0, 155.0, 148.0, 152.0, 1200000L,
                     null, null, null, null,  // No BB data
                     28.9, 25.1, 41.2),       // DMI data
            new OHLCV(symbol, LocalDate.now().minusDays(1), 152.0, 157.0, 150.0, 154.0, 1100000L,
                     null, null, null, null,  // No BB data
                     30.5, 23.9, 43.8)        // DMI data
        ]
        databaseManager.saveOHLCVData(updatedData)

        and: "retrieving all data"
        List<OHLCV> allData = databaseManager.getOHLCVData(symbol, 
                                                           LocalDate.now().minusDays(3), 
                                                           LocalDate.now())

        then: "should have DMI values for all records"
        allData.size() == 3
        allData.every { it.dmiPlusDi != null && it.dmiMinusDi != null && it.dmiAdx != null }
        
        and: "should show progression of DMI values"
        def sortedData = allData.sort { it.date }
        sortedData[0].dmiPlusDi == 28.9  // Oldest
        sortedData[1].dmiPlusDi == 30.5  // Middle
        sortedData[2].dmiPlusDi == 32.1  // Newest
    }

    def "should handle edge cases and null values"() {
        given: "data with various null combinations"
        String symbol = "EDGE"
        databaseManager.saveInstrument(symbol, "Edge Cases Company", "Technology")
        
        List<OHLCV> edgeCaseData = [
            // Only basic OHLCV
            new OHLCV(symbol, LocalDate.now().minusDays(3), 200.0, 205.0, 198.0, 202.0, 500000L),
            // Only DMI data
            new OHLCV(symbol, LocalDate.now().minusDays(2), 202.0, 207.0, 200.0, 204.0, 520000L,
                     null, null, null, null,  // No BB data
                     15.5, 35.2, 28.9),       // DMI data
            // Only BB data
            new OHLCV(symbol, LocalDate.now().minusDays(1), 204.0, 209.0, 202.0, 206.0, 540000L,
                     205.1, 1.8, 208.7, 201.5,  // BB data
                     null, null, null),          // No DMI data
            // Both BB and DMI data
            new OHLCV(symbol, LocalDate.now(), 206.0, 211.0, 204.0, 208.0, 560000L,
                     207.2, 1.9, 211.0, 203.4,  // BB data
                     18.7, 32.1, 31.5)          // DMI data
        ]

        when: "saving edge case data"
        databaseManager.saveOHLCVData(edgeCaseData)

        and: "retrieving the data"
        List<OHLCV> retrievedData = databaseManager.getOHLCVData(symbol, 
                                                                 LocalDate.now().minusDays(4), 
                                                                 LocalDate.now())

        then: "should handle all null combinations correctly"
        retrievedData.size() == 4
        
        and: "first record should have no technical indicators"
        def record1 = retrievedData.find { it.date == LocalDate.now().minusDays(3) }
        record1.bbMiddleBand == null && record1.dmiPlusDi == null
        
        and: "second record should have only DMI data"
        def record2 = retrievedData.find { it.date == LocalDate.now().minusDays(2) }
        record2.bbMiddleBand == null && record2.dmiPlusDi == 15.5
        
        and: "third record should have only BB data"
        def record3 = retrievedData.find { it.date == LocalDate.now().minusDays(1) }
        record3.bbMiddleBand == 205.1 && record3.dmiPlusDi == null
        
        and: "fourth record should have both BB and DMI data"
        def record4 = retrievedData.find { it.date == LocalDate.now() }
        record4.bbMiddleBand == 207.2 && record4.dmiPlusDi == 18.7
    }

    private List<OHLCV> createHistoricalData(String symbol, int days) {
        List<OHLCV> data = []
        double basePrice = 100.0
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i)
            double open = basePrice + (Math.random() - 0.5) * 4
            double close = open + (Math.random() - 0.5) * 6
            double high = Math.max(open, close) + Math.random() * 3
            double low = Math.min(open, close) - Math.random() * 3
            long volume = (long) (800000 + Math.random() * 400000)
            
            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
            basePrice = close // Use previous close as base for next day
        }
        
        return data
    }

    private List<OHLCV> addDMICalculations(List<OHLCV> data) {
        List<OHLCV> result = []
        
        for (int i = 0; i < data.size(); i++) {
            OHLCV original = data[i]
            
            // Simple DMI simulation (in real implementation, this would be proper DMI calculation)
            Double plusDI = null
            Double minusDI = null
            Double adx = null
            
            if (i >= 14) { // DMI typically needs 14+ periods
                plusDI = 15.0 + Math.random() * 30  // +DI between 15-45
                minusDI = 10.0 + Math.random() * 25  // -DI between 10-35
                adx = 20.0 + Math.random() * 40      // ADX between 20-60
            }
            
            result.add(new OHLCV(
                original.symbol, original.date, original.open, original.high, 
                original.low, original.close, original.volume,
                original.bbMiddleBand, original.bbStdDev, original.bbUpperBand, original.bbLowerBand,
                plusDI, minusDI, adx
            ))
        }
        
        return result
    }
}
