package com.investment.service

import com.investment.database.DatabaseManager
import spock.lang.Specification
import spock.lang.Subject

/**
 * Spock specification for testing the SymbolValidationService.
 */
class SymbolValidationServiceSpec extends Specification {

    @Subject
    SymbolValidationService symbolValidationService

    DatabaseManager mockDatabaseManager

    def setup() {
        mockDatabaseManager = Mock(DatabaseManager)
        symbolValidationService = new SymbolValidationService(mockDatabaseManager)
    }

    def "should validate symbols and identify invalid ones in dry run mode"() {
        given: "a database with some symbols"
        def databaseSymbols = ["AAPL", "GOOGL", "INVALID1", "MSFT", "INVALID2"]
        mockDatabaseManager.getAllSymbols() >> databaseSymbols

        and: "mock OHLCV record counts"
        mockDatabaseManager.countOhlcvRecords("INVALID1") >> 10
        mockDatabaseManager.countOhlcvRecords("INVALID2") >> 5

        when: "validating symbols in dry run mode"
        def response = symbolValidationService.validateSymbols(true, false)

        then: "should identify invalid symbols without deleting anything"
        response.totalSymbolsInDatabase == 5
        response.invalidSymbols >= 0  // Will depend on actual SEC data
        response.dryRun == true
        response.deletedSymbols == 0  // No actual deletions in dry run
        response.summary.contains("DRY RUN")

        and: "should not call delete methods"
        0 * mockDatabaseManager.deleteInstrumentsAndOhlcvData(_)
    }

    def "should handle empty database gracefully"() {
        given: "an empty database"
        mockDatabaseManager.getAllSymbols() >> []

        when: "validating symbols"
        def response = symbolValidationService.validateSymbols(true, false)

        then: "should handle empty case gracefully"
        response.totalSymbolsInDatabase == 0
        response.validSymbols == 0
        response.invalidSymbols == 0
        response.invalidSymbolsList.isEmpty()
        response.deletedSymbols == 0
        response.deletedOhlcvRecords == 0
    }

    def "should get cache status information"() {
        when: "getting cache status"
        def status = symbolValidationService.getCacheStatus()

        then: "should return cache information"
        status.containsKey("cacheDirectory")
        status.containsKey("cacheFile")
        status.containsKey("cacheExpiry")
        status.containsKey("lastCacheUpdate")
        status.containsKey("cachedTickersCount")
        status.containsKey("cacheFileExists")
    }

    def "should handle database errors gracefully"() {
        given: "database throws an exception"
        mockDatabaseManager.getAllSymbols() >> { throw new RuntimeException("Database error") }

        when: "validating symbols"
        symbolValidationService.validateSymbols(true, false)

        then: "should propagate the error with context"
        def ex = thrown(RuntimeException)
        ex.message.contains("Symbol validation failed")
        ex.cause.message == "Database error"
    }
}
