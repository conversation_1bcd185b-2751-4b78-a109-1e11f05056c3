package com.investment.service

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.model.Position
import com.investment.api.model.PositionsUpdateRequest
import spock.lang.Specification

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test to verify that last_price in positions table is properly synchronized
 * with the latest close price from OHLCV table after OHLCV data updates.
 */
class PositionsLastPriceSyncSpec extends Specification {

    DatabaseManager databaseManager
    PositionsService positionsService
    OHLCVService ohlcvService

    def setup() {
        // Use in-memory database for testing
        String testDbUrl = "jdbc:duckdb:"
        DatabaseManager.setDbUrl(testDbUrl)
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()

        // Create services
        ohlcvService = new OHLCVService(databaseManager, null) // null provider for testing
        positionsService = new PositionsService(databaseManager, ohlcvService)
    }

    def cleanup() {
        databaseManager?.closeConnection()
    }

    def "should synchronize last_price with latest OHLCV close price after manual update"() {
        given: "an instrument and position exist"
        String symbol = "AAPL"
        databaseManager.saveInstrument(symbol, "Apple Inc.", "US_STOCK")

        // Create initial OHLCV data
        def initialOhlcvData = [
            new OHLCV(symbol, LocalDate.of(2024, 1, 1), 150.0, 155.0, 148.0, 152.0, 1000000L),
            new OHLCV(symbol, LocalDate.of(2024, 1, 2), 152.0, 157.0, 150.0, 154.0, 1100000L)
        ]
        databaseManager.saveOHLCVData(initialOhlcvData)

        // Create a position
        Long positionId = databaseManager.createPosition(
            symbol, new BigDecimal("100"), "BUY", "OPEN",
            new BigDecimal("150.00"), new BigDecimal("15000.00"), new BigDecimal("100000.00"),
            new BigDecimal("1000.00"), new BigDecimal("0.02"), new BigDecimal("0.01")
        )

        // Update position with initial market price
        positionsService.updatePositionPrice(positionId, new BigDecimal("154.00"))

        when: "new OHLCV data is added with a different close price"
        def newOhlcvData = [
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 154.0, 160.0, 153.0, 158.50, 1200000L)
        ]
        databaseManager.saveOHLCVData(newOhlcvData)

        and: "P&L recalculation is triggered directly (simulating the OHLCV update effect)"
        def pnlUpdatedCount = positionsService.recalculatePnLForAllPositions()

        then: "the position's last_price should be updated to the latest close price"
        def updatedPosition = positionsService.getPositionById(positionId).get()
        updatedPosition.lastPrice == new BigDecimal("158.50")
        updatedPosition.pnlValue != null
        updatedPosition.pnlPercent != null

        and: "the P&L update should indicate success"
        pnlUpdatedCount == 1
    }

    def "should handle multiple positions with different symbols"() {
        given: "multiple instruments and positions exist"
        def symbols = ["AAPL", "MSFT", "GOOGL"]
        def positions = []

        symbols.each { symbol ->
            databaseManager.saveInstrument(symbol, "${symbol} Inc.", "US_STOCK")

            // Create initial OHLCV data
            def ohlcvData = [
                new OHLCV(symbol, LocalDate.of(2024, 1, 1), 100.0, 105.0, 98.0, 102.0, 1000000L),
                new OHLCV(symbol, LocalDate.of(2024, 1, 2), 102.0, 107.0, 100.0, 104.0, 1100000L)
            ]
            databaseManager.saveOHLCVData(ohlcvData)

            // Create position
            Long positionId = databaseManager.createPosition(
                symbol, new BigDecimal("100"), "BUY", "OPEN",
                new BigDecimal("100.00"), new BigDecimal("10000.00"), new BigDecimal("100000.00"),
                new BigDecimal("1000.00"), new BigDecimal("0.02"), new BigDecimal("0.01")
            )
            positions.add(positionId)
        }

        when: "new OHLCV data is added for all symbols"
        symbols.eachWithIndex { symbol, index ->
            def newPrice = 110.0 + (index * 5) // Different prices for each symbol
            def newOhlcvData = [
                new OHLCV(symbol, LocalDate.of(2024, 1, 3), 104.0, newPrice + 2, 103.0, newPrice, 1200000L)
            ]
            databaseManager.saveOHLCVData(newOhlcvData)
        }

        and: "P&L recalculation is triggered directly"
        def pnlUpdatedCount = positionsService.recalculatePnLForAllPositions()

        then: "all positions should have updated last_price values"
        positions.eachWithIndex { positionId, index ->
            def updatedPosition = positionsService.getPositionById(positionId).get()
            def expectedPrice = new BigDecimal(110.0 + (index * 5))
            updatedPosition.lastPrice == expectedPrice
        }

        and: "the P&L update should indicate success for all positions"
        pnlUpdatedCount == 3
    }

    def "should not update CLOSED positions with market prices"() {
        given: "an instrument and closed position exist"
        String symbol = "AAPL"
        databaseManager.saveInstrument(symbol, "Apple Inc.", "US_STOCK")

        // Create OHLCV data
        def ohlcvData = [
            new OHLCV(symbol, LocalDate.of(2024, 1, 1), 150.0, 155.0, 148.0, 152.0, 1000000L)
        ]
        databaseManager.saveOHLCVData(ohlcvData)

        // Create a CLOSED position
        Long positionId = databaseManager.createPosition(
            symbol, new BigDecimal("100"), "BUY", "CLOSED",
            new BigDecimal("150.00"), new BigDecimal("15000.00"), new BigDecimal("100000.00"),
            new BigDecimal("1000.00"), new BigDecimal("0.02"), new BigDecimal("0.01")
        )

        // Set close price for the closed position
        positionsService.updateClosePrice(positionId, new BigDecimal("155.00"))
        def originalPosition = positionsService.getPositionById(positionId).get()
        def originalLastPrice = originalPosition.lastPrice

        when: "new OHLCV data is added"
        def newOhlcvData = [
            new OHLCV(symbol, LocalDate.of(2024, 1, 2), 155.0, 160.0, 154.0, 158.50, 1200000L)
        ]
        databaseManager.saveOHLCVData(newOhlcvData)

        and: "P&L recalculation is triggered directly"
        def pnlUpdatedCount = positionsService.recalculatePnLForAllPositions()

        then: "the CLOSED position's last_price should NOT be updated with market price"
        def updatedPosition = positionsService.getPositionById(positionId).get()
        updatedPosition.status == Position.Status.CLOSED
        // For CLOSED positions, P&L should be recalculated using close_price, not market price
        updatedPosition.pnlValue != null
        updatedPosition.pnlPercent != null

        and: "the P&L update should still process the CLOSED position (using its close_price)"
        pnlUpdatedCount == 1 // CLOSED positions get P&L recalculated using their close_price
    }
}
