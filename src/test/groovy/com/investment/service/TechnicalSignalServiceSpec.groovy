package com.investment.service

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDate

/**
 * Test specification for TechnicalSignalService.
 * Tests the calculation of technical signal streaks for BUY indicators.
 */
class TechnicalSignalServiceSpec extends Specification {

    DatabaseManager databaseManager = Mock()
    
    @Subject
    TechnicalSignalService technicalSignalService = new TechnicalSignalService(databaseManager)

    def "should calculate bullish Bollinger streak with corrected two-phase algorithm - normal case"() {
        given: "OHLCV data where candlestick bodies are completely above BB middle with bullish validation"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - Phase 1: both open & close > bb_middle, Phase 2: bullish (close > open)
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 151.0, 155.0, 150.0, 152.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0), // open=151 > bb_middle=150, close=152 > bb_middle=150, bullish

            // Day 2 - Phase 1: both open & close > bb_middle, Phase 2: bullish
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 149.5, 153.0, 149.0, 151.0, 1000000L,
                     149.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0), // open=149.5 > bb_middle=149, close=151 > bb_middle=149, bullish

            // Day 3 - Phase 1: both open & close > bb_middle, Phase 2: bullish
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 147.5, 150.0, 147.0, 149.0, 1000000L,
                     147.0, 2.0, 151.0, 143.0, 23.0, 17.0, 33.0, 18.0), // open=147.5 > bb_middle=147, close=149 > bb_middle=147, bullish

            // Day 4 - breaks Phase 1 streak (open < bb_middle)
            new OHLCV(symbol, LocalDate.of(2024, 1, 2), 145.0, 149.0, 144.0, 148.0, 1000000L,
                     146.0, 2.0, 150.0, 142.0, 22.0, 18.0, 32.0, 17.0)  // open=145 < bb_middle=146 (breaks Phase 1)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 3 days"
        streaks[0] == 3 // bullishBbStreak
    }

    def "should calculate bullish Bollinger streak with Phase 2 validation reducing streak count"() {
        given: "OHLCV data where Phase 1 qualifies 4 days but Phase 2 validation finds bullish candle on 3rd oldest day"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - Phase 1: qualified, Phase 2: bearish (close < open)
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 152.0, 155.0, 150.0, 151.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0), // open=152 > bb_middle=150, close=151 > bb_middle=150, bearish

            // Day 2 - Phase 1: qualified, Phase 2: bearish
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 151.0, 153.0, 149.0, 150.5, 1000000L,
                     149.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0), // open=151 > bb_middle=149, close=150.5 > bb_middle=149, bearish

            // Day 3 - Phase 1: qualified, Phase 2: bullish (this should be the final streak count)
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 147.5, 150.0, 147.0, 149.0, 1000000L,
                     147.0, 2.0, 151.0, 143.0, 23.0, 17.0, 33.0, 18.0), // open=147.5 > bb_middle=147, close=149 > bb_middle=147, bullish

            // Day 4 - Phase 1: qualified, Phase 2: bearish
            new OHLCV(symbol, LocalDate.of(2024, 1, 2), 146.5, 149.0, 146.0, 146.0, 1000000L,
                     145.0, 2.0, 150.0, 142.0, 22.0, 18.0, 32.0, 17.0), // open=146.5 > bb_middle=145, close=146 > bb_middle=145, bearish

            // Day 5 - breaks Phase 1 streak
            new OHLCV(symbol, LocalDate.of(2024, 1, 1), 144.0, 147.0, 143.0, 145.0, 1000000L,
                     146.0, 2.0, 150.0, 142.0, 21.0, 19.0, 31.0, 16.0)  // open=144 < bb_middle=146 (breaks Phase 1)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 3 days (Phase 2 validation finds bullish candle on 2nd oldest qualified day)"
        streaks[0] == 3 // bullishBbStreak
    }

    def "should return zero when Phase 2 validation finds no bullish candlesticks"() {
        given: "OHLCV data where Phase 1 qualifies days but all are bearish candlesticks"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - Phase 1: qualified, Phase 2: bearish
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 152.0, 155.0, 150.0, 151.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0), // open=152 > bb_middle=150, close=151 > bb_middle=150, bearish

            // Day 2 - Phase 1: qualified, Phase 2: bearish
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 150.0, 153.0, 149.0, 149.5, 1000000L,
                     149.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0), // open=150 > bb_middle=149, close=149.5 > bb_middle=149, bearish

            // Day 3 - breaks Phase 1 streak
            new OHLCV(symbol, LocalDate.of(2024, 1, 2), 146.0, 149.0, 145.0, 148.0, 1000000L,
                     147.0, 2.0, 151.0, 143.0, 23.0, 17.0, 33.0, 18.0)  // open=146 < bb_middle=147 (breaks Phase 1)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 0 (no bullish candlesticks found in Phase 2)"
        streaks[0] == 0 // bullishBbStreak
    }

    def "should return zero when Phase 1 finds no qualified days"() {
        given: "OHLCV data where no candlestick bodies are completely above BB middle"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - Phase 1: fails (open < bb_middle)
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 149.0, 155.0, 148.0, 152.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0), // open=149 < bb_middle=150 (fails Phase 1)

            // Day 2 - would qualify if reached
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 151.0, 153.0, 149.0, 152.0, 1000000L,
                     149.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 0 (Phase 1 immediately fails)"
        streaks[0] == 0 // bullishBbStreak
    }

    def "should handle null Bollinger Band data correctly"() {
        given: "OHLCV data with null BB middle values"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - null BB data should terminate calculation
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 150.0, 155.0, 148.0, 152.0, 1000000L,
                     null, null, null, null, 25.0, 15.0, 35.0, 20.0), // bb_middle=null

            // Day 2 - would qualify if reached
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 151.0, 153.0, 149.0, 152.0, 1000000L,
                     149.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 0 (null BB data terminates calculation)"
        streaks[0] == 0 // bullishBbStreak
    }

    def "should handle empty OHLCV data correctly"() {
        given: "empty OHLCV data list"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = []

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 0 (empty data)"
        streaks[0] == 0 // bullishBbStreak
    }

    def "should handle single day streak correctly"() {
        given: "OHLCV data with only one qualifying day"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - Phase 1: qualified, Phase 2: bullish
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 151.0, 155.0, 150.0, 152.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0), // open=151 > bb_middle=150, close=152 > bb_middle=150, bullish

            // Day 2 - breaks Phase 1 streak (close < bb_middle)
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 151.0, 153.0, 149.0, 149.5, 1000000L,
                     150.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0) // close=149.5 < bb_middle=150 (breaks Phase 1)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 1 day"
        streaks[0] == 1 // bullishBbStreak
    }

    def "should demonstrate Phase 2 validation with clear example"() {
        given: "OHLCV data where Phase 1 qualifies 5 days but Phase 2 finds bullish candle on 2nd oldest day"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - Phase 1: qualified, Phase 2: bearish
            new OHLCV(symbol, LocalDate.of(2024, 1, 6), 152.0, 155.0, 150.0, 151.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0), // open=152 > bb_middle=150, close=151 > bb_middle=150, bearish (close < open)

            // Day 2 - Phase 1: qualified, Phase 2: bearish
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 151.0, 153.0, 149.0, 150.5, 1000000L,
                     149.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0), // open=151 > bb_middle=149, close=150.5 > bb_middle=149, bearish

            // Day 3 - Phase 1: qualified, Phase 2: bearish
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 148.5, 150.0, 147.0, 148.0, 1000000L,
                     147.0, 2.0, 151.0, 143.0, 23.0, 17.0, 33.0, 18.0), // open=148.5 > bb_middle=147, close=148 > bb_middle=147, bearish

            // Day 4 - Phase 1: qualified, Phase 2: bullish (this should determine final streak)
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 146.0, 149.0, 145.0, 147.0, 1000000L,
                     145.0, 2.0, 150.0, 142.0, 22.0, 18.0, 32.0, 17.0), // open=146 > bb_middle=145, close=147 > bb_middle=145, bullish (close > open)

            // Day 5 - Phase 1: qualified, Phase 2: bearish
            new OHLCV(symbol, LocalDate.of(2024, 1, 2), 144.5, 147.0, 144.0, 144.0, 1000000L,
                     143.0, 2.0, 148.0, 140.0, 21.0, 19.0, 31.0, 16.0), // open=144.5 > bb_middle=143, close=144 > bb_middle=143, bearish

            // Day 6 - breaks Phase 1 streak
            new OHLCV(symbol, LocalDate.of(2024, 1, 1), 142.0, 145.0, 141.0, 143.0, 1000000L,
                     144.0, 2.0, 149.0, 139.0, 20.0, 20.0, 30.0, 15.0)  // open=142 < bb_middle=144 (breaks Phase 1)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 4 days (Phase 2 validation: oldest=bearish(streak=4), 2nd oldest=bullish -> return 4)"
        streaks[0] == 4 // bullishBbStreak
    }

    def "should handle mixed BB data availability correctly"() {
        given: "OHLCV data where BB data becomes null partway through"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - Phase 1: qualified
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 151.0, 155.0, 150.0, 152.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0), // qualified, bullish

            // Day 2 - Phase 1: qualified
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 149.5, 153.0, 149.0, 151.0, 1000000L,
                     149.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0), // qualified, bullish

            // Day 3 - null BB data should terminate Phase 1
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 147.0, 150.0, 146.0, 149.0, 1000000L,
                     null, null, null, null, 23.0, 17.0, 33.0, 18.0), // bb_middle=null

            // Day 4 - would qualify if reached
            new OHLCV(symbol, LocalDate.of(2024, 1, 2), 147.5, 149.0, 147.0, 148.0, 1000000L,
                     147.0, 2.0, 151.0, 143.0, 22.0, 18.0, 32.0, 17.0)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 2 days (terminated by null BB data, both qualified days are bullish)"
        streaks[0] == 2 // bullishBbStreak
    }

    def "should calculate DMI bullish streak correctly"() {
        given: "OHLCV data with DMI indicators"
        String symbol = "MSFT"
        List<OHLCV> ohlcvData = [
            // Most recent day - meets DMI conditions (+DI > -DI AND +DI > ADX)
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 300.0, 305.0, 298.0, 302.0, 1000000L,
                     null, null, null, null, 30.0, 15.0, 35.0, 25.0), // +DI=30 > -DI=15 AND +DI=30 > ADX=25
            
            // Day 2 - meets DMI conditions
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 298.0, 303.0, 297.0, 301.0, 1000000L,
                     null, null, null, null, 28.0, 16.0, 34.0, 24.0), // +DI=28 > -DI=16 AND +DI=28 > ADX=24
            
            // Day 3 - breaks streak (+DI < ADX)
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 296.0, 300.0, 295.0, 299.0, 1000000L,
                     null, null, null, null, 20.0, 18.0, 33.0, 25.0)  // +DI=20 < ADX=25 (fails condition)
        ]
        
        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "DMI bullish streak should be 2 days"
        streaks[1] == 2 // dmiBullishStreak
    }

    def "should calculate combined signal streak correctly with updated BB logic"() {
        given: "OHLCV data where both updated Bollinger and DMI conditions are met"
        String symbol = "GOOGL"
        List<OHLCV> ohlcvData = [
            // Most recent day - meets both updated BB and DMI conditions
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 2815.0, 2850.0, 2810.0, 2820.0, 1000000L,
                     2810.0, 20.0, 2850.0, 2770.0, 32.0, 18.0, 36.0, 28.0), // BB: open=2815>2810 & close=2820>2810 & bullish, DMI: 32>18 & 32>28

            // Day 2 - meets both conditions
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 2805.0, 2830.0, 2800.0, 2810.0, 1000000L,
                     2800.0, 20.0, 2840.0, 2760.0, 30.0, 19.0, 35.0, 27.0), // BB: open=2805>2800 & close=2810>2800 & bullish, DMI: 30>19 & 30>27

            // Day 3 - breaks combined streak (DMI fails)
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 2785.0, 2800.0, 2780.0, 2790.0, 1000000L,
                     2780.0, 20.0, 2820.0, 2740.0, 25.0, 20.0, 34.0, 30.0)  // BB: open=2785>2780 & close=2790>2780 & bullish, DMI: 25<30 (fails)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "combined signal streak should be 2 days"
        streaks[2] == 2 // combinedSignalStreak
    }

    def "should calculate combined signal streak with BB condition failing due to open price"() {
        given: "OHLCV data where BB condition fails due to open price not above BB middle"
        String symbol = "GOOGL"
        List<OHLCV> ohlcvData = [
            // Most recent day - BB condition fails (open < bb_middle)
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 2805.0, 2850.0, 2800.0, 2820.0, 1000000L,
                     2810.0, 20.0, 2850.0, 2770.0, 32.0, 18.0, 36.0, 28.0), // BB: open=2805<2810 (fails), DMI: 32>18 & 32>28

            // Day 2 - would meet conditions if reached
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 2815.0, 2830.0, 2810.0, 2825.0, 1000000L,
                     2800.0, 20.0, 2840.0, 2760.0, 30.0, 19.0, 35.0, 27.0)
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "combined signal streak should be 0 (BB condition fails immediately)"
        streaks[2] == 0 // combinedSignalStreak
    }

    def "should return zero streaks when no technical indicator data is available"() {
        given: "OHLCV data without technical indicators"
        String symbol = "TSLA"
        List<OHLCV> ohlcvData = [
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 200.0, 205.0, 198.0, 202.0, 1000000L),
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 198.0, 203.0, 197.0, 201.0, 1000000L)
        ]
        
        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "all streaks should be zero"
        streaks[0] == 0 // bullishBbStreak
        streaks[1] == 0 // dmiBullishStreak
        streaks[2] == 0 // combinedSignalStreak
    }

    def "should return zero streaks when no OHLCV data is available"() {
        given: "no OHLCV data"
        String symbol = "NVDA"
        databaseManager.getOHLCVData(symbol, _, _) >> []

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "all streaks should be zero"
        streaks[0] == 0 // bullishBbStreak
        streaks[1] == 0 // dmiBullishStreak
        streaks[2] == 0 // combinedSignalStreak
    }

    def "should update watch list technical signals correctly"() {
        given: "a watch list item and calculated streaks"
        Long watchListId = 1L
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Data that meets all conditions: BB (open & close > bb_middle & bullish), DMI (+DI > -DI & +DI > ADX)
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 151.0, 155.0, 150.0, 152.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0) // open=151>150, close=152>150, bullish, +DI=25>-DI=15, +DI=25>ADX=20
        ]

        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "updating technical signal streaks"
        technicalSignalService.updateTechnicalSignalStreaks(watchListId, symbol)

        then: "database should be updated with calculated streaks"
        1 * databaseManager.updateWatchListTechnicalSignals(watchListId, 1, 1, 1)
    }
}
