package com.investment.service

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDate

/**
 * Test specification for TechnicalSignalService.
 * Tests the calculation of technical signal streaks for BUY indicators.
 */
class TechnicalSignalServiceSpec extends Specification {

    DatabaseManager databaseManager = Mock()
    
    @Subject
    TechnicalSignalService technicalSignalService = new TechnicalSignalService(databaseManager)

    def "should calculate bullish Bollinger streak correctly"() {
        given: "OHLCV data with Bollinger Band and price data"
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            // Most recent day - meets both conditions (close > bb_middle AND close > open)
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 150.0, 155.0, 148.0, 152.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0), // close=152 > bb_middle=150, close=152 > open=150
            
            // Day 2 - meets both conditions
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 148.0, 153.0, 147.0, 151.0, 1000000L,
                     149.0, 2.0, 153.0, 145.0, 24.0, 16.0, 34.0, 19.0), // close=151 > bb_middle=149, close=151 > open=148
            
            // Day 3 - meets both conditions
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 146.0, 150.0, 145.0, 149.0, 1000000L,
                     147.0, 2.0, 151.0, 143.0, 23.0, 17.0, 33.0, 18.0), // close=149 > bb_middle=147, close=149 > open=146
            
            // Day 4 - breaks streak (close < open, bearish candle)
            new OHLCV(symbol, LocalDate.of(2024, 1, 2), 148.0, 149.0, 144.0, 145.0, 1000000L,
                     146.0, 2.0, 150.0, 142.0, 22.0, 18.0, 32.0, 17.0)  // close=145 < open=148 (bearish)
        ]
        
        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "bullish Bollinger streak should be 3 days"
        streaks[0] == 3 // bullishBbStreak
    }

    def "should calculate DMI bullish streak correctly"() {
        given: "OHLCV data with DMI indicators"
        String symbol = "MSFT"
        List<OHLCV> ohlcvData = [
            // Most recent day - meets DMI conditions (+DI > -DI AND +DI > ADX)
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 300.0, 305.0, 298.0, 302.0, 1000000L,
                     null, null, null, null, 30.0, 15.0, 35.0, 25.0), // +DI=30 > -DI=15 AND +DI=30 > ADX=25
            
            // Day 2 - meets DMI conditions
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 298.0, 303.0, 297.0, 301.0, 1000000L,
                     null, null, null, null, 28.0, 16.0, 34.0, 24.0), // +DI=28 > -DI=16 AND +DI=28 > ADX=24
            
            // Day 3 - breaks streak (+DI < ADX)
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 296.0, 300.0, 295.0, 299.0, 1000000L,
                     null, null, null, null, 20.0, 18.0, 33.0, 25.0)  // +DI=20 < ADX=25 (fails condition)
        ]
        
        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "DMI bullish streak should be 2 days"
        streaks[1] == 2 // dmiBullishStreak
    }

    def "should calculate combined signal streak correctly"() {
        given: "OHLCV data where both Bollinger and DMI conditions are met"
        String symbol = "GOOGL"
        List<OHLCV> ohlcvData = [
            // Most recent day - meets both BB and DMI conditions
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 2800.0, 2850.0, 2780.0, 2820.0, 1000000L,
                     2810.0, 20.0, 2850.0, 2770.0, 32.0, 18.0, 36.0, 28.0), // BB: 2820>2810 & 2820>2800, DMI: 32>18 & 32>28
            
            // Day 2 - meets both conditions
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 2780.0, 2830.0, 2770.0, 2810.0, 1000000L,
                     2800.0, 20.0, 2840.0, 2760.0, 30.0, 19.0, 35.0, 27.0), // BB: 2810>2800 & 2810>2780, DMI: 30>19 & 30>27
            
            // Day 3 - breaks combined streak (DMI fails)
            new OHLCV(symbol, LocalDate.of(2024, 1, 3), 2760.0, 2800.0, 2750.0, 2790.0, 1000000L,
                     2780.0, 20.0, 2820.0, 2740.0, 25.0, 20.0, 34.0, 30.0)  // BB: 2790>2780 & 2790>2760, DMI: 25<30 (fails)
        ]
        
        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "combined signal streak should be 2 days"
        streaks[2] == 2 // combinedSignalStreak
    }

    def "should return zero streaks when no technical indicator data is available"() {
        given: "OHLCV data without technical indicators"
        String symbol = "TSLA"
        List<OHLCV> ohlcvData = [
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 200.0, 205.0, 198.0, 202.0, 1000000L),
            new OHLCV(symbol, LocalDate.of(2024, 1, 4), 198.0, 203.0, 197.0, 201.0, 1000000L)
        ]
        
        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "all streaks should be zero"
        streaks[0] == 0 // bullishBbStreak
        streaks[1] == 0 // dmiBullishStreak
        streaks[2] == 0 // combinedSignalStreak
    }

    def "should return zero streaks when no OHLCV data is available"() {
        given: "no OHLCV data"
        String symbol = "NVDA"
        databaseManager.getOHLCVData(symbol, _, _) >> []

        when: "calculating technical signal streaks"
        int[] streaks = technicalSignalService.calculateTechnicalSignalStreaks(symbol)

        then: "all streaks should be zero"
        streaks[0] == 0 // bullishBbStreak
        streaks[1] == 0 // dmiBullishStreak
        streaks[2] == 0 // combinedSignalStreak
    }

    def "should update watch list technical signals correctly"() {
        given: "a watch list item and calculated streaks"
        Long watchListId = 1L
        String symbol = "AAPL"
        List<OHLCV> ohlcvData = [
            new OHLCV(symbol, LocalDate.of(2024, 1, 5), 150.0, 155.0, 148.0, 152.0, 1000000L,
                     150.0, 2.0, 154.0, 146.0, 25.0, 15.0, 35.0, 20.0)
        ]
        
        databaseManager.getOHLCVData(symbol, _, _) >> ohlcvData

        when: "updating technical signal streaks"
        technicalSignalService.updateTechnicalSignalStreaks(watchListId, symbol)

        then: "database should be updated with calculated streaks"
        1 * databaseManager.updateWatchListTechnicalSignals(watchListId, 1, 1, 1)
    }
}
