package com.investment.service

import com.investment.api.model.SyncResponse
import com.investment.database.DatabaseManager
import com.investment.model.SecCompany
import spock.lang.Specification
import spock.lang.Subject

/**
 * Spock specification for testing the SecSynchronizationService.
 */
class SecSynchronizationServiceSpec extends Specification {

    @Subject
    SecSynchronizationService service

    DatabaseManager mockDatabaseManager

    def setup() {
        mockDatabaseManager = Mock(DatabaseManager)
        service = new SecSynchronizationService(mockDatabaseManager)
    }

    def "should identify missing symbols in dry run mode"() {
        given: "a database with some existing symbols"
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "GOOGL", "MSFT"]

        and: "mock SEC data with additional symbols"
        // We need to mock the SEC data download and parsing
        // This is a simplified test - in reality we'd need to mock the HTTP client
        
        when: "performing dry run synchronization"
        // Note: This test would need more sophisticated mocking of the HTTP client
        // For now, we'll test the core logic separately
        def existingSymbols = ["AAPL", "GOOGL", "MSFT"] as Set
        def secSymbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"] as Set
        def missingSymbols = secSymbols - existingSymbols

        then: "should identify missing symbols correctly"
        missingSymbols.size() == 2
        missingSymbols.contains("TSLA")
        missingSymbols.contains("AMZN")
    }

    def "should respect maxInstruments limit"() {
        given: "a large number of missing symbols"
        def missingSymbols = (1..1500).collect { "SYM${it}" }
        def maxInstruments = 1000

        when: "limiting symbols to process"
        def symbolsToProcess = missingSymbols.stream()
                .limit(maxInstruments)
                .collect()

        then: "should limit to maxInstruments"
        symbolsToProcess.size() == maxInstruments
        symbolsToProcess.every { it.startsWith("SYM") }
    }

    def "should create proper SyncResponse for dry run"() {
        given: "synchronization parameters"
        def totalSecSymbols = 5000
        def existingInDatabase = 100
        def missingFromDatabase = 50
        def missingSymbolsList = (1..50).collect { "MISSING${it}" }
        def addedSymbols = 0 // dry run
        def dryRun = true

        when: "creating sync response"
        def response = new SyncResponse(
                totalSecSymbols,
                existingInDatabase,
                missingFromDatabase,
                missingSymbolsList,
                addedSymbols,
                dryRun
        )

        then: "should have correct values"
        response.totalSecSymbols == 5000
        response.existingInDatabase == 100
        response.missingFromDatabase == 50
        response.missingSymbolsList.size() == 50
        response.addedSymbols == 0
        response.dryRun == true
        response.summary.contains("DRY RUN")
        response.summary.contains("50 missing symbols")
        response.timestamp != null
    }

    def "should create proper SyncResponse for actual sync"() {
        given: "synchronization parameters for actual sync"
        def totalSecSymbols = 5000
        def existingInDatabase = 100
        def missingFromDatabase = 50
        def missingSymbolsList = (1..25).collect { "ADDED${it}" } // Only first 25 processed
        def addedSymbols = 25 // actual sync
        def dryRun = false

        when: "creating sync response"
        def response = new SyncResponse(
                totalSecSymbols,
                existingInDatabase,
                missingFromDatabase,
                missingSymbolsList,
                addedSymbols,
                dryRun
        )

        then: "should have correct values"
        response.totalSecSymbols == 5000
        response.existingInDatabase == 100
        response.missingFromDatabase == 50
        response.missingSymbolsList.size() == 25
        response.addedSymbols == 25
        response.dryRun == false
        response.summary.contains("SYNC COMPLETED")
        response.summary.contains("Added 25 new instruments")
        response.timestamp != null
    }

    def "should handle empty missing symbols list"() {
        given: "no missing symbols"
        def totalSecSymbols = 5000
        def existingInDatabase = 5000
        def missingFromDatabase = 0
        def missingSymbolsList = []
        def addedSymbols = 0
        def dryRun = true

        when: "creating sync response"
        def response = new SyncResponse(
                totalSecSymbols,
                existingInDatabase,
                missingFromDatabase,
                missingSymbolsList,
                addedSymbols,
                dryRun
        )

        then: "should handle empty list correctly"
        response.missingFromDatabase == 0
        response.missingSymbolsList.isEmpty()
        response.addedSymbols == 0
        response.summary.contains("0 missing symbols")
    }

    def "should validate cache status structure"() {
        when: "getting cache status"
        def cacheStatus = service.getCacheStatus()

        then: "should contain expected keys"
        cacheStatus.containsKey("cacheDirectory")
        cacheStatus.containsKey("cacheFile")
        cacheStatus.containsKey("cacheExpiry")
        cacheStatus.containsKey("lastCacheUpdate")
        cacheStatus.containsKey("cachedCompaniesCount")
        cacheStatus.containsKey("cacheFileExists")
        
        and: "should have correct values"
        cacheStatus.cacheDirectory == "./data/sec_cache"
        cacheStatus.cacheFile == "company_tickers.json"
        cacheStatus.cacheExpiry == "PT24H"
        cacheStatus.cachedCompaniesCount == 0 // No data loaded yet
    }
}
