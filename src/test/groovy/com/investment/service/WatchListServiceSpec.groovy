package com.investment.service

import com.investment.api.model.CreateWatchListRequest
import com.investment.api.model.UpdateWatchListRequest
import com.investment.database.DatabaseManager
import com.investment.model.WatchListItem
import spock.lang.Specification

import java.math.BigDecimal
import java.sql.SQLException
import java.sql.Timestamp
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test specification for WatchListService.
 */
class WatchListServiceSpec extends Specification {

    WatchListService watchListService
    DatabaseManager databaseManager

    def setup() {
        databaseManager = Mock(DatabaseManager)
        watchListService = new WatchListService(databaseManager)
    }

    def "should create watch list item successfully"() {
        given: "a valid create watch list request"
        def request = new CreateWatchListRequest(1, "AAPL", LocalDate.of(2024, 1, 15), "Strong growth potential")

        and: "symbol exists in instruments table"
        databaseManager.symbolExists("AAPL") >> true

        and: "symbol does not exist in watch list"
        databaseManager.symbolExistsInWatchList("AAPL") >> false

        and: "database creates watch list item successfully"
        databaseManager.createWatchListItem(1, "AAPL", _, "Strong growth potential") >> 1L

        when: "creating the watch list item"
        def item = watchListService.createWatchListItem(request)

        then: "item should be created with correct data"
        item.id == 1L
        item.displayIndex == 1
        item.symbol == "AAPL"
        item.startDate == LocalDate.of(2024, 1, 15)
        item.remarks == "Strong growth potential"
    }

    def "should throw exception when symbol does not exist in instruments"() {
        given: "a create watch list request with non-existent symbol"
        def request = new CreateWatchListRequest(1, "INVALID", LocalDate.now())

        and: "symbol does not exist in instruments table"
        databaseManager.symbolExists("INVALID") >> false

        when: "creating the watch list item"
        watchListService.createWatchListItem(request)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Symbol not found in instruments: INVALID")
    }

    def "should throw exception when symbol already exists in watch list"() {
        given: "a create watch list request"
        def request = new CreateWatchListRequest(1, "AAPL", LocalDate.now())

        and: "symbol exists in instruments table"
        databaseManager.symbolExists("AAPL") >> true

        and: "symbol already exists in watch list"
        databaseManager.symbolExistsInWatchList("AAPL") >> true

        when: "creating the watch list item"
        watchListService.createWatchListItem(request)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Symbol already exists in watch list: AAPL")
    }

    def "should get watch list item by ID successfully"() {
        given: "a watch list item ID"
        def itemId = 1L

        and: "database returns watch list item data"
        def itemData = [
                id: 1L,
                display_index: 1,
                symbol: "AAPL",
                start_date: java.sql.Date.valueOf(LocalDate.of(2024, 1, 15)),
                remarks: "Strong growth potential",
                "one_mo_perf": new BigDecimal("0.0523"),
                "three_mo_perf": new BigDecimal("0.1245"),
                "six_mo_perf": new BigDecimal("0.2187"),
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]
        
        databaseManager.getWatchListItemById(itemId) >> itemData

        when: "getting watch list item by ID"
        def optionalItem = watchListService.getWatchListItemById(itemId)

        then: "should return item with correct data"
        optionalItem.isPresent()
        def item = optionalItem.get()
        item.id == 1L
        item.displayIndex == 1
        item.symbol == "AAPL"
        item.startDate == LocalDate.of(2024, 1, 15)
        item.remarks == "Strong growth potential"
        item.oneMonthPerf == new BigDecimal("0.0523")
        item.threeMonthPerf == new BigDecimal("0.1245")
        item.sixMonthPerf == new BigDecimal("0.2187")
    }

    def "should return empty optional when watch list item not found"() {
        given: "a watch list item ID that doesn't exist"
        def itemId = 999L

        and: "database returns no results"
        databaseManager.getWatchListItemById(itemId) >> null

        when: "getting watch list item by ID"
        def optionalItem = watchListService.getWatchListItemById(itemId)

        then: "should return empty optional"
        !optionalItem.isPresent()
    }

    def "should get all watch list items successfully"() {
        given: "database returns watch list items"
        def itemsData = [
                [
                        id: 1L,
                        display_index: 1,
                        symbol: "AAPL",
                        start_date: java.sql.Date.valueOf(LocalDate.of(2024, 1, 15)),
                        remarks: "Strong growth potential",
                        "one_mo_perf": null,
                        "three_mo_perf": null,
                        "six_mo_perf": null,
                        created_date: Timestamp.valueOf(LocalDateTime.now()),
                        updated_date: Timestamp.valueOf(LocalDateTime.now())
                ],
                [
                        id: 2L,
                        display_index: 2,
                        symbol: "MSFT",
                        start_date: java.sql.Date.valueOf(LocalDate.of(2024, 1, 10)),
                        remarks: "Cloud leader",
                        "one_mo_perf": null,
                        "three_mo_perf": null,
                        "six_mo_perf": null,
                        created_date: Timestamp.valueOf(LocalDateTime.now()),
                        updated_date: Timestamp.valueOf(LocalDateTime.now())
                ]
        ]
        
        databaseManager.getWatchListItems() >> itemsData

        when: "getting all watch list items"
        def items = watchListService.getAllWatchListItems()

        then: "should return all items"
        items.size() == 2
        items[0].symbol == "AAPL"
        items[0].displayIndex == 1
        items[1].symbol == "MSFT"
        items[1].displayIndex == 2
    }

    def "should update watch list item successfully"() {
        given: "an existing watch list item"
        def itemId = 1L
        def updateRequest = new UpdateWatchListRequest()
        updateRequest.setDisplayIndex(5)
        updateRequest.setRemarks("Updated analysis")
        updateRequest.setOneMonthPerf(new BigDecimal("0.08"))

        and: "item exists in database"
        def itemData = [
                id: itemId,
                display_index: 1,
                symbol: "AAPL",
                start_date: java.sql.Date.valueOf(LocalDate.of(2024, 1, 15)),
                remarks: "Original remarks",
                "one_mo_perf": null,
                "three_mo_perf": null,
                "six_mo_perf": null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]
        
        databaseManager.getWatchListItemById(itemId) >> itemData

        when: "updating the watch list item"
        def updatedItem = watchListService.updateWatchListItem(itemId, updateRequest)

        then: "item should be updated"
        updatedItem.id == itemId
        updatedItem.displayIndex == 5
        updatedItem.remarks == "Updated analysis"
        updatedItem.oneMonthPerf == new BigDecimal("0.08")

        and: "database should be updated"
        1 * databaseManager.updateWatchListItem(itemId, 5, "Updated analysis", new BigDecimal("0.08"), null, null)
    }

    def "should throw exception when updating non-existent watch list item"() {
        given: "a non-existent watch list item ID"
        def itemId = 999L
        def updateRequest = new UpdateWatchListRequest()
        updateRequest.setDisplayIndex(5)

        and: "item does not exist"
        databaseManager.getWatchListItemById(itemId) >> null

        when: "updating the watch list item"
        watchListService.updateWatchListItem(itemId, updateRequest)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Watch list item not found: 999")
    }

    def "should throw exception when no update fields provided"() {
        given: "an empty update request"
        def itemId = 1L
        def updateRequest = new UpdateWatchListRequest()

        when: "updating the watch list item"
        watchListService.updateWatchListItem(itemId, updateRequest)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message == "No update fields provided"
    }

    def "should update performance metrics successfully"() {
        given: "an existing watch list item"
        def itemId = 1L
        def oneMonthPerf = new BigDecimal("0.05")
        def threeMonthPerf = new BigDecimal("0.12")
        def sixMonthPerf = new BigDecimal("0.18")

        and: "item exists in database"
        def itemData = [
                id: itemId,
                display_index: 1,
                symbol: "AAPL",
                start_date: java.sql.Date.valueOf(LocalDate.of(2024, 1, 15)),
                remarks: "Strong growth potential",
                "one_mo_perf": null,
                "three_mo_perf": null,
                "six_mo_perf": null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]
        
        databaseManager.getWatchListItemById(itemId) >> itemData

        when: "updating performance metrics"
        def updatedItem = watchListService.updateWatchListPerformance(itemId, oneMonthPerf, threeMonthPerf, sixMonthPerf)

        then: "performance should be updated"
        updatedItem.oneMonthPerf == oneMonthPerf
        updatedItem.threeMonthPerf == threeMonthPerf
        updatedItem.sixMonthPerf == sixMonthPerf

        and: "database should be updated"
        1 * databaseManager.updateWatchListPerformance(itemId, oneMonthPerf, threeMonthPerf, sixMonthPerf)
    }

    def "should delete watch list item successfully"() {
        given: "a watch list item ID"
        def itemId = 1L

        and: "database deletes item successfully"
        databaseManager.deleteWatchListItem(itemId) >> true

        when: "deleting the watch list item"
        def deleted = watchListService.deleteWatchListItem(itemId)

        then: "should return true"
        deleted
    }

    def "should return false when deleting non-existent watch list item"() {
        given: "a non-existent watch list item ID"
        def itemId = 999L

        and: "database returns false for deletion"
        databaseManager.deleteWatchListItem(itemId) >> false

        when: "deleting the watch list item"
        def deleted = watchListService.deleteWatchListItem(itemId)

        then: "should return false"
        !deleted
    }

    def "should reorder watch list items successfully"() {
        given: "a mapping of IDs to new indexes"
        def idToIndexMap = [1L: 0, 2L: 1, 3L: 2]

        and: "all items exist in database"
        databaseManager.getWatchListItemById(1L) >> [id: 1L, display_index: 1, symbol: "AAPL", start_date: java.sql.Date.valueOf(LocalDate.now()), remarks: null, "one_mo_perf": null, "three_mo_perf": null, "six_mo_perf": null, created_date: Timestamp.valueOf(LocalDateTime.now()), updated_date: Timestamp.valueOf(LocalDateTime.now())]
        databaseManager.getWatchListItemById(2L) >> [id: 2L, display_index: 2, symbol: "MSFT", start_date: java.sql.Date.valueOf(LocalDate.now()), remarks: null, "one_mo_perf": null, "three_mo_perf": null, "six_mo_perf": null, created_date: Timestamp.valueOf(LocalDateTime.now()), updated_date: Timestamp.valueOf(LocalDateTime.now())]
        databaseManager.getWatchListItemById(3L) >> [id: 3L, display_index: 3, symbol: "GOOGL", start_date: java.sql.Date.valueOf(LocalDate.now()), remarks: null, "one_mo_perf": null, "three_mo_perf": null, "six_mo_perf": null, created_date: Timestamp.valueOf(LocalDateTime.now()), updated_date: Timestamp.valueOf(LocalDateTime.now())]

        when: "reordering watch list items"
        watchListService.reorderWatchListItems(idToIndexMap)

        then: "database should be updated"
        1 * databaseManager.updateWatchListDisplayIndexes(idToIndexMap)
    }

    def "should throw exception when reordering with non-existent item"() {
        given: "a mapping with non-existent ID"
        def idToIndexMap = [999L: 0]

        and: "item does not exist"
        databaseManager.getWatchListItemById(999L) >> null

        when: "reordering watch list items"
        watchListService.reorderWatchListItems(idToIndexMap)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Watch list item not found: 999")
    }

    def "should handle SQL exceptions gracefully"() {
        given: "database throws SQLException"
        databaseManager.getWatchListItemById(1L) >> { throw new SQLException("Database error") }

        when: "performing database operation"
        watchListService.getWatchListItemById(1L)

        then: "should propagate SQLException"
        thrown(SQLException)
    }
}
