package com.investment.service

import com.investment.api.model.CreateInstrumentRequest
import com.investment.database.DatabaseManager
import com.investment.model.Instrument
import com.investment.model.InstrumentType
import spock.lang.Specification
import spock.lang.Subject

import java.math.BigDecimal
import java.sql.SQLException

class InstrumentServiceSpec extends Specification {

    DatabaseManager databaseManager = Mock()
    
    @Subject
    InstrumentService instrumentService = new InstrumentService(databaseManager: databaseManager)

    def "should create instrument successfully with all fields"() {
        given: "a valid create instrument request"
        def request = new CreateInstrumentRequest(
                symbol: "AAPL",
                name: "Apple Inc.",
                type: InstrumentType.US_STOCK,
                marketCap: new BigDecimal("2500000000000"),
                country: "United States",
                ipoYear: 1980,
                sector: "Technology",
                industry: "Consumer Electronics"
        )

        when: "creating the instrument"
        def result = instrumentService.createInstrument(request)

        then: "should check if symbol exists"
        1 * databaseManager.symbolExists("AAPL") >> false

        and: "should save instrument to database"
        1 * databaseManager.saveInstrumentWithDetails(
                "AAPL",
                "Apple Inc.",
                "US_STOCK",
                new BigDecimal("2500000000000"),
                "United States",
                1980,
                "Technology",
                "Consumer Electronics"
        )

        and: "should return created instrument"
        result != null
        result.symbol == "AAPL"
        result.name == "Apple Inc."
        result.type == InstrumentType.US_STOCK
        result.marketCap == new BigDecimal("2500000000000")
        result.country == "United States"
        result.ipoYear == 1980
        result.sector == "Technology"
        result.industry == "Consumer Electronics"
    }

    def "should create instrument successfully with required fields only"() {
        given: "a request with only required fields"
        def request = new CreateInstrumentRequest(
                symbol: "MSFT",
                name: "Microsoft Corporation",
                type: InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        def result = instrumentService.createInstrument(request)

        then: "should check if symbol exists"
        1 * databaseManager.symbolExists("MSFT") >> false

        and: "should save instrument with null optional fields"
        1 * databaseManager.saveInstrumentWithDetails(
                "MSFT",
                "Microsoft Corporation",
                "US_STOCK",
                null,
                null,
                null,
                null,
                null
        )

        and: "should return created instrument"
        result != null
        result.symbol == "MSFT"
        result.name == "Microsoft Corporation"
        result.type == InstrumentType.US_STOCK
        result.marketCap == null
        result.country == null
        result.ipoYear == null
        result.sector == null
        result.industry == null
    }

    def "should normalize symbol to uppercase"() {
        given: "a request with lowercase symbol"
        def request = new CreateInstrumentRequest(
                symbol: "googl",
                name: "Alphabet Inc.",
                type: InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        instrumentService.createInstrument(request)

        then: "should check uppercase symbol"
        1 * databaseManager.symbolExists("GOOGL") >> false

        and: "should save with uppercase symbol"
        1 * databaseManager.saveInstrumentWithDetails(
                "GOOGL",
                "Alphabet Inc.",
                "US_STOCK",
                null,
                null,
                null,
                null,
                null
        )
    }

    def "should throw exception when symbol already exists"() {
        given: "a request with existing symbol"
        def request = new CreateInstrumentRequest(
                symbol: "AAPL",
                name: "Apple Inc.",
                type: InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        instrumentService.createInstrument(request)

        then: "should check if symbol exists"
        1 * databaseManager.symbolExists("AAPL") >> true

        and: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Symbol already exists: AAPL")

        and: "should not save to database"
        0 * databaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)
    }

    def "should validate symbol format"() {
        given: "a request with invalid symbol"
        def request = new CreateInstrumentRequest(
                symbol: "INVALID@SYMBOL",
                name: "Test Company",
                type: InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        instrumentService.createInstrument(request)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Symbol must contain only uppercase letters, numbers, dots, and hyphens")

        and: "should not save to database"
        0 * databaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)
    }

    def "should validate negative market cap"() {
        given: "a request with negative market cap"
        def request = new CreateInstrumentRequest(
                symbol: "TEST",
                name: "Test Company",
                type: InstrumentType.US_STOCK,
                marketCap: new BigDecimal("-1000")
        )

        when: "creating the instrument"
        instrumentService.createInstrument(request)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Market cap cannot be negative")

        and: "should not save to database"
        0 * databaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)
    }

    def "should validate future IPO year"() {
        given: "a request with future IPO year"
        def futureYear = new Date().year + 1900 + 10
        def request = new CreateInstrumentRequest(
                symbol: "TEST",
                name: "Test Company",
                type: InstrumentType.US_STOCK,
                ipoYear: futureYear
        )

        when: "creating the instrument"
        instrumentService.createInstrument(request)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("IPO year cannot be in the future")

        and: "should not save to database"
        0 * databaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)
    }

    def "should handle database errors"() {
        given: "a valid request"
        def request = new CreateInstrumentRequest(
                symbol: "TEST",
                name: "Test Company",
                type: InstrumentType.US_STOCK
        )

        when: "creating the instrument"
        instrumentService.createInstrument(request)

        then: "should check if symbol exists"
        1 * databaseManager.symbolExists("TEST") >> false

        and: "database throws exception"
        1 * databaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _) >> {
            throw new RuntimeException("Database error")
        }

        and: "should throw SQLException"
        def exception = thrown(SQLException)
        exception.message.contains("Failed to create instrument")
    }

    def "should check if symbol exists"() {
        when: "checking if symbol exists"
        def result = instrumentService.symbolExists("AAPL")

        then: "should delegate to database manager"
        1 * databaseManager.symbolExists("AAPL") >> true
        result == true
    }

    def "should handle null symbol in exists check"() {
        when: "checking null symbol"
        def result = instrumentService.symbolExists(null)

        then: "should return false without database call"
        0 * databaseManager.symbolExists(_)
        result == false
    }

    def "should get instrument by symbol"() {
        given: "an existing instrument"
        def instrument = new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK)
        def allInstruments = [instrument]

        when: "getting instrument by symbol"
        def result = instrumentService.getInstrumentBySymbol("AAPL")

        then: "should get all instruments and filter"
        1 * databaseManager.getAllInstruments() >> allInstruments
        result == instrument
    }

    def "should handle null symbol in get by symbol"() {
        when: "getting instrument with null symbol"
        def result = instrumentService.getInstrumentBySymbol(null)

        then: "should return null without database call"
        0 * databaseManager.getAllInstruments()
        result == null
    }

    def "should return null when symbol not found"() {
        given: "instruments without the requested symbol"
        def allInstruments = [
            new Instrument("MSFT", "Microsoft", InstrumentType.US_STOCK),
            new Instrument("GOOGL", "Google", InstrumentType.US_STOCK)
        ]

        when: "getting instrument by non-existent symbol"
        def result = instrumentService.getInstrumentBySymbol("AAPL")

        then: "should get all instruments and return null"
        1 * databaseManager.getAllInstruments() >> allInstruments
        result == null
    }
}
