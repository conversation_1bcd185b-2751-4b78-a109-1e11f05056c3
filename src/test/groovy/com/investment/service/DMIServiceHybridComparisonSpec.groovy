package com.investment.service

import com.investment.api.model.DMIRequest
import com.investment.api.model.DMIResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import spock.lang.Specification
import spock.lang.Shared

import java.time.LocalDate

/**
 * Comprehensive test specification comparing Pure Java vs Hybrid SQL+Java DMI calculations.
 * Ensures both approaches produce mathematically identical results.
 */
class DMIServiceHybridComparisonSpec extends Specification {

    @Shared
    DatabaseManager databaseManager

    @Shared
    DMIService dmiService

    def setupSpec() {
        // Clean up any existing test database
        new File("./data/dmi_comparison_test.duckdb").delete()
        
        // Initialize test database
        DatabaseManager.setDbUrl("*********************************************")
        databaseManager = new DatabaseManager()
        databaseManager.initDatabase()
        
        dmiService = new DMIService(databaseManager)
    }

    def cleanupSpec() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/dmi_comparison_test.duckdb").delete()
    }

    def setup() {
        // Clean up any existing test data
        try {
            databaseManager.connection.createStatement().execute("DELETE FROM ohlcv WHERE symbol LIKE 'CMP_%'")
            databaseManager.connection.createStatement().execute("DELETE FROM instruments WHERE symbol LIKE 'CMP_%'")
        } catch (Exception e) {
            // Ignore cleanup errors
        }
    }

    def "should produce identical results for Pure Java vs Hybrid SQL+Java approaches with realistic market data"() {
        given: "realistic market data for comparison"
        String baseSymbol = "CMP_REALISTIC"
        List<OHLCV> marketData = createRealisticMarketData(baseSymbol, 200) // 200 days for comprehensive testing

        when: "setting up identical data for both calculation methods"
        String javaSymbol = "${baseSymbol}_JAVA"
        String hybridSymbol = "${baseSymbol}_HYBRID"
        
        // Create identical datasets
        List<OHLCV> javaData = marketData.collect { ohlcv ->
            new OHLCV(javaSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> hybridData = marketData.collect { ohlcv ->
            new OHLCV(hybridSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        
        // Save instruments and data
        databaseManager.saveInstrument(javaSymbol, "Java Test Company", "Technology")
        databaseManager.saveInstrument(hybridSymbol, "Hybrid Test Company", "Technology")
        databaseManager.saveOHLCVData(javaData)
        databaseManager.saveOHLCVData(hybridData)

        and: "calculating DMI using Pure Java approach"
        DMIRequest javaRequest = new DMIRequest()
        javaRequest.period = 14
        javaRequest.calculationMode = DMIRequest.CalculationMode.FULL_RECALCULATION
        javaRequest.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        javaRequest.dryRun = false
        
        DMIResponse javaResponse = dmiService.calculateDMI(javaRequest)

        and: "calculating DMI using Hybrid SQL+Java approach"
        DMIRequest hybridRequest = new DMIRequest()
        hybridRequest.period = 14
        hybridRequest.calculationMode = DMIRequest.CalculationMode.FULL_RECALCULATION
        hybridRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        hybridRequest.dryRun = false
        
        DMIResponse hybridResponse = dmiService.calculateDMI(hybridRequest)

        then: "both calculations should be successful"
        javaResponse.status == "success"
        hybridResponse.status == "success"
        javaResponse.totalRecordsUpdated > 0
        hybridResponse.totalRecordsUpdated > 0
        javaResponse.totalRecordsUpdated == hybridResponse.totalRecordsUpdated

        when: "retrieving calculated results"
        List<OHLCV> javaResults = databaseManager.getOHLCVData(javaSymbol, 
                                                               marketData[0].date, 
                                                               marketData[-1].date)
        List<OHLCV> hybridResults = databaseManager.getOHLCVData(hybridSymbol, 
                                                                  marketData[0].date, 
                                                                  marketData[-1].date)

        then: "results should be mathematically identical"
        javaResults.size() == hybridResults.size()
        javaResults.size() == marketData.size()
        
        // Compare each record
        for (int i = 0; i < javaResults.size(); i++) {
            def javaRecord = javaResults[i]
            def hybridRecord = hybridResults[i]
            
            // Dates should match exactly
            javaRecord.date == hybridRecord.date
            
            // OHLCV data should be identical
            javaRecord.open == hybridRecord.open
            javaRecord.high == hybridRecord.high
            javaRecord.low == hybridRecord.low
            javaRecord.close == hybridRecord.close
            javaRecord.volume == hybridRecord.volume
            
            // DMI values should be mathematically identical (within floating point precision)
            compareDoubleValues(javaRecord.dmiPlusDi, hybridRecord.dmiPlusDi, "Plus DI at index $i")
            compareDoubleValues(javaRecord.dmiMinusDi, hybridRecord.dmiMinusDi, "Minus DI at index $i")
            compareDoubleValues(javaRecord.dmiDx, hybridRecord.dmiDx, "DX at index $i")
            compareDoubleValues(javaRecord.dmiAdx, hybridRecord.dmiAdx, "ADX at index $i")
        }

        and: "response metadata should indicate the correct calculation methods"
        javaResponse.parameters.calculationMethod == DMIRequest.CalculationMethod.PURE_JAVA
        hybridResponse.parameters.calculationMethod == DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
    }

    def "should handle incremental calculations identically for both approaches"() {
        given: "initial market data"
        String baseSymbol = "CMP_INCREMENTAL"
        List<OHLCV> initialData = createRealisticMarketData(baseSymbol, 50)
        
        String javaSymbol = "${baseSymbol}_JAVA"
        String hybridSymbol = "${baseSymbol}_HYBRID"
        
        // Create identical initial datasets
        List<OHLCV> javaInitial = initialData.collect { ohlcv ->
            new OHLCV(javaSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> hybridInitial = initialData.collect { ohlcv ->
            new OHLCV(hybridSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }

        when: "performing initial calculations"
        databaseManager.saveInstrument(javaSymbol, "Java Incremental Test", "Technology")
        databaseManager.saveInstrument(hybridSymbol, "Hybrid Incremental Test", "Technology")
        databaseManager.saveOHLCVData(javaInitial)
        databaseManager.saveOHLCVData(hybridInitial)
        
        // Initial calculations
        DMIRequest initialRequest = new DMIRequest()
        initialRequest.period = 14
        initialRequest.calculationMode = DMIRequest.CalculationMode.FULL_RECALCULATION
        initialRequest.dryRun = false
        
        initialRequest.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        DMIResponse javaInitialResponse = dmiService.calculateDMI(initialRequest)
        
        initialRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        DMIResponse hybridInitialResponse = dmiService.calculateDMI(initialRequest)

        then: "initial calculations should be successful and identical"
        javaInitialResponse.status == "success"
        hybridInitialResponse.status == "success"
        javaInitialResponse.totalRecordsUpdated == hybridInitialResponse.totalRecordsUpdated

        when: "adding new data and performing incremental calculations"
        List<OHLCV> additionalData = createRealisticMarketData(baseSymbol, 20, initialData[-1].date.plusDays(1))
        
        List<OHLCV> javaAdditional = additionalData.collect { ohlcv ->
            new OHLCV(javaSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> hybridAdditional = additionalData.collect { ohlcv ->
            new OHLCV(hybridSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        
        databaseManager.saveOHLCVData(javaAdditional)
        databaseManager.saveOHLCVData(hybridAdditional)
        
        // Incremental calculations
        DMIRequest incrementalRequest = new DMIRequest()
        incrementalRequest.period = 14
        incrementalRequest.calculationMode = DMIRequest.CalculationMode.INCREMENTAL
        incrementalRequest.dryRun = false
        
        incrementalRequest.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        DMIResponse javaIncrementalResponse = dmiService.calculateDMI(incrementalRequest)
        
        incrementalRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        DMIResponse hybridIncrementalResponse = dmiService.calculateDMI(incrementalRequest)

        then: "incremental calculations should be successful"
        javaIncrementalResponse.status == "success"
        hybridIncrementalResponse.status == "success"
        // Note: Record counts may differ due to implementation differences in incremental processing
        // but the mathematical results should be identical

        when: "retrieving final results"
        List<OHLCV> javaFinalResults = databaseManager.getOHLCVData(javaSymbol, 
                                                                    initialData[0].date, 
                                                                    additionalData[-1].date)
        List<OHLCV> hybridFinalResults = databaseManager.getOHLCVData(hybridSymbol, 
                                                                       initialData[0].date, 
                                                                       additionalData[-1].date)

        then: "final results should be mathematically identical"
        javaFinalResults.size() == hybridFinalResults.size()
        javaFinalResults.size() == (initialData.size() + additionalData.size())
        
        // Verify that the incremental data matches
        for (int i = 0; i < javaFinalResults.size(); i++) {
            def javaRecord = javaFinalResults[i]
            def hybridRecord = hybridFinalResults[i]
            
            compareDoubleValues(javaRecord.dmiPlusDi, hybridRecord.dmiPlusDi, "Plus DI at index $i")
            compareDoubleValues(javaRecord.dmiMinusDi, hybridRecord.dmiMinusDi, "Minus DI at index $i")
            compareDoubleValues(javaRecord.dmiDx, hybridRecord.dmiDx, "DX at index $i")
            compareDoubleValues(javaRecord.dmiAdx, hybridRecord.dmiAdx, "ADX at index $i")
        }
    }

    def "should handle edge cases identically for both approaches"() {
        given: "edge case market data (minimal volatility, extreme values)"
        String baseSymbol = "CMP_EDGE"
        List<OHLCV> edgeData = createEdgeCaseMarketData(baseSymbol, 100)
        
        String javaSymbol = "${baseSymbol}_JAVA"
        String hybridSymbol = "${baseSymbol}_HYBRID"
        
        List<OHLCV> javaData = edgeData.collect { ohlcv ->
            new OHLCV(javaSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }
        List<OHLCV> hybridData = edgeData.collect { ohlcv ->
            new OHLCV(hybridSymbol, ohlcv.date, ohlcv.open, ohlcv.high, ohlcv.low, ohlcv.close, ohlcv.volume)
        }

        when: "calculating DMI for edge cases"
        databaseManager.saveInstrument(javaSymbol, "Java Edge Test", "Technology")
        databaseManager.saveInstrument(hybridSymbol, "Hybrid Edge Test", "Technology")
        databaseManager.saveOHLCVData(javaData)
        databaseManager.saveOHLCVData(hybridData)
        
        DMIRequest edgeRequest = new DMIRequest()
        edgeRequest.period = 14
        edgeRequest.calculationMode = DMIRequest.CalculationMode.FULL_RECALCULATION
        edgeRequest.dryRun = false
        
        edgeRequest.calculationMethod = DMIRequest.CalculationMethod.PURE_JAVA
        DMIResponse javaEdgeResponse = dmiService.calculateDMI(edgeRequest)
        
        edgeRequest.calculationMethod = DMIRequest.CalculationMethod.HYBRID_SQL_JAVA
        DMIResponse hybridEdgeResponse = dmiService.calculateDMI(edgeRequest)

        then: "both approaches should handle edge cases identically"
        javaEdgeResponse.status == "success"
        hybridEdgeResponse.status == "success"
        javaEdgeResponse.totalRecordsUpdated == hybridEdgeResponse.totalRecordsUpdated

        when: "retrieving edge case results"
        List<OHLCV> javaEdgeResults = databaseManager.getOHLCVData(javaSymbol, 
                                                                   edgeData[0].date, 
                                                                   edgeData[-1].date)
        List<OHLCV> hybridEdgeResults = databaseManager.getOHLCVData(hybridSymbol, 
                                                                      edgeData[0].date, 
                                                                      edgeData[-1].date)

        then: "edge case results should be mathematically identical"
        javaEdgeResults.size() == hybridEdgeResults.size()
        
        for (int i = 0; i < javaEdgeResults.size(); i++) {
            def javaRecord = javaEdgeResults[i]
            def hybridRecord = hybridEdgeResults[i]
            
            compareDoubleValues(javaRecord.dmiPlusDi, hybridRecord.dmiPlusDi, "Plus DI at index $i")
            compareDoubleValues(javaRecord.dmiMinusDi, hybridRecord.dmiMinusDi, "Minus DI at index $i")
            compareDoubleValues(javaRecord.dmiDx, hybridRecord.dmiDx, "DX at index $i")
            compareDoubleValues(javaRecord.dmiAdx, hybridRecord.dmiAdx, "ADX at index $i")
        }
    }

    /**
     * Compare two Double values with appropriate tolerance for floating point precision.
     */
    private void compareDoubleValues(Double value1, Double value2, String description) {
        if (value1 == null && value2 == null) {
            return // Both null, considered equal
        }
        
        if (value1 == null || value2 == null) {
            throw new AssertionError("$description: One value is null while the other is not (value1: $value1, value2: $value2)")
        }
        
        double tolerance = 1e-10 // Very tight tolerance for exact mathematical equivalence
        double diff = Math.abs(value1 - value2)
        
        if (diff > tolerance) {
            throw new AssertionError("$description: Values differ beyond tolerance (value1: $value1, value2: $value2, diff: $diff, tolerance: $tolerance)")
        }
    }

    /**
     * Create realistic market data with proper price movements and volatility.
     */
    private List<OHLCV> createRealisticMarketData(String symbol, int days, LocalDate startDate = LocalDate.now().minusDays(days)) {
        List<OHLCV> data = []
        double basePrice = 100.0
        Random random = new Random(42) // Fixed seed for reproducible tests

        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            // Create realistic price movements with trends and volatility
            double trendFactor = Math.sin(i * 0.1) * 0.005 // Long-term trend
            double volatility = 0.02 + Math.abs(random.nextGaussian() * 0.01) // Variable volatility
            double change = trendFactor + (random.nextGaussian() * volatility)
            
            double open = basePrice * (1 + change)
            double close = open * (1 + (random.nextGaussian() * volatility * 0.5))
            double high = Math.max(open, close) * (1 + Math.abs(random.nextGaussian() * volatility * 0.3))
            double low = Math.min(open, close) * (1 - Math.abs(random.nextGaussian() * volatility * 0.3))
            long volume = 1000000L + (long)(random.nextGaussian() * 300000)

            data.add(new OHLCV(symbol, date, open, high, low, close, Math.max(volume, 100000L)))
            basePrice = close // Use close as next day's base
        }

        return data
    }

    /**
     * Create edge case market data to test boundary conditions.
     */
    private List<OHLCV> createEdgeCaseMarketData(String symbol, int days, LocalDate startDate = LocalDate.now().minusDays(days)) {
        List<OHLCV> data = []
        double basePrice = 100.0

        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i)
            
            double open, high, low, close
            
            if (i < 20) {
                // Flat market (no movement)
                open = high = low = close = basePrice
            } else if (i < 40) {
                // Extreme volatility
                double change = (i % 2 == 0) ? 0.1 : -0.1 // 10% swings
                open = basePrice
                close = basePrice * (1 + change)
                high = Math.max(open, close) * 1.05
                low = Math.min(open, close) * 0.95
                basePrice = close
            } else if (i < 60) {
                // Gradual uptrend
                double change = 0.001 // 0.1% daily increase
                open = basePrice
                close = basePrice * (1 + change)
                high = close * 1.002
                low = open * 0.998
                basePrice = close
            } else {
                // Normal market with small movements
                double change = (Math.random() - 0.5) * 0.01 // ±0.5%
                open = basePrice
                close = basePrice * (1 + change)
                high = Math.max(open, close) * 1.005
                low = Math.min(open, close) * 0.995
                basePrice = close
            }
            
            long volume = 1000000L

            data.add(new OHLCV(symbol, date, open, high, low, close, volume))
        }

        return data
    }
}
