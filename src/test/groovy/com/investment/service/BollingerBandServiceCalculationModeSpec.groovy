package com.investment.service

import com.investment.api.model.BollingerBandRequest
import com.investment.api.model.BollingerBandResponse
import com.investment.database.DatabaseManager
import spock.lang.Specification
import spock.lang.Subject

import java.sql.SQLException
import java.time.LocalDate

class BollingerBandServiceCalculationModeSpec extends Specification {

    DatabaseManager mockDatabaseManager = Mock()
    
    @Subject
    BollingerBandService service = new BollingerBandService(mockDatabaseManager)

    def "should use INCREMENTAL mode by default"() {
        given: "a request with default settings"
        def request = new BollingerBandRequest()

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "should use incremental calculation"
        1 * mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        1 * mockDatabaseManager.getAllSymbols() >> ["AAPL"]
        1 * mockDatabaseManager.countOhlcvRecords("AAPL") >> 50
        1 * mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("AAPL", 20, 2.0, false) >> 10

        and: "response should reflect incremental mode"
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.INCREMENTAL
        response.processedSymbols == 1
        response.totalRecordsUpdated == 10
    }

    def "should use FULL_RECALCULATION mode when specified"() {
        given: "a request with FULL_RECALCULATION mode"
        def request = new BollingerBandRequest()
        request.calculationMode = BollingerBandRequest.CalculationMode.FULL_RECALCULATION

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "should clear existing data and perform full calculation"
        1 * mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        1 * mockDatabaseManager.getAllSymbols() >> ["AAPL"]
        1 * mockDatabaseManager.countOhlcvRecords("AAPL") >> 50
        1 * mockDatabaseManager.clearBollingerBandData("AAPL") >> 25
        1 * mockDatabaseManager.calculateAndUpdateBollingerBands("AAPL", 20, 2.0, false) >> 25

        and: "response should reflect full recalculation mode"
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.FULL_RECALCULATION
        response.processedSymbols == 1
        response.totalRecordsUpdated == 25
    }

    def "should use SKIP_EXISTING mode and skip symbols with existing data"() {
        given: "a request with SKIP_EXISTING mode"
        def request = new BollingerBandRequest()
        request.calculationMode = BollingerBandRequest.CalculationMode.SKIP_EXISTING

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "should skip AAPL and process MSFT"
        1 * mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT"]
        1 * mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT"]
        1 * mockDatabaseManager.countOhlcvRecords("AAPL") >> 50
        1 * mockDatabaseManager.countOhlcvRecords("MSFT") >> 50
        1 * mockDatabaseManager.hasExistingBollingerBandData("AAPL") >> true
        1 * mockDatabaseManager.hasExistingBollingerBandData("MSFT") >> false
        1 * mockDatabaseManager.calculateAndUpdateBollingerBands("MSFT", 20, 2.0, false) >> 15
        0 * mockDatabaseManager.calculateAndUpdateBollingerBands("AAPL", _, _, _)
        0 * mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("AAPL", _, _, _)

        and: "response should reflect skip existing mode"
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.SKIP_EXISTING
        response.processedSymbols == 1
        response.totalRecordsUpdated == 15
        response.skippedSymbols.contains("AAPL")
        !response.skippedSymbols.contains("MSFT")
    }

    def "should maintain backward compatibility with forceRecalculate=true"() {
        given: "a request with legacy forceRecalculate=true"
        def request = new BollingerBandRequest()
        request.forceRecalculate = true

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "should use FULL_RECALCULATION mode"
        1 * mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        1 * mockDatabaseManager.getAllSymbols() >> ["AAPL"]
        1 * mockDatabaseManager.countOhlcvRecords("AAPL") >> 50
        1 * mockDatabaseManager.clearBollingerBandData("AAPL") >> 25
        1 * mockDatabaseManager.calculateAndUpdateBollingerBands("AAPL", 20, 2.0, false) >> 25

        and: "response should reflect full recalculation mode"
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.FULL_RECALCULATION
    }

    def "should maintain backward compatibility with forceRecalculate=false"() {
        given: "a request with legacy forceRecalculate=false"
        def request = new BollingerBandRequest()
        request.forceRecalculate = false

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "should use SKIP_EXISTING mode and skip the symbol"
        1 * mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        1 * mockDatabaseManager.getAllSymbols() >> ["AAPL"]
        1 * mockDatabaseManager.countOhlcvRecords("AAPL") >> 50
        1 * mockDatabaseManager.hasExistingBollingerBandData("AAPL") >> true
        0 * mockDatabaseManager.calculateAndUpdateBollingerBands(_, _, _, _)
        0 * mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental(_, _, _, _)

        and: "response should reflect skip existing mode"
        response.parameters.calculationMode == BollingerBandRequest.CalculationMode.SKIP_EXISTING
        response.processedSymbols == 0
        response.skippedSymbols.contains("AAPL")
    }

    def "should handle dry run mode correctly for all calculation modes"() {
        given: "a dry run request with INCREMENTAL mode"
        def request = new BollingerBandRequest()
        request.dryRun = true
        request.calculationMode = BollingerBandRequest.CalculationMode.INCREMENTAL

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "should perform dry run calculation"
        1 * mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL"]
        1 * mockDatabaseManager.getAllSymbols() >> ["AAPL"]
        1 * mockDatabaseManager.countOhlcvRecords("AAPL") >> 50
        1 * mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("AAPL", 20, 2.0, true) >> 5

        and: "response should reflect dry run mode"
        response.dryRun == true
        response.totalRecordsUpdated == 5
    }
}
