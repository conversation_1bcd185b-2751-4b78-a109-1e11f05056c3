package com.investment.service

import com.investment.database.DatabaseManager
import com.investment.model.Instrument
import com.investment.model.InstrumentType
import com.investment.provider.DataProvider
import spock.lang.Specification
import spock.lang.Subject

import java.math.BigDecimal
import java.time.LocalDate

class OHLCVServiceSpec extends Specification {

    DatabaseManager mockDatabaseManager = Mock()
    DataProvider mockDataProvider = Mock()

    @Subject
    OHLCVService ohlcvService = new OHLCVService(mockDatabaseManager, mockDataProvider)

    def "should refresh all OHLCV data in dry run mode"() {
        given: "instruments in the database"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software"),
            new Instrument("GOOGL", "Alphabet Inc.", InstrumentType.US_STOCK, new BigDecimal("1800000000000"), "US", 2004, "Technology", "Internet Services")
        ]
        mockDatabaseManager.getTotalInstrumentCount() >> 3
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(0, 100) >> instruments
        mockDatabaseManager.hasRecentData(_) >> false

        when: "refreshing all OHLCV data in dry run mode"
        def response = ohlcvService.refreshAllOHLCVData(true, 100, false)

        then: "should return successful response without calling data provider"
        response.totalInstruments == 3
        response.processedSymbols == 3
        response.skippedSymbols == 0
        response.successfulUpdates == 3
        response.failedUpdates == 0
        response.totalDataPointsUpdated == 0
        response.processedSymbolsList == ["AAPL", "MSFT", "GOOGL"]
        response.skippedSymbolsList.isEmpty()
        response.failedSymbolsList.isEmpty()
        response.dryRun == true
        response.summary.contains("DRY RUN")

        and: "should not call data provider or database save methods"
        0 * mockDataProvider.downloadHistoricalData(_, _, _, _)
        0 * mockDatabaseManager.getLastDataDate(_)
    }

    def "should refresh all OHLCV data with actual updates"() {
        given: "instruments in the database"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software")
        ]
        mockDatabaseManager.getTotalInstrumentCount() >> 2
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(0, 100) >> instruments
        mockDatabaseManager.hasRecentData(_) >> false
        mockDatabaseManager.getLastDataDate("AAPL") >> LocalDate.now().minusDays(10)
        mockDatabaseManager.getLastDataDate("MSFT") >> LocalDate.now().minusDays(5)

        when: "refreshing all OHLCV data with actual updates"
        def response = ohlcvService.refreshAllOHLCVData(false, 100, false)

        then: "should call data provider for each symbol"
        2 * mockDataProvider.downloadHistoricalData(_, _, _, _)
        
        and: "should return successful response"
        response.totalInstruments == 2
        response.processedSymbols == 2
        response.skippedSymbols == 0
        response.successfulUpdates == 2
        response.failedUpdates == 0
        response.processedSymbolsList == ["AAPL", "MSFT"]
        response.dryRun == false
        response.summary.contains("REFRESH COMPLETED")
    }

    def "should skip symbols with recent data when skipExisting is true"() {
        given: "instruments in the database with some having recent data"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software"),
            new Instrument("GOOGL", "Alphabet Inc.", InstrumentType.US_STOCK, new BigDecimal("1800000000000"), "US", 2004, "Technology", "Internet Services")
        ]
        mockDatabaseManager.getTotalInstrumentCount() >> 3
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(0, 100) >> instruments
        mockDatabaseManager.hasRecentData("AAPL") >> false
        mockDatabaseManager.hasRecentData("MSFT") >> true  // Has recent data
        mockDatabaseManager.hasRecentData("GOOGL") >> false

        when: "refreshing with skipExisting enabled"
        def response = ohlcvService.refreshAllOHLCVData(true, 100, true)

        then: "should skip symbols with recent data"
        response.totalInstruments == 3
        response.processedSymbols == 2  // Only AAPL and GOOGL processed
        response.skippedSymbols == 1    // MSFT skipped
        response.successfulUpdates == 2
        response.failedUpdates == 0
        response.processedSymbolsList == ["AAPL", "GOOGL"]
        response.skippedSymbolsList == ["MSFT"]
        response.failedSymbolsList.isEmpty()
    }

    def "should respect maxSymbols limit"() {
        given: "more instruments than the limit"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software"),
            new Instrument("GOOGL", "Alphabet Inc.", InstrumentType.US_STOCK, new BigDecimal("1800000000000"), "US", 2004, "Technology", "Internet Services"),
            new Instrument("TSLA", "Tesla Inc.", InstrumentType.US_STOCK, new BigDecimal("800000000000"), "US", 2010, "Automotive", "Electric Vehicles")
        ]
        mockDatabaseManager.getTotalInstrumentCount() >> 4
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(0, 2) >> instruments.take(2)
        mockDatabaseManager.hasRecentData(_) >> false

        when: "refreshing with a limit of 2 symbols"
        def response = ohlcvService.refreshAllOHLCVData(true, 2, false)

        then: "should process only up to the limit"
        response.totalInstruments == 4
        response.processedSymbols == 2  // Limited to 2
        response.skippedSymbols == 0
        response.successfulUpdates == 2
        response.processedSymbolsList.size() == 2
        response.processedSymbolsList == ["AAPL", "MSFT"]  // First 2 by market cap
    }

    def "should handle individual symbol failures gracefully"() {
        given: "instruments in the database"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("INVALID", "Invalid Symbol", InstrumentType.US_STOCK, new BigDecimal("1000000000"), "US", 2000, "Technology", "Software"),
            new Instrument("GOOGL", "Alphabet Inc.", InstrumentType.US_STOCK, new BigDecimal("1800000000000"), "US", 2004, "Technology", "Internet Services")
        ]
        mockDatabaseManager.getTotalInstrumentCount() >> 3
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(0, 100) >> instruments
        mockDatabaseManager.hasRecentData(_) >> false
        mockDatabaseManager.getLastDataDate("AAPL") >> LocalDate.now().minusDays(10)
        mockDatabaseManager.getLastDataDate("INVALID") >> { throw new RuntimeException("Symbol not found") }
        mockDatabaseManager.getLastDataDate("GOOGL") >> LocalDate.now().minusDays(5)

        when: "refreshing all OHLCV data with one failing symbol"
        def response = ohlcvService.refreshAllOHLCVData(false, 100, false)

        then: "should handle failures gracefully and continue with other symbols"
        response.totalInstruments == 3
        response.processedSymbols == 3
        response.skippedSymbols == 0
        response.successfulUpdates == 2  // AAPL and GOOGL succeed
        response.failedUpdates == 1     // INVALID fails
        response.processedSymbolsList == ["AAPL", "INVALID", "GOOGL"]
        response.failedSymbolsList == ["INVALID"]
        response.skippedSymbolsList.isEmpty()
    }

    def "should handle empty instruments database"() {
        given: "no instruments in the database"
        mockDatabaseManager.getTotalInstrumentCount() >> 0
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(0, 100) >> []

        when: "refreshing all OHLCV data"
        def response = ohlcvService.refreshAllOHLCVData(true, 100, false)

        then: "should return response with zero counts"
        response.totalInstruments == 0
        response.processedSymbols == 0
        response.skippedSymbols == 0
        response.successfulUpdates == 0
        response.failedUpdates == 0
        response.totalDataPointsUpdated == 0
        response.processedSymbolsList.isEmpty()
        response.skippedSymbolsList.isEmpty()
        response.failedSymbolsList.isEmpty()
        response.dryRun == true
    }

    def "should process instruments in market cap order (highest first)"() {
        given: "instruments with different market caps"
        def instruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software"),
            new Instrument("SMALL", "Small Company", InstrumentType.US_STOCK, new BigDecimal("1000000000"), "US", 2010, "Technology", "Software"),
            new Instrument("GOOGL", "Alphabet Inc.", InstrumentType.US_STOCK, new BigDecimal("1800000000000"), "US", 2004, "Technology", "Internet Services")
        ]
        mockDatabaseManager.getTotalInstrumentCount() >> 4
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(0, 100) >> instruments
        mockDatabaseManager.hasRecentData(_) >> false

        when: "refreshing all OHLCV data"
        def response = ohlcvService.refreshAllOHLCVData(true, 100, false)

        then: "should process symbols in market cap order (highest first)"
        response.processedSymbolsList == ["AAPL", "MSFT", "SMALL", "GOOGL"]
        // Note: The order should match the order returned by getAllInstrumentsOrderedByMarketCap()
        // which is expected to be ordered by market cap descending
    }

    def "should handle database errors gracefully"() {
        given: "database error when getting instruments"
        mockDatabaseManager.getTotalInstrumentCount() >> { throw new RuntimeException("Database connection failed") }

        when: "refreshing all OHLCV data"
        ohlcvService.refreshAllOHLCVData(true, 100, false)

        then: "should throw runtime exception"
        thrown(RuntimeException)
    }

    def "should support pagination with startIndex and endIndex"() {
        given: "instruments in the database"
        def allInstruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software"),
            new Instrument("GOOGL", "Alphabet Inc.", InstrumentType.US_STOCK, new BigDecimal("1800000000000"), "US", 2004, "Technology", "Internet Services"),
            new Instrument("TSLA", "Tesla Inc.", InstrumentType.US_STOCK, new BigDecimal("800000000000"), "US", 2010, "Automotive", "Electric Vehicles")
        ]
        def paginatedInstruments = allInstruments[1..2] // MSFT and GOOGL (indices 1-2)

        mockDatabaseManager.getTotalInstrumentCount() >> 4
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(1, 2) >> paginatedInstruments
        mockDatabaseManager.hasRecentData(_) >> false

        when: "refreshing with pagination (startIndex=1, endIndex=3)"
        def response = ohlcvService.refreshAllOHLCVData(true, 100, false, 1, 3)

        then: "should process only the paginated instruments"
        response.totalInstruments == 4
        response.processedSymbols == 2
        response.processedSymbolsList == ["MSFT", "GOOGL"]
        response.startIndex == 1
        response.endIndex == 3
        response.summary.contains("(range: 1-2)")
    }

    def "should respect maxSymbols limit with pagination"() {
        given: "instruments in the database"
        def paginatedInstruments = [
            new Instrument("AAPL", "Apple Inc.", InstrumentType.US_STOCK, new BigDecimal("3000000000000"), "US", 1980, "Technology", "Consumer Electronics"),
            new Instrument("MSFT", "Microsoft Corporation", InstrumentType.US_STOCK, new BigDecimal("2500000000000"), "US", 1986, "Technology", "Software")
        ]

        mockDatabaseManager.getTotalInstrumentCount() >> 10
        mockDatabaseManager.getAllInstrumentsOrderedByMarketCap(5, 2) >> paginatedInstruments
        mockDatabaseManager.hasRecentData("AAPL") >> false
        mockDatabaseManager.hasRecentData("MSFT") >> false

        when: "refreshing with pagination and maxSymbols limit"
        def response = ohlcvService.refreshAllOHLCVData(true, 2, false, 5, 10)

        then: "should respect both pagination and maxSymbols"
        response.totalInstruments == 10
        response.processedSymbols == 2
        response.processedSymbolsList == ["AAPL", "MSFT"]
        response.startIndex == 5
        response.endIndex == 10 // effectiveEndIndex is the provided endIndex
    }
}
