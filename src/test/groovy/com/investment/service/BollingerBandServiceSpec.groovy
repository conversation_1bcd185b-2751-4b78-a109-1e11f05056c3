package com.investment.service

import com.investment.api.model.BollingerBandRequest
import com.investment.api.model.BollingerBandResponse
import com.investment.database.DatabaseManager
import spock.lang.Specification
import spock.lang.Subject

import java.sql.SQLException

class BollingerBandServiceSpec extends Specification {

    DatabaseManager mockDatabaseManager = Mock()
    
    @Subject
    BollingerBandService service = new BollingerBandService(mockDatabaseManager)

    def "should calculate Bollinger Bands for all symbols successfully"() {
        given: "a valid request"
        def request = new BollingerBandRequest(20, 2.0, false)
        request.maxSymbols = 0
        request.minDataPoints = 20
        request.forceRecalculate = false

        and: "mock database returns symbols and data"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT", "GOOGL"]
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT", "GOOGL", "NOOHLCV1", "NOOHLCV2"]
        mockDatabaseManager.countOhlcvRecords("AAPL") >> 100
        mockDatabaseManager.countOhlcvRecords("MSFT") >> 150
        mockDatabaseManager.countOhlcvRecords("GOOGL") >> 200
        mockDatabaseManager.hasExistingBollingerBandData("AAPL") >> false
        mockDatabaseManager.hasExistingBollingerBandData("MSFT") >> false
        mockDatabaseManager.hasExistingBollingerBandData("GOOGL") >> false
        mockDatabaseManager.calculateAndUpdateBollingerBands("AAPL", 20, 2.0, false) >> 100
        mockDatabaseManager.calculateAndUpdateBollingerBands("MSFT", 20, 2.0, false) >> 150
        mockDatabaseManager.calculateAndUpdateBollingerBands("GOOGL", 20, 2.0, false) >> 200

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "response indicates success"
        response.status == "success"
        response.processedSymbols == 3
        response.totalRecordsUpdated == 450
        response.symbolsWithInsufficientData.isEmpty()
        response.skippedSymbols.isEmpty()
        response.failedSymbols.isEmpty()
        response.errors.isEmpty()
        !response.dryRun
        response.parameters.period == 20
        response.parameters.stdDevMultiplier == 2.0
        response.parameters.minDataPoints == 20
    }

    def "should handle symbols with insufficient data"() {
        given: "a request with minimum data points requirement"
        def request = new BollingerBandRequest(20, 2.0, false)
        request.minDataPoints = 50

        and: "mock database returns symbols with varying data amounts"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "NEWCO", "MSFT"]
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "NEWCO", "MSFT"]
        mockDatabaseManager.countOhlcvRecords("AAPL") >> 100
        mockDatabaseManager.countOhlcvRecords("NEWCO") >> 10  // insufficient data
        mockDatabaseManager.countOhlcvRecords("MSFT") >> 75
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("AAPL", 20, 2.0, false) >> 100
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("MSFT", 20, 2.0, false) >> 75

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "response shows insufficient data symbol"
        response.status == "success"
        response.processedSymbols == 2
        response.totalRecordsUpdated == 175
        response.symbolsWithInsufficientData == ["NEWCO"]
        response.skippedSymbols.isEmpty()
        response.failedSymbols.isEmpty()
    }

    def "should skip symbols with existing data when forceRecalculate is false"() {
        given: "a request with forceRecalculate disabled"
        def request = new BollingerBandRequest(20, 2.0, false)
        request.forceRecalculate = false

        and: "mock database returns symbols, some with existing data"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT"]
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT"]
        mockDatabaseManager.countOhlcvRecords("AAPL") >> 100
        mockDatabaseManager.countOhlcvRecords("MSFT") >> 150
        mockDatabaseManager.hasExistingBollingerBandData("AAPL") >> true  // already has data
        mockDatabaseManager.hasExistingBollingerBandData("MSFT") >> false
        mockDatabaseManager.calculateAndUpdateBollingerBands("MSFT", 20, 2.0, false) >> 150

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "response shows skipped symbol"
        response.status == "success"
        response.processedSymbols == 1
        response.totalRecordsUpdated == 150
        response.symbolsWithInsufficientData.isEmpty()
        response.skippedSymbols == ["AAPL"]
        response.failedSymbols.isEmpty()
    }

    def "should handle calculation failures gracefully"() {
        given: "a valid request"
        def request = new BollingerBandRequest(20, 2.0, false)

        and: "mock database returns symbols but one fails"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "FAIL", "MSFT"]
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "FAIL", "MSFT"]
        mockDatabaseManager.countOhlcvRecords("AAPL") >> 100
        mockDatabaseManager.countOhlcvRecords("FAIL") >> 100
        mockDatabaseManager.countOhlcvRecords("MSFT") >> 150
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("AAPL", 20, 2.0, false) >> 100
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("FAIL", 20, 2.0, false) >> { throw new SQLException("Database error") }
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("MSFT", 20, 2.0, false) >> 150

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "response shows partial success with failed symbol"
        response.status == "partial_success"
        response.processedSymbols == 2
        response.totalRecordsUpdated == 250
        response.symbolsWithInsufficientData.isEmpty()
        response.skippedSymbols.isEmpty()
        response.failedSymbols == ["FAIL"]
        response.errors.size() == 1
        response.errors[0].contains("FAIL")
    }

    def "should respect maxSymbols limit"() {
        given: "a request with maxSymbols limit"
        def request = new BollingerBandRequest(20, 2.0, false)
        request.maxSymbols = 2

        and: "mock database returns more symbols than limit"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT", "GOOGL", "AMZN"]
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT", "GOOGL", "AMZN"]
        mockDatabaseManager.countOhlcvRecords("AAPL") >> 100
        mockDatabaseManager.countOhlcvRecords("MSFT") >> 150
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("AAPL", 20, 2.0, false) >> 100
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("MSFT", 20, 2.0, false) >> 150

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "only processes limited number of symbols"
        response.status == "success"
        response.processedSymbols == 2
        response.totalRecordsUpdated == 250
    }

    def "should handle dry run mode correctly"() {
        given: "a dry run request"
        def request = new BollingerBandRequest(20, 2.0, true)

        and: "mock database returns symbols"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT"]
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT"]
        mockDatabaseManager.countOhlcvRecords("AAPL") >> 100
        mockDatabaseManager.countOhlcvRecords("MSFT") >> 150
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("AAPL", 20, 2.0, true) >> 100
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("MSFT", 20, 2.0, true) >> 150

        when: "calculating Bollinger Bands in dry run"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "response indicates dry run"
        response.status == "success"
        response.processedSymbols == 2
        response.totalRecordsUpdated == 250
        response.dryRun == true
        response.summary.contains("would be")
    }

    def "should handle fatal errors during processing"() {
        given: "a valid request"
        def request = new BollingerBandRequest(20, 2.0, false)

        and: "mock database throws exception on getSymbolsWithOhlcvData"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> { throw new RuntimeException("Database connection failed") }

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "response indicates failure"
        response.status == "failed"
        response.processedSymbols == 0
        response.totalRecordsUpdated == 0
        response.errors.size() == 1
        response.errors[0].contains("Fatal error")
    }

    def "should optimize by processing only symbols with OHLCV data"() {
        given: "a valid request"
        def request = new BollingerBandRequest(20, 2.0, false)

        and: "mock database returns fewer symbols with OHLCV data than total instruments"
        mockDatabaseManager.getSymbolsWithOhlcvData() >> ["AAPL", "MSFT"]  // only 2 symbols have OHLCV data
        mockDatabaseManager.getAllSymbols() >> ["AAPL", "MSFT", "NOOHLCV1", "NOOHLCV2", "NOOHLCV3"]  // 5 total instruments
        mockDatabaseManager.countOhlcvRecords("AAPL") >> 100
        mockDatabaseManager.countOhlcvRecords("MSFT") >> 150
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("AAPL", 20, 2.0, false) >> 100
        mockDatabaseManager.calculateAndUpdateBollingerBandsIncremental("MSFT", 20, 2.0, false) >> 150

        when: "calculating Bollinger Bands"
        BollingerBandResponse response = service.calculateBollingerBands(request)

        then: "should only process symbols with OHLCV data"
        response.status == "success"
        response.processedSymbols == 2
        response.totalRecordsUpdated == 250

        and: "should not attempt to process symbols without OHLCV data"
        0 * mockDatabaseManager.countOhlcvRecords("NOOHLCV1")
        0 * mockDatabaseManager.countOhlcvRecords("NOOHLCV2")
        0 * mockDatabaseManager.countOhlcvRecords("NOOHLCV3")
        0 * mockDatabaseManager.hasExistingBollingerBandData("NOOHLCV1")
        0 * mockDatabaseManager.hasExistingBollingerBandData("NOOHLCV2")
        0 * mockDatabaseManager.hasExistingBollingerBandData("NOOHLCV3")
    }
}
