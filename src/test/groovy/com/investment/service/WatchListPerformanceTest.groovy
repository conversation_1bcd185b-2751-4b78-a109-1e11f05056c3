package com.investment.service

import com.investment.api.model.RecalculatePerformanceResponse
import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.model.WatchListItem
import spock.lang.Specification
import spock.lang.Subject

import java.math.BigDecimal
import java.time.LocalDate

/**
 * Test for WatchList performance calculation functionality.
 */
class WatchListPerformanceTest extends Specification {

    @Subject
    WatchListService watchListService

    DatabaseManager databaseManager = Mock()

    def setup() {
        watchListService = new WatchListService(databaseManager)
    }

    def "should calculate performance metrics for watch list items"() {
        given: "watch list items exist"
        def currentDate = LocalDate.now()
        def currentOhlcvData = [
                new OHLCV("AAPL", currentDate.minusDays(1), 150.0, 155.0, 149.0, 154.0, 1000000L)
        ]

        and: "historical OHLCV data exists for performance calculation"
        def oneMonthAgo = currentDate.minusMonths(1)
        def historicalOhlcvData = [
                new OHLCV("AAPL", oneMonthAgo, 140.0, 145.0, 139.0, 144.0, 1200000L)
        ]

        when: "performance recalculation is triggered"
        def response = watchListService.calculateAndUpdateAllPerformance()

        then: "database interactions occur correctly"
        1 * databaseManager.getWatchListItems() >> [
                [id: 1L, display_index: 1, symbol: "AAPL", start_date: java.sql.Date.valueOf(LocalDate.of(2024, 1, 1)),
                 remarks: "Test", one_mo_perf: null, three_mo_perf: null, six_mo_perf: null,
                 created_date: new java.sql.Timestamp(System.currentTimeMillis()),
                 updated_date: new java.sql.Timestamp(System.currentTimeMillis())]
        ]

        // Mock OHLCV data calls - current price lookup
        1 * databaseManager.getOHLCVData("AAPL", _, _) >> currentOhlcvData

        // Mock OHLCV data calls - historical price lookups (1M, 3M, 6M)
        3 * databaseManager.getOHLCVData("AAPL", _, _) >> historicalOhlcvData

        // Mock performance update call
        1 * databaseManager.updateWatchListPerformance(1L, _, _, _)

        and: "response contains expected results"
        response != null
        response instanceof RecalculatePerformanceResponse
        response.totalItems == 1
        response.successfulUpdates == 1
        response.processingTimeMs > 0
    }

    def "should handle symbols with insufficient data gracefully"() {
        given: "a watch list item exists"
        when: "performance recalculation is triggered"
        def response = watchListService.calculateAndUpdateAllPerformance()

        then: "database interactions occur"
        1 * databaseManager.getWatchListItems() >> [
                [id: 1L, display_index: 1, symbol: "NODATA", start_date: java.sql.Date.valueOf(LocalDate.of(2024, 1, 1)),
                 remarks: "Test", one_mo_perf: null, three_mo_perf: null, six_mo_perf: null,
                 created_date: new java.sql.Timestamp(System.currentTimeMillis()),
                 updated_date: new java.sql.Timestamp(System.currentTimeMillis())]
        ]

        // Mock no OHLCV data available
        1 * databaseManager.getOHLCVData("NODATA", _, _) >> []

        // No performance update should occur
        0 * databaseManager.updateWatchListPerformance(_, _, _, _)

        and: "response indicates skipped item"
        response != null
        response.totalItems == 1
        response.skippedItems == 1
        response.successfulUpdates == 0
    }

    def "should handle database errors gracefully"() {
        given: "a watch list item exists"
        when: "performance recalculation is triggered with database error"
        def response = watchListService.calculateAndUpdateAllPerformance()

        then: "database error is handled"
        1 * databaseManager.getWatchListItems() >> [
                [id: 1L, display_index: 1, symbol: "ERROR", start_date: java.sql.Date.valueOf(LocalDate.of(2024, 1, 1)),
                 remarks: "Test", one_mo_perf: null, three_mo_perf: null, six_mo_perf: null,
                 created_date: new java.sql.Timestamp(System.currentTimeMillis()),
                 updated_date: new java.sql.Timestamp(System.currentTimeMillis())]
        ]

        1 * databaseManager.getOHLCVData("ERROR", _, _) >> { throw new RuntimeException("Database error") }

        0 * databaseManager.updateWatchListPerformance(_, _, _, _)

        and: "response indicates failed item"
        response != null
        response.totalItems == 1
        response.failedUpdates == 1
        response.successfulUpdates == 0
    }

    private WatchListItem createWatchListItem(Long id, String symbol, LocalDate startDate) {
        def item = new WatchListItem(1, symbol, startDate, "Test item")
        item.setId(id)
        return item
    }
}
