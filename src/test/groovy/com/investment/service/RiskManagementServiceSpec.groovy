package com.investment.service

import com.investment.database.DatabaseManager
import com.investment.model.OHLCV
import com.investment.model.Position
import spock.lang.Specification
import spock.lang.Subject

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test specification for RiskManagementService enhanced risk management logic.
 */
class RiskManagementServiceSpec extends Specification {

    @Subject
    RiskManagementService riskManagementService

    DatabaseManager mockDatabaseManager = Mock()

    def setup() {
        riskManagementService = new RiskManagementService()
        riskManagementService.databaseManager = mockDatabaseManager
    }

    def "should use aggressive mode for positions in initial period (days 1-2)"() {
        given: "a position created 1 day ago"
        def position = createTestPosition()
        position.createdDate = LocalDateTime.now().minusDays(1)
        position.aggressiveStopPercent = new BigDecimal("0.05")  // 5%
        position.conservativeStopPercent = new BigDecimal("0.03") // 3%
        position.highestAfterTrade = new BigDecimal("100.00")

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should use aggressive mode and calculate stop value accordingly"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05)
    }

    def "should use conservative mode when Bollinger Band distance is contracting"() {
        given: "a position created 5 days ago"
        def position = createTestPosition()
        position.createdDate = LocalDateTime.now().minusDays(5)
        position.aggressiveStopPercent = new BigDecimal("0.05")  // 5%
        position.conservativeStopPercent = new BigDecimal("0.03") // 3%
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "OHLCV data showing contracting Bollinger Band distance"
        def ohlcvData = [
            // T-1: distance = |92 - 90| = 2 (contracting - closer to BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(1), 91.0, 93.0, 90.0, 92.0, 1000000L, 90.0, null, null, null),
            // T-2: distance = |95 - 90| = 5 (was further from BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(2), 94.0, 96.0, 93.0, 95.0, 1000000L, 90.0, null, null, null),
            // T-3: for minimum data requirement
            new OHLCV("AAPL", LocalDate.now().minusDays(3), 88.0, 90.0, 87.0, 89.0, 1000000L, 85.0, null, null, null)
        ]

        mockDatabaseManager.getOHLCVData("AAPL", _, _) >> ohlcvData

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should switch to conservative mode for 2 days"
        position.riskMode == Position.RiskMode.CONSERVATIVE
        position.conservativePeriodEndDate != null
        result == new BigDecimal("97.000000") // 100 * (1 - 0.03)
    }

    def "should continue aggressive mode when Bollinger Band distance is expanding"() {
        given: "a position created 5 days ago"
        def position = createTestPosition()
        position.createdDate = LocalDateTime.now().minusDays(5)
        position.aggressiveStopPercent = new BigDecimal("0.05")  // 5%
        position.conservativeStopPercent = new BigDecimal("0.03") // 3%
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "OHLCV data showing expanding Bollinger Band distance"
        def ohlcvData = [
            // T-1: distance = |98 - 90| = 8 (further from BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(1), 97.0, 99.0, 96.0, 98.0, 1000000L, 90.0, null, null, null),
            // T-2: distance = |95 - 90| = 5 (closer to BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(2), 94.0, 96.0, 93.0, 95.0, 1000000L, 90.0, null, null, null),
            // T-3: for minimum data requirement
            new OHLCV("AAPL", LocalDate.now().minusDays(3), 88.0, 90.0, 87.0, 89.0, 1000000L, 85.0, null, null, null)
        ]

        mockDatabaseManager.getOHLCVData("AAPL", _, _) >> ohlcvData

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should continue with aggressive mode"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05)
    }

    def "should handle SELL positions with inverted logic"() {
        given: "a SELL position created 5 days ago"
        def position = createTestPosition()
        position.side = Position.Side.SELL
        position.createdDate = LocalDateTime.now().minusDays(5)
        position.aggressiveStopPercent = new BigDecimal("0.05")  // 5%
        position.conservativeStopPercent = new BigDecimal("0.03") // 3%
        position.highestAfterTrade = new BigDecimal("100.00") // Lowest price for SELL positions

        and: "OHLCV data showing expanding distance (for SELL, use body top)"
        def ohlcvData = [
            // T-1: distance = |110 - 95| = 15 (body top further from BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(1), 108.0, 112.0, 107.0, 110.0, 1000000L, 95.0, null, null, null),
            // T-2: distance = |105 - 95| = 10 (body top closer to BB middle)
            new OHLCV("AAPL", LocalDate.now().minusDays(2), 103.0, 107.0, 102.0, 105.0, 1000000L, 95.0, null, null, null),
            // T-3: for minimum data requirement
            new OHLCV("AAPL", LocalDate.now().minusDays(3), 100.0, 102.0, 99.0, 101.0, 1000000L, 98.0, null, null, null)
        ]

        mockDatabaseManager.getOHLCVData("AAPL", _, _) >> ohlcvData

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should use aggressive mode and calculate stop value for SELL position"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("105.000000") // 100 * (1 + 0.05) for SELL positions
    }

    def "should remain in conservative mode during forced conservative period"() {
        given: "a position currently in conservative period"
        def position = createTestPosition()
        position.createdDate = LocalDateTime.now().minusDays(5)
        position.riskMode = Position.RiskMode.CONSERVATIVE
        position.conservativePeriodEndDate = LocalDateTime.now().plusDays(1) // Still in conservative period
        position.aggressiveStopPercent = new BigDecimal("0.05")
        position.conservativeStopPercent = new BigDecimal("0.03")
        position.highestAfterTrade = new BigDecimal("100.00")

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should remain in conservative mode regardless of market conditions"
        position.riskMode == Position.RiskMode.CONSERVATIVE
        result == new BigDecimal("97.000000") // 100 * (1 - 0.03)
    }

    def "should fallback to standard calculation when insufficient OHLCV data"() {
        given: "a position with insufficient historical data"
        def position = createTestPosition()
        position.createdDate = LocalDateTime.now().minusDays(5)
        position.aggressiveStopPercent = new BigDecimal("0.05")
        position.conservativeStopPercent = new BigDecimal("0.03")
        position.highestAfterTrade = new BigDecimal("100.00")

        and: "insufficient OHLCV data"
        mockDatabaseManager.getOHLCVData("AAPL", _, _) >> []

        when: "calculating enhanced effective stop value"
        def result = riskManagementService.calculateEnhancedEffectiveStopValue(position)

        then: "should default to aggressive mode"
        position.riskMode == Position.RiskMode.AGGRESSIVE
        result == new BigDecimal("95.000000") // 100 * (1 - 0.05)
    }

    def "should initialize risk parameters correctly"() {
        given: "a new position"
        def position = createTestPosition()
        def aggressivePercent = new BigDecimal("0.05")
        def conservativePercent = new BigDecimal("0.03")

        when: "initializing risk parameters"
        riskManagementService.initializeRiskParameters(position, aggressivePercent, conservativePercent)

        then: "should set all parameters correctly"
        position.aggressiveStopPercent == aggressivePercent
        position.conservativeStopPercent == conservativePercent
        position.riskMode == Position.RiskMode.AGGRESSIVE
    }

    private Position createTestPosition() {
        def position = new Position()
        position.id = 1L
        position.symbol = "AAPL"
        position.side = Position.Side.BUY
        position.status = Position.Status.OPEN
        position.tradePrice = new BigDecimal("95.00")
        position.position = new BigDecimal("100")
        position.createdDate = LocalDateTime.now()
        return position
    }
}
