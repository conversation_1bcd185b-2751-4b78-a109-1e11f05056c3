package com.investment.model

import spock.lang.Specification

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test specification for WatchListItem domain model.
 */
class WatchListItemSpec extends Specification {

    def "should create watch list item with required fields"() {
        given: "required fields for watch list item"
        def displayIndex = 1
        def symbol = "AAPL"
        def startDate = LocalDate.of(2024, 1, 15)

        when: "creating watch list item"
        def item = new WatchListItem(displayIndex, symbol, startDate)

        then: "item should be created with correct data"
        item.displayIndex == 1
        item.symbol == "AAPL"
        item.startDate == LocalDate.of(2024, 1, 15)
        item.remarks == null
        item.oneMonthPerf == null
        item.threeMonthPerf == null
        item.sixMonthPerf == null
        item.createdDate != null
        item.updatedDate != null
        item.id == null
    }

    def "should create watch list item with remarks"() {
        given: "watch list item data with remarks"
        def displayIndex = 2
        def symbol = "MSFT"
        def startDate = LocalDate.of(2024, 1, 10)
        def remarks = "Strong growth potential"

        when: "creating watch list item"
        def item = new WatchListItem(displayIndex, symbol, startDate, remarks)

        then: "item should be created with remarks"
        item.displayIndex == 2
        item.symbol == "MSFT"
        item.startDate == LocalDate.of(2024, 1, 10)
        item.remarks == "Strong growth potential"
    }

    def "should convert symbol to uppercase"() {
        when: "creating item with lowercase symbol"
        def item = new WatchListItem(1, "aapl", LocalDate.now())

        then: "symbol should be converted to uppercase"
        item.symbol == "AAPL"
    }

    def "should update performance metrics"() {
        given: "a watch list item"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())
        def originalUpdatedDate = item.updatedDate

        when: "updating performance metrics"
        Thread.sleep(1) // Ensure time difference
        item.updatePerformance(
                new BigDecimal("0.0523"),
                new BigDecimal("0.1245"),
                new BigDecimal("0.2187")
        )

        then: "performance metrics should be updated"
        item.oneMonthPerf == new BigDecimal("0.0523")
        item.threeMonthPerf == new BigDecimal("0.1245")
        item.sixMonthPerf == new BigDecimal("0.2187")
        item.updatedDate.isAfter(originalUpdatedDate)
    }

    def "should update display index"() {
        given: "a watch list item"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())
        def originalUpdatedDate = item.updatedDate

        when: "updating display index"
        Thread.sleep(1) // Ensure time difference
        item.updateDisplayIndex(5)

        then: "display index should be updated"
        item.displayIndex == 5
        item.updatedDate.isAfter(originalUpdatedDate)
    }

    def "should update remarks"() {
        given: "a watch list item"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())
        def originalUpdatedDate = item.updatedDate

        when: "updating remarks"
        Thread.sleep(1) // Ensure time difference
        item.updateRemarks("Updated analysis")

        then: "remarks should be updated"
        item.remarks == "Updated analysis"
        item.updatedDate.isAfter(originalUpdatedDate)
    }

    def "should check if has performance data"() {
        given: "a watch list item"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())

        when: "item has no performance data"
        then: "should return false"
        !item.hasPerformanceData()

        when: "item has one month performance"
        item.setOneMonthPerf(new BigDecimal("0.05"))

        then: "should return true"
        item.hasPerformanceData()
    }

    def "should get best performance"() {
        given: "a watch list item with performance data"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())
        item.setOneMonthPerf(new BigDecimal("0.05"))
        item.setThreeMonthPerf(new BigDecimal("0.12"))
        item.setSixMonthPerf(new BigDecimal("0.08"))

        when: "getting best performance"
        def best = item.getBestPerformance()

        then: "should return highest value"
        best == new BigDecimal("0.12")
    }

    def "should get worst performance"() {
        given: "a watch list item with performance data"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())
        item.setOneMonthPerf(new BigDecimal("0.05"))
        item.setThreeMonthPerf(new BigDecimal("-0.02"))
        item.setSixMonthPerf(new BigDecimal("0.08"))

        when: "getting worst performance"
        def worst = item.getWorstPerformance()

        then: "should return lowest value"
        worst == new BigDecimal("-0.02")
    }

    def "should check if performing positively"() {
        given: "a watch list item"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())

        when: "item has positive performance"
        item.setOneMonthPerf(new BigDecimal("0.05"))
        item.setThreeMonthPerf(new BigDecimal("0.12"))

        then: "should return true"
        item.isPerformingPositively()

        when: "item has only negative performance"
        item.setOneMonthPerf(new BigDecimal("-0.05"))
        item.setThreeMonthPerf(new BigDecimal("-0.02"))
        item.setSixMonthPerf(new BigDecimal("-0.01"))

        then: "should return false"
        !item.isPerformingPositively()
    }

    def "should validate required fields"() {
        given: "a watch list item"
        def item = new WatchListItem()

        when: "validating with null symbol"
        item.setDisplayIndex(1)
        item.setStartDate(LocalDate.now())
        item.validate()

        then: "should throw exception"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Symbol cannot be null or empty")
    }

    def "should validate display index"() {
        given: "a watch list item"
        def item = new WatchListItem()

        when: "validating with negative display index"
        item.setSymbol("AAPL")
        item.setDisplayIndex(-1)
        item.setStartDate(LocalDate.now())
        item.validate()

        then: "should throw exception"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Display index must be a non-negative integer")
    }

    def "should validate start date"() {
        given: "a watch list item"
        def item = new WatchListItem()

        when: "validating with future start date"
        item.setSymbol("AAPL")
        item.setDisplayIndex(1)
        item.setStartDate(LocalDate.now().plusDays(1))
        item.validate()

        then: "should throw exception"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Start date cannot be in the future")
    }

    def "should validate remarks length"() {
        given: "a watch list item"
        def item = new WatchListItem()

        when: "validating with long remarks"
        item.setSymbol("AAPL")
        item.setDisplayIndex(1)
        item.setStartDate(LocalDate.now())
        item.setRemarks("A".repeat(129)) // 129 characters
        item.validate()

        then: "should throw exception"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Remarks cannot exceed 128 characters")
    }

    def "should pass validation with valid data"() {
        given: "a watch list item with valid data"
        def item = new WatchListItem(1, "AAPL", LocalDate.now(), "Valid remarks")

        when: "validating"
        item.validate()

        then: "should not throw exception"
        notThrown(Exception)
    }

    def "should handle null values in performance calculations"() {
        given: "a watch list item with no performance data"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())

        when: "getting best and worst performance"
        def best = item.getBestPerformance()
        def worst = item.getWorstPerformance()

        then: "should return null"
        best == null
        worst == null
        !item.isPerformingPositively()
    }

    def "should handle partial performance data"() {
        given: "a watch list item with partial performance data"
        def item = new WatchListItem(1, "AAPL", LocalDate.now())
        item.setThreeMonthPerf(new BigDecimal("0.15"))

        when: "getting best and worst performance"
        def best = item.getBestPerformance()
        def worst = item.getWorstPerformance()

        then: "should return the available value"
        best == new BigDecimal("0.15")
        worst == new BigDecimal("0.15")
        item.isPerformingPositively()
    }

    def "should implement equals and hashCode correctly"() {
        given: "two watch list items with same ID and symbol"
        def item1 = new WatchListItem(1, "AAPL", LocalDate.now())
        item1.setId(1L)
        
        def item2 = new WatchListItem(2, "AAPL", LocalDate.now())
        item2.setId(1L)

        when: "comparing items"
        then: "should be equal"
        item1.equals(item2)
        item1.hashCode() == item2.hashCode()
    }

    def "should have meaningful toString"() {
        given: "a watch list item"
        def item = new WatchListItem(1, "AAPL", LocalDate.of(2024, 1, 15))
        item.setId(1L)

        when: "converting to string"
        def str = item.toString()

        then: "should contain key information"
        str.contains("id=1")
        str.contains("symbol='AAPL'")
        str.contains("displayIndex=1")
        str.contains("startDate=2024-01-15")
    }
}
