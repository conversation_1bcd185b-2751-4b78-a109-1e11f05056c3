package com.investment.process

import spock.lang.Specification

/**
 * Test specification for ProcessManager.
 */
class ProcessManagerSpec extends Specification {

    ProcessManager processManager

    def setup() {
        processManager = new ProcessManager()
    }

    def "should register a new process and return context"() {
        given: "process parameters"
        def processType = ProcessType.DMI_CALCULATION
        def description = "Test DMI calculation"
        def initiatedBy = "test-user"

        when: "registering a process"
        def context = processManager.registerProcess(processType, description, initiatedBy)

        then: "context should be created with correct information"
        context != null
        context.processInfo.processType == processType
        context.processInfo.description == description
        context.processInfo.initiatedBy == initiatedBy
        context.processInfo.status == ProcessStatus.RUNNING
        context.processInfo.processId != null
        !context.processInfo.processId.isEmpty()
    }

    def "should generate unique process IDs"() {
        given: "multiple processes"
        def processType = ProcessType.DMI_CALCULATION
        def description = "Test process"
        def initiatedBy = "test-user"

        when: "registering multiple processes"
        def context1 = processManager.registerProcess(processType, description, initiatedBy)
        def context2 = processManager.registerProcess(processType, description, initiatedBy)
        def context3 = processManager.registerProcess(processType, description, initiatedBy)

        then: "all process IDs should be unique"
        context1.processInfo.processId != context2.processInfo.processId
        context2.processInfo.processId != context3.processInfo.processId
        context1.processInfo.processId != context3.processInfo.processId
    }

    def "should retrieve active processes"() {
        given: "multiple processes with different statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "Process 3", "user3")

        and: "one process is completed"
        context2.markCompleted()

        when: "getting active processes"
        def activeProcesses = processManager.getActiveProcesses()

        then: "only running processes should be returned"
        activeProcesses.size() == 2
        activeProcesses.any { it.processId == context1.processInfo.processId }
        activeProcesses.any { it.processId == context3.processInfo.processId }
        !activeProcesses.any { it.processId == context2.processInfo.processId }
    }

    def "should abort a specific process"() {
        given: "a running process"
        def context = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Test process", "user")
        def processId = context.processInfo.processId

        when: "aborting the process"
        def result = processManager.abortProcess(processId)

        then: "process should be marked for abortion"
        result == true
        context.isCancellationRequested()
        context.processInfo.status == ProcessStatus.ABORTING
    }

    def "should abort all active processes"() {
        given: "multiple active processes"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "Process 3", "user3")

        and: "one process is already completed"
        context3.markCompleted()

        when: "aborting all processes"
        def result = processManager.abortAllProcesses()

        then: "all active processes should be aborted"
        result.totalActiveProcesses == 2
        result.abortRequestsSent == 2
        result.abortedProcessIds.size() == 2
        result.failedToAbortIds.isEmpty()
        result.fullSuccess

        and: "processes should be marked for cancellation"
        context1.isCancellationRequested()
        context2.isCancellationRequested()
        !context3.isCancellationRequested() // Already completed
    }

    def "should get processes by type"() {
        given: "processes of different types"
        def dmiContext = processManager.registerProcess(ProcessType.DMI_CALCULATION, "DMI process", "user1")
        def ohlcvContext = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "OHLCV process", "user2")
        def secContext = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "SEC process", "user3")

        when: "getting processes by type"
        def dmiProcesses = processManager.getProcessesByType(ProcessType.DMI_CALCULATION)
        def ohlcvProcesses = processManager.getProcessesByType(ProcessType.OHLCV_REFRESH)

        then: "correct processes should be returned"
        dmiProcesses.size() == 1
        dmiProcesses[0].processId == dmiContext.processInfo.processId

        ohlcvProcesses.size() == 1
        ohlcvProcesses[0].processId == ohlcvContext.processInfo.processId
    }

    def "should get processes by status"() {
        given: "processes with different statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "Process 3", "user3")

        and: "different statuses"
        context2.markCompleted()
        context3.markFailed("Test error")

        when: "getting processes by status"
        def runningProcesses = processManager.getProcessesByStatus(ProcessStatus.RUNNING)
        def completedProcesses = processManager.getProcessesByStatus(ProcessStatus.COMPLETED)
        def failedProcesses = processManager.getProcessesByStatus(ProcessStatus.FAILED)

        then: "correct processes should be returned"
        runningProcesses.size() == 1
        runningProcesses[0].processId == context1.processInfo.processId

        completedProcesses.size() == 1
        completedProcesses[0].processId == context2.processInfo.processId

        failedProcesses.size() == 1
        failedProcesses[0].processId == context3.processInfo.processId
    }

    def "should cleanup completed processes"() {
        given: "processes with different statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "Process 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "Process 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "Process 3", "user3")

        and: "some processes are completed"
        context2.markCompleted()
        context3.markFailed("Test error")

        when: "cleaning up completed processes"
        def cleanedUp = processManager.cleanupCompletedProcesses()

        then: "completed processes should be removed"
        cleanedUp == 2
        processManager.getAllProcesses().size() == 1
        processManager.getAllProcesses()[0].processId == context1.processInfo.processId
    }

    def "should generate process statistics"() {
        given: "processes with different types and statuses"
        def context1 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "DMI 1", "user1")
        def context2 = processManager.registerProcess(ProcessType.DMI_CALCULATION, "DMI 2", "user2")
        def context3 = processManager.registerProcess(ProcessType.OHLCV_REFRESH, "OHLCV 1", "user3")
        def context4 = processManager.registerProcess(ProcessType.SEC_SYNCHRONIZATION, "SEC 1", "user4")

        and: "different statuses"
        context2.markCompleted()
        context4.markFailed("Test error")

        when: "getting statistics"
        def stats = processManager.getStatistics()

        then: "statistics should be correct"
        stats.totalProcesses == 4
        stats.activeProcesses == 2
        stats.statusCounts[ProcessStatus.RUNNING] == 2
        stats.statusCounts[ProcessStatus.COMPLETED] == 1
        stats.statusCounts[ProcessStatus.FAILED] == 1
        stats.typeCounts[ProcessType.DMI_CALCULATION] == 2
        stats.typeCounts[ProcessType.OHLCV_REFRESH] == 1
        stats.typeCounts[ProcessType.SEC_SYNCHRONIZATION] == 1
    }
}
