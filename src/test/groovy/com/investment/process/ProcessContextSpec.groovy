package com.investment.process

import spock.lang.Specification

/**
 * Test specification for ProcessContext.
 */
class ProcessContextSpec extends Specification {

    ProcessInfo processInfo
    ProcessContext processContext

    def setup() {
        processInfo = new ProcessInfo("test-process-001", ProcessType.DMI_CALCULATION, "Test process", "test-user")
        processContext = new ProcessContext(processInfo)
    }

    def "should initialize with running status"() {
        expect: "initial state should be correct"
        !processContext.isCancellationRequested()
        processInfo.status == ProcessStatus.RUNNING
        processInfo.progressPercentage == 0
        processInfo.currentOperation == "Starting..."
    }

    def "should request cancellation"() {
        when: "requesting cancellation"
        processContext.requestCancellation()

        then: "cancellation should be requested"
        processContext.isCancellationRequested()
        processInfo.status == ProcessStatus.ABORTING
        processInfo.currentOperation == "Cancellation requested - cleaning up..."
    }

    def "should throw exception when cancellation is requested"() {
        given: "cancellation is requested"
        processContext.requestCancellation()

        when: "checking for cancellation"
        processContext.throwIfCancellationRequested()

        then: "exception should be thrown"
        thrown(ProcessContext.ProcessCancelledException)
    }

    def "should not throw exception when cancellation is not requested"() {
        when: "checking for cancellation without requesting it"
        processContext.throwIfCancellationRequested()

        then: "no exception should be thrown"
        notThrown(ProcessContext.ProcessCancelledException)
    }

    def "should update current operation"() {
        given: "a new operation description"
        def operation = "Processing symbol AAPL"

        when: "updating current operation"
        processContext.updateCurrentOperation(operation)

        then: "operation should be updated"
        processInfo.currentOperation == operation
    }

    def "should update progress with counts"() {
        given: "progress values"
        def processed = 75
        def total = 100

        when: "updating progress"
        processContext.updateProgress(processed, total)

        then: "progress should be updated"
        processInfo.processedItems == processed
        processInfo.totalItems == total
        processInfo.progressPercentage == 75
    }

    def "should update progress percentage directly"() {
        given: "a progress percentage"
        def percentage = 42

        when: "updating progress percentage"
        processContext.updateProgressPercentage(percentage)

        then: "percentage should be updated"
        processInfo.progressPercentage == percentage
    }

    def "should update progress with custom message"() {
        given: "progress values and message"
        def processed = 50
        def total = 200
        def operation = "Processing batch 2"

        when: "updating progress with message"
        processContext.updateProgress(processed, total, operation)

        then: "all values should be updated"
        processInfo.processedItems == processed
        processInfo.totalItems == total
        processInfo.progressPercentage == 25
        processInfo.currentOperation == operation
    }

    def "should mark process as completed"() {
        when: "marking as completed"
        processContext.markCompleted()

        then: "process should be completed"
        processInfo.status == ProcessStatus.COMPLETED
        processInfo.progressPercentage == 100
        processInfo.currentOperation == "Completed"
        processInfo.endTime != null
    }

    def "should mark process as failed"() {
        given: "an error message"
        def errorMessage = "Database connection failed"

        when: "marking as failed"
        processContext.markFailed(errorMessage)

        then: "process should be failed"
        processInfo.status == ProcessStatus.FAILED
        processInfo.errorMessage == errorMessage
        processInfo.endTime != null
    }

    def "should mark process as aborted"() {
        when: "marking as aborted"
        processContext.markAborted()

        then: "process should be aborted"
        processInfo.status == ProcessStatus.ABORTED
        processInfo.currentOperation == "Aborted"
        processInfo.endTime != null
    }

    def "should handle progress callback"() {
        given: "a progress callback"
        def callbackInvoked = false
        def receivedProcessInfo = null
        
        processContext.setProgressCallback { info ->
            callbackInvoked = true
            receivedProcessInfo = info
        }

        when: "updating progress"
        processContext.updateProgress(10, 100)

        then: "callback should be invoked"
        callbackInvoked
        receivedProcessInfo == processInfo
    }

    def "should handle callback exceptions gracefully"() {
        given: "a callback that throws an exception"
        processContext.setProgressCallback { info ->
            throw new RuntimeException("Callback error")
        }

        when: "updating progress"
        processContext.updateProgress(10, 100)

        then: "no exception should propagate"
        notThrown(Exception)
        processInfo.processedItems == 10
        processInfo.totalItems == 100
    }

    def "should clamp progress percentage to valid range"() {
        when: "setting progress percentage outside valid range"
        processContext.updateProgressPercentage(-10)
        def negativeResult = processInfo.progressPercentage

        processContext.updateProgressPercentage(150)
        def overResult = processInfo.progressPercentage

        then: "percentage should be clamped"
        negativeResult == 0
        overResult == 100
    }

    def "should calculate progress percentage correctly"() {
        when: "updating progress with different ratios"
        processContext.updateProgress(0, 100)
        def zeroPercent = processInfo.progressPercentage

        processContext.updateProgress(25, 100)
        def quarterPercent = processInfo.progressPercentage

        processContext.updateProgress(50, 100)
        def halfPercent = processInfo.progressPercentage

        processContext.updateProgress(100, 100)
        def fullPercent = processInfo.progressPercentage

        then: "percentages should be calculated correctly"
        zeroPercent == 0
        quarterPercent == 25
        halfPercent == 50
        fullPercent == 100
    }

    def "should handle zero total items gracefully"() {
        when: "updating progress with zero total"
        processContext.updateProgress(5, 0)

        then: "should not cause division by zero"
        processInfo.processedItems == 5
        processInfo.totalItems == 0
        // Progress percentage should remain unchanged when total is 0
        processInfo.progressPercentage == 0
    }
}
