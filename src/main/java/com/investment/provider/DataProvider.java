package com.investment.provider;

import com.investment.database.DatabaseManager;
import com.investment.model.Instrument;

import java.time.LocalDate;

public interface DataProvider {
    /**
     * Downloads historical OHLCV data for the specified instrument and date range.
     * 
     * @param instrument The financial instrument
     * @param startDate The start date for historical data
     * @param endDate The end date for historical data
     * @param dbManager Database manager to save the downloaded data
     */
    void downloadHistoricalData(Instrument instrument, LocalDate startDate, LocalDate endDate, DatabaseManager dbManager);
}
