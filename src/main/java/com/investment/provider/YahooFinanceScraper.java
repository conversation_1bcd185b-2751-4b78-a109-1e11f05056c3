package com.investment.provider;

import com.investment.model.OHLCV;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileFilter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * <PERSON>raper for Yahoo Finance historical data.
 * This class handles the HTML scraping of Yahoo Finance pages to extract OHLCV data.
 */
public class YahooFinanceScraper {
    private static final Logger logger = LoggerFactory.getLogger(YahooFinanceScraper.class);
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MMM d, yyyy");
    private static final Pattern DIVIDEND_PATTERN = Pattern.compile("Dividend", Pattern.CASE_INSENSITIVE);

    // Directory for storing HTML files
    private static final String HTML_STORAGE_DIR = System.getProperty("user.home") + "/Downloads/yahoo-finance-html";
    private static final String HTML_FILE_PATTERN = "yahoo_finance_%s_%s.html";

    // Allow overriding the storage directory for testing
    private String htmlStorageDir = HTML_STORAGE_DIR;

    /**
     * Sets the HTML storage directory. Used primarily for testing.
     * @param htmlStorageDir The directory path to use for storing HTML files
     */
    void setHtmlStorageDir(String htmlStorageDir) {
        this.htmlStorageDir = htmlStorageDir;
    }

    /**
     * Ensures that the HTML storage directory exists.
     * Creates the directory if it doesn't exist.
     *
     * @return The path to the HTML storage directory
     * @throws IOException If there's an error creating the directory
     */
    private Path ensureHtmlStorageDirectoryExists() throws IOException {
        Path dirPath = Paths.get(htmlStorageDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
                logger.info("Created HTML storage directory: {}", dirPath);
            } catch (IOException e) {
                logger.error("Failed to create HTML storage directory: {}", dirPath, e);
                throw e;
            }
        }
        return dirPath;
    }

    /**
     * Saves HTML content to a file in the HTML storage directory.
     *
     * @param symbol      The stock symbol
     * @param htmlContent The HTML content to save
     * @return The path to the saved file
     * @throws IOException If there's an error saving the file
     */
    private Path saveHtmlToFile(String symbol, String htmlContent) throws IOException {
        Path dirPath = ensureHtmlStorageDirectoryExists();
        String timestamp = Long.toString(Instant.now().getEpochSecond());
        String fileName = String.format(HTML_FILE_PATTERN, symbol, timestamp);
        Path filePath = dirPath.resolve(fileName);

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath.toFile()))) {
            writer.write(htmlContent);
            logger.info("Saved HTML content to file: {}", filePath);
        } catch (IOException e) {
            logger.error("Failed to save HTML content to file: {}", filePath, e);
            throw e;
        }

        return filePath;
    }

    /**
     * Reads HTML content from a file.
     *
     * @param filePath The path to the file to read
     * @return The HTML content from the file
     * @throws IOException If there's an error reading the file
     */
    private String readHtmlFromFile(Path filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath.toFile()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            logger.info("Read HTML content from file: {}", filePath);
        } catch (IOException e) {
            logger.error("Failed to read HTML content from file: {}", filePath, e);
            throw e;
        }

        return content.toString();
    }

    /**
     * Scrapes historical OHLCV data from Yahoo Finance for the given symbol and date range.
     *
     * @param symbol    The stock symbol
     * @param period1   Start date as Unix timestamp
     * @param period2   End date as Unix timestamp
     * @return List of OHLCV data points
     * @throws IOException If there's an error fetching or parsing the data
     */
    public List<OHLCV> scrapeHistoricalData(String symbol, long period1, long period2) throws IOException {
        return scrapeHistoricalData(symbol, period1, period2, true);
    }
    public List<OHLCV> scrapeHistoricalData(String symbol, long period1, long period2, boolean cleanUpBeforeScraping) throws IOException {
        // Build the URL for the history page
        String url = String.format(
                "https://finance.yahoo.com/quote/%s/history/?period1=%d&period2=%d&interval=1d",
                symbol, period1, period2);

        logger.info("Scraping data from Yahoo Finance: {}", url);

        // Create request with proper headers to avoid being blocked
        Request request = new Request.Builder()
                .url(url)
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
                .header("Accept-Language", "en-US,en;q=0.5")
                .build();

        String htmlContent;
        Path savedFilePath = null;

        // Download the HTML content
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to download data: " + response);
            }

            htmlContent = response.body().string();

            // Save the HTML content to a file
            try {
                if (cleanUpBeforeScraping) {
                    cleanUpHtmlFiles();
                }
                savedFilePath = saveHtmlToFile(symbol, htmlContent);
            } catch (IOException e) {
                // Log the error but continue with the in-memory content
                logger.warn("Failed to save HTML content to file, continuing with in-memory content", e);
            }
        }

        // If we successfully saved the file, read it back
        if (savedFilePath != null) {
            try {
                htmlContent = readHtmlFromFile(savedFilePath);
            } catch (IOException e) {
                // Log the error but continue with the original in-memory content
                logger.warn("Failed to read HTML content from file, continuing with original content", e);
            }
        }

        // Parse the HTML content
        return parseHtmlData(htmlContent, symbol);
    }

    /**
     * Parses the HTML content from Yahoo Finance to extract OHLCV data.
     *
     * @param htmlContent The HTML content from Yahoo Finance
     * @param symbol      The stock symbol
     * @return List of OHLCV data points
     */
    private List<OHLCV> parseHtmlData(String htmlContent, String symbol) {
        List<OHLCV> result = new ArrayList<>();

        try {
            Document doc = Jsoup.parse(htmlContent);

//            // Find the table with historical data
//            // The table is typically in a div with data-test="historical-prices"
//            Element table = doc.selectFirst("table[data-test='historical-prices']");
//            if (table == null) {
//                logger.warn("Could not find historical prices table for {}", symbol);
//                return result;
//            }
//
//            // Get all rows except the header
//            Elements rows = table.select("tbody tr");

            // Find rows with the new class that contains historical data
            Elements rows = doc.select("tr.yf-1jecxey");

            if (rows.isEmpty()) {
                logger.warn("Could not find historical prices rows for {}", symbol);
                return result;
            }

            for (Element row : rows) {
                try {
                    Elements cells = row.select("td");

                    // Skip if we don't have enough cells or if this is a dividend row
                    if (cells.size() < 6 || DIVIDEND_PATTERN.matcher(cells.get(1).text()).find()) {
                        continue;
                    }

                    // Parse date
                    String dateStr = cells.get(0).text();
                    LocalDate date;
                    try {
                        date = LocalDate.parse(dateStr, DATE_FORMATTER);
                    } catch (DateTimeParseException e) {
                        logger.warn("Could not parse date: {}", dateStr);
                        continue;
                    }

                    // Parse OHLCV values
                    // Yahoo Finance format: Open, High, Low, Close, Adj Close, Volume
                    String openStr = cells.get(1).text().replace(",", "");
                    String highStr = cells.get(2).text().replace(",", "");
                    String lowStr = cells.get(3).text().replace(",", "");
                    String closeStr = cells.get(4).text().replace(",", "");
                    String volumeStr = cells.get(6).text().replace(",", "");

                    // Skip if any value is "null" or empty
                    if (openStr.equals("null") || highStr.equals("null") ||
                        lowStr.equals("null") || closeStr.equals("null") ||
                        volumeStr.equals("null") || volumeStr.isEmpty() ||
                        openStr.equals("-") || highStr.equals("-") ||
                        lowStr.equals("-") || closeStr.equals("-") ||
                        volumeStr.equals("-")) {
                        continue;
                    }

                    double open = Double.parseDouble(openStr);
                    double high = Double.parseDouble(highStr);
                    double low = Double.parseDouble(lowStr);
                    double close = Double.parseDouble(closeStr);
                    long volume = Long.parseLong(volumeStr);

                    result.add(new OHLCV(symbol, date, open, high, low, close, volume));
                } catch (NumberFormatException e) {
                    logger.warn("Error parsing number in row: {}", row.text(), e);
                } catch (Exception e) {
                    logger.warn("Error processing row: {}", row.text(), e);
                }
            }
        } catch (Exception e) {
            logger.error("Error parsing HTML data for {}", symbol, e);
        }

        logger.info("Extracted {} OHLCV data points for {}", result.size(), symbol);
        return result;
    }

    /**
     * Cleans up all HTML files in the HTML storage directory.
     * This method removes all files ending with .html to prevent disk space accumulation.
     *
     * @throws IOException If there's an error accessing or deleting files
     */
    private void cleanUpHtmlFiles() throws IOException {
        Path dirPath = Paths.get(htmlStorageDir);

        // Check if directory exists
        if (!Files.exists(dirPath)) {
            logger.debug("HTML storage directory does not exist, nothing to clean up: {}", dirPath);
            return;
        }

        File directory = dirPath.toFile();
        if (!directory.isDirectory()) {
            logger.warn("HTML storage path is not a directory: {}", dirPath);
            return;
        }

        // Find all HTML files in the directory
        File[] htmlFiles = directory.listFiles(new FileFilter() {
            @Override
            public boolean accept(File file) {
                return file.isFile() && file.getName().toLowerCase().endsWith(".html");
            }
        });

        if (htmlFiles == null) {
            logger.warn("Unable to list files in directory: {}", dirPath);
            return;
        }

        int deletedCount = 0;
        int failedCount = 0;

        // Delete each HTML file
        for (File htmlFile : htmlFiles) {
            try {
                if (htmlFile.delete()) {
                    deletedCount++;
                    logger.debug("Deleted HTML file: {}", htmlFile.getName());
                } else {
                    failedCount++;
                    logger.warn("Failed to delete HTML file: {}", htmlFile.getName());
                }
            } catch (SecurityException e) {
                failedCount++;
                logger.error("Security exception while deleting HTML file: {}", htmlFile.getName(), e);
            }
        }

        if (deletedCount > 0 || failedCount > 0) {
            logger.info("HTML cleanup completed: {} files deleted, {} files failed to delete",
                       deletedCount, failedCount);
        } else {
            logger.debug("No HTML files found to clean up in directory: {}", dirPath);
        }

        // If there were failures, throw an exception
        if (failedCount > 0) {
            throw new IOException(String.format("Failed to delete %d HTML files during cleanup", failedCount));
        }
    }


}

