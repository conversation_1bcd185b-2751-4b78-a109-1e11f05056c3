package com.investment.model;

import java.math.BigDecimal;

public class Instrument {
    private final String symbol;
    private final String name;
    private final InstrumentType type;
    private final BigDecimal marketCap;
    private final String country;
    private final Integer ipoYear;
    private final String sector;
    private final String industry;

    // Backward compatibility constructor
    public Instrument(String symbol, String name, InstrumentType type) {
        this(symbol, name, type, null, null, null, null, null);
    }

    // Full constructor with all fields
    public Instrument(String symbol, String name, InstrumentType type,
                     BigDecimal marketCap, String country, Integer ipoYear,
                     String sector, String industry) {
        this.symbol = symbol;
        this.name = name;
        this.type = type;
        this.marketCap = marketCap;
        this.country = country;
        this.ipoYear = ipoYear;
        this.sector = sector;
        this.industry = industry;
    }

    public String getSymbol() {
        return symbol;
    }

    public String getName() {
        return name;
    }

    public InstrumentType getType() {
        return type;
    }

    public BigDecimal getMarketCap() {
        return marketCap;
    }

    public String getCountry() {
        return country;
    }

    public Integer getIpoYear() {
        return ipoYear;
    }

    public String getSector() {
        return sector;
    }

    public String getIndustry() {
        return industry;
    }

    @Override
    public String toString() {
        return "Instrument{" +
                "symbol='" + symbol + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", marketCap=" + marketCap +
                ", country='" + country + '\'' +
                ", ipoYear=" + ipoYear +
                ", sector='" + sector + '\'' +
                ", industry='" + industry + '\'' +
                '}';
    }
}
