package com.investment.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Domain model representing a watch list item for tracking financial instruments.
 * Follows garbage-free patterns for low-latency trading systems.
 */
public class WatchListItem {
    
    private Long id;
    private Integer displayIndex;
    private String symbol;
    private LocalDate startDate;
    private String remarks;
    private BigDecimal oneMonthPerf;
    private BigDecimal threeMonthPerf;
    private BigDecimal sixMonthPerf;
    private Integer bullishBbStreak;
    private Integer dmiBullishStreak;
    private Integer combinedSignalStreak;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;

    /**
     * Default constructor.
     */
    public WatchListItem() {
        this.createdDate = LocalDateTime.now();
        this.updatedDate = LocalDateTime.now();
    }

    /**
     * Constructor for creating a new watch list item.
     */
    public WatchListItem(Integer displayIndex, String symbol, LocalDate startDate) {
        this();
        this.displayIndex = displayIndex;
        this.symbol = symbol != null ? symbol.toUpperCase() : null;
        this.startDate = startDate;
    }

    /**
     * Constructor with remarks.
     */
    public WatchListItem(Integer displayIndex, String symbol, LocalDate startDate, String remarks) {
        this(displayIndex, symbol, startDate);
        this.remarks = remarks;
    }

    /**
     * Update performance metrics.
     */
    public void updatePerformance(BigDecimal oneMonthPerf, BigDecimal threeMonthPerf, BigDecimal sixMonthPerf) {
        this.oneMonthPerf = oneMonthPerf;
        this.threeMonthPerf = threeMonthPerf;
        this.sixMonthPerf = sixMonthPerf;
        this.updatedDate = LocalDateTime.now();
    }

    /**
     * Update display index for reordering.
     */
    public void updateDisplayIndex(Integer newDisplayIndex) {
        this.displayIndex = newDisplayIndex;
        this.updatedDate = LocalDateTime.now();
    }

    /**
     * Update remarks.
     */
    public void updateRemarks(String newRemarks) {
        this.remarks = newRemarks;
        this.updatedDate = LocalDateTime.now();
    }

    /**
     * Check if the item has any performance data.
     */
    public boolean hasPerformanceData() {
        return oneMonthPerf != null || threeMonthPerf != null || sixMonthPerf != null;
    }

    /**
     * Get the best performing period (highest percentage).
     */
    public BigDecimal getBestPerformance() {
        BigDecimal best = null;
        
        if (oneMonthPerf != null) {
            best = oneMonthPerf;
        }
        
        if (threeMonthPerf != null && (best == null || threeMonthPerf.compareTo(best) > 0)) {
            best = threeMonthPerf;
        }
        
        if (sixMonthPerf != null && (best == null || sixMonthPerf.compareTo(best) > 0)) {
            best = sixMonthPerf;
        }
        
        return best;
    }

    /**
     * Get the worst performing period (lowest percentage).
     */
    public BigDecimal getWorstPerformance() {
        BigDecimal worst = null;
        
        if (oneMonthPerf != null) {
            worst = oneMonthPerf;
        }
        
        if (threeMonthPerf != null && (worst == null || threeMonthPerf.compareTo(worst) < 0)) {
            worst = threeMonthPerf;
        }
        
        if (sixMonthPerf != null && (worst == null || sixMonthPerf.compareTo(worst) < 0)) {
            worst = sixMonthPerf;
        }
        
        return worst;
    }

    /**
     * Check if the item is performing positively overall.
     */
    public boolean isPerformingPositively() {
        BigDecimal best = getBestPerformance();
        return best != null && best.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Validate the watch list item data.
     */
    public void validate() {
        if (symbol == null || symbol.trim().isEmpty()) {
            throw new IllegalArgumentException("Symbol cannot be null or empty");
        }
        
        if (displayIndex == null || displayIndex < 0) {
            throw new IllegalArgumentException("Display index must be a non-negative integer");
        }
        
        if (startDate == null) {
            throw new IllegalArgumentException("Start date cannot be null");
        }
        
        if (startDate.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("Start date cannot be in the future");
        }
        
        if (remarks != null && remarks.length() > 128) {
            throw new IllegalArgumentException("Remarks cannot exceed 128 characters");
        }
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getDisplayIndex() {
        return displayIndex;
    }

    public void setDisplayIndex(Integer displayIndex) {
        this.displayIndex = displayIndex;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol != null ? symbol.toUpperCase() : null;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public BigDecimal getOneMonthPerf() {
        return oneMonthPerf;
    }

    public void setOneMonthPerf(BigDecimal oneMonthPerf) {
        this.oneMonthPerf = oneMonthPerf;
    }

    public BigDecimal getThreeMonthPerf() {
        return threeMonthPerf;
    }

    public void setThreeMonthPerf(BigDecimal threeMonthPerf) {
        this.threeMonthPerf = threeMonthPerf;
    }

    public BigDecimal getSixMonthPerf() {
        return sixMonthPerf;
    }

    public void setSixMonthPerf(BigDecimal sixMonthPerf) {
        this.sixMonthPerf = sixMonthPerf;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Integer getBullishBbStreak() {
        return bullishBbStreak;
    }

    public void setBullishBbStreak(Integer bullishBbStreak) {
        this.bullishBbStreak = bullishBbStreak;
    }

    public Integer getDmiBullishStreak() {
        return dmiBullishStreak;
    }

    public void setDmiBullishStreak(Integer dmiBullishStreak) {
        this.dmiBullishStreak = dmiBullishStreak;
    }

    public Integer getCombinedSignalStreak() {
        return combinedSignalStreak;
    }

    public void setCombinedSignalStreak(Integer combinedSignalStreak) {
        this.combinedSignalStreak = combinedSignalStreak;
    }

    @Override
    public String toString() {
        return "WatchListItem{" +
                "id=" + id +
                ", displayIndex=" + displayIndex +
                ", symbol='" + symbol + '\'' +
                ", startDate=" + startDate +
                ", remarks='" + remarks + '\'' +
                ", oneMonthPerf=" + oneMonthPerf +
                ", threeMonthPerf=" + threeMonthPerf +
                ", sixMonthPerf=" + sixMonthPerf +
                ", bullishBbStreak=" + bullishBbStreak +
                ", dmiBullishStreak=" + dmiBullishStreak +
                ", combinedSignalStreak=" + combinedSignalStreak +
                ", createdDate=" + createdDate +
                ", updatedDate=" + updatedDate +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        WatchListItem that = (WatchListItem) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        return symbol != null ? symbol.equals(that.symbol) : that.symbol == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (symbol != null ? symbol.hashCode() : 0);
        return result;
    }
}
