package com.investment.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Model representing a company from SEC data.
 * Maps to the structure of SEC company_tickers.json file.
 */
public class SecCompany {
    @JsonProperty("cik_str")
    private String cikStr;
    
    @JsonProperty("ticker")
    private String ticker;
    
    @JsonProperty("title")
    private String title;
    
    // Default constructor for Jackson
    public SecCompany() {}
    
    public SecCompany(String cikStr, String ticker, String title) {
        this.cikStr = cikStr;
        this.ticker = ticker;
        this.title = title;
    }
    
    public String getCikStr() {
        return cikStr;
    }
    
    public void setCikStr(String cikStr) {
        this.cikStr = cikStr;
    }
    
    public String getTicker() {
        return ticker;
    }
    
    public void setTicker(String ticker) {
        this.ticker = ticker;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    @Override
    public String toString() {
        return "SecCompany{" +
                "cikStr='" + cikStr + '\'' +
                ", ticker='" + ticker + '\'' +
                ", title='" + title + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        SecCompany that = (SecCompany) o;
        
        if (cikStr != null ? !cikStr.equals(that.cikStr) : that.cikStr != null) return false;
        if (ticker != null ? !ticker.equals(that.ticker) : that.ticker != null) return false;
        return title != null ? title.equals(that.title) : that.title == null;
    }
    
    @Override
    public int hashCode() {
        int result = cikStr != null ? cikStr.hashCode() : 0;
        result = 31 * result + (ticker != null ? ticker.hashCode() : 0);
        result = 31 * result + (title != null ? title.hashCode() : 0);
        return result;
    }
}
