package com.investment.service;

import com.investment.api.model.DMIRequest;
import com.investment.api.model.DMIResponse;
import com.investment.database.DatabaseManager;
import com.investment.process.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for calculating DMI (Directional Movement Index) technical indicators.
 * Follows the same patterns as BollingerBandService for consistency.
 */
@Service
public class DMIService {

    private static final Logger logger = LoggerFactory.getLogger(DMIService.class);

    private final DatabaseManager databaseManager;
    private final AsyncProcessExecutor processExecutor;

    @Autowired
    public DMIService(DatabaseManager databaseManager, AsyncProcessExecutor processExecutor) {
        this.databaseManager = databaseManager;
        this.processExecutor = processExecutor;
    }

    /**
     * Calculate DMI for all symbols or a subset based on the request parameters.
     * This method runs synchronously and is suitable for smaller datasets.
     * For large datasets, consider using calculateDMIAsync().
     *
     * @param request The DMI calculation request
     * @return Response containing calculation results and statistics
     */
    public DMIResponse calculateDMI(DMIRequest request) {
        logger.info("Starting DMI calculation with parameters: {}", request);

        // Validate pagination parameters
        try {
            request.validatePagination();
        } catch (IllegalArgumentException e) {
            logger.error("Invalid pagination parameters: {}", e.getMessage());
            return createErrorResponse(request, "Invalid pagination parameters: " + e.getMessage());
        }

        long startTime = System.currentTimeMillis();
        List<String> symbolsWithInsufficientData = new ArrayList<>();
        List<String> skippedSymbols = new ArrayList<>();
        List<String> failedSymbols = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        
        int processedSymbols = 0;
        int totalRecordsUpdated = 0;

        // Initialize pagination metadata
        int totalSymbolsAvailable = 0;
        int startIndex = request.getStartIndex();
        int effectiveEndIndex = startIndex;
        int symbolsInRange = 0;

        try {
            // Get total count for pagination metadata
            totalSymbolsAvailable = databaseManager.getTotalSymbolsWithOhlcvDataCount();

            // Calculate pagination parameters
            effectiveEndIndex = calculateEffectiveEndIndex(request, totalSymbolsAvailable);
            symbolsInRange = Math.max(0, Math.min(effectiveEndIndex - startIndex, totalSymbolsAvailable - startIndex));

            // Get symbols to process with pagination
            List<String> symbols = getSymbolsToProcess(request, totalSymbolsAvailable);
            logger.debug("Found {} symbols to potentially process (total available: {}, range: {}-{})",
                        symbols.size(), totalSymbolsAvailable, startIndex, effectiveEndIndex - 1);

            // Determine calculation mode
            DMIRequest.CalculationMode mode = determineCalculationMode(request);
            logger.debug("Using calculation mode: {}", mode);

            for (String symbol : symbols) {
                try {
                    // Check if symbol has sufficient data
                    int recordCount = databaseManager.countOhlcvRecords(symbol);
                    if (recordCount < request.getMinDataPoints()) {
                        logger.debug("Symbol {} has insufficient data: {} records (need {})", 
                                   symbol, recordCount, request.getMinDataPoints());
                        symbolsWithInsufficientData.add(symbol);
                        continue;
                    }

                    // Check if we should skip this symbol
                    if (shouldSkipSymbol(symbol, mode, request)) {
                        logger.debug("Skipping symbol {} (already has DMI data)", symbol);
                        skippedSymbols.add(symbol);
                        continue;
                    }

                    // Calculate DMI for this symbol
                    int recordsUpdated = calculateDMIForSymbol(symbol, request, mode);
                    totalRecordsUpdated += recordsUpdated;
                    processedSymbols++;

                    logger.debug("Processed symbol {}: {} records updated", symbol, recordsUpdated);

                    logger.info("Progress: {}/{} symbols processed, {} skipped",
                        processedSymbols, symbols.size(), skippedSymbols.size());

                    // Check if we've reached the maximum number of symbols
                    if (request.getMaxSymbols() > 0 && processedSymbols >= request.getMaxSymbols()) {
                        logger.info("Reached maximum symbols limit: {}", request.getMaxSymbols());
                        break;
                    }

                } catch (Exception e) {
                    logger.error("Failed to calculate DMI for symbol: {}", symbol, e);
                    failedSymbols.add(symbol);
                    errors.add(String.format("Symbol %s: %s", symbol, e.getMessage()));
                }
            }

        } catch (Exception e) {
            logger.error("Error during DMI calculation", e);
            errors.add("General error: " + e.getMessage());
        }

        long processingTime = System.currentTimeMillis() - startTime;
        String status = errors.isEmpty() ? "success" : "partial_success";

        // Determine calculation mode for response
        DMIRequest.CalculationMode finalMode = determineCalculationMode(request);

        DMIResponse.CalculationParameters parameters = new DMIResponse.CalculationParameters(
                request.getPeriod(),
                request.getMinDataPoints(),
                finalMode,
                request.getCalculationMethod()
        );

        DMIResponse response = new DMIResponse(
                status,
                processedSymbols,
                totalRecordsUpdated,
                symbolsWithInsufficientData,
                skippedSymbols,
                failedSymbols,
                processingTime,
                errors,
                request.isDryRun(),
                parameters,
                totalSymbolsAvailable,
                startIndex,
                effectiveEndIndex,
                symbolsInRange
        );

        logger.info("DMI calculation completed: {}", response.getSummary());
        return response;
    }

    /**
     * Calculate DMI asynchronously with full process tracking and cancellation support.
     * This method is recommended for large datasets that may take significant time.
     *
     * @param request The DMI calculation request
     * @param initiatedBy Who initiated the process (for tracking)
     * @return CompletableFuture containing the process execution result
     */
    public CompletableFuture<AsyncProcessExecutor.ProcessExecutionResult<DMIResponse>> calculateDMIAsync(
            DMIRequest request, String initiatedBy) {

        String description = String.format("DMI calculation (period=%d, mode=%s, method=%s)",
                request.getPeriod(), request.getCalculationMode(), request.getCalculationMethod());

        return processExecutor.<DMIResponse>executeAsync(
                ProcessType.DMI_CALCULATION,
                description,
                initiatedBy,
                context -> {
                    try {
                        return calculateDMIWithProcessTracking(request, context);
                    } catch (ProcessContext.ProcessCancelledException e) {
                        throw new RuntimeException(e);
                    }
                }
        );
    }

    /**
     * Start DMI calculation asynchronously and return the process ID immediately.
     * This method is designed for REST API endpoints that need to return quickly.
     *
     * @param request The DMI calculation request
     * @param initiatedBy Who initiated the process (for tracking)
     * @return Process ID for tracking the calculation
     */
    public String startDMICalculationAsync(DMIRequest request, String initiatedBy) {
        String description = String.format("DMI calculation (period=%d, mode=%s, method=%s)",
                request.getPeriod(), request.getCalculationMode(), request.getCalculationMethod());

        return processExecutor.startAsync(
                ProcessType.DMI_CALCULATION,
                description,
                initiatedBy,
                context -> {
                    try {
                        return calculateDMIWithProcessTracking(request, context);
                    } catch (ProcessContext.ProcessCancelledException e) {
                        throw new RuntimeException(e);
                    }
                }
        );
    }

    /**
     * Calculate DMI with process tracking and cancellation support.
     * This is the core implementation that supports both sync and async execution.
     */
    private DMIResponse calculateDMIWithProcessTracking(DMIRequest request, ProcessContext context)
            throws ProcessContext.ProcessCancelledException {

        logger.info("Starting DMI calculation with process tracking: {}", request);
        context.updateCurrentOperation("Validating request parameters");

        // Validate pagination parameters
        try {
            request.validatePagination();
        } catch (IllegalArgumentException e) {
            logger.error("Invalid pagination parameters: {}", e.getMessage());
            throw new RuntimeException("Invalid pagination parameters: " + e.getMessage());
        }

        long startTime = System.currentTimeMillis();
        List<String> symbolsWithInsufficientData = new ArrayList<>();
        List<String> skippedSymbols = new ArrayList<>();
        List<String> failedSymbols = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        int processedSymbols = 0;
        int totalRecordsUpdated = 0;

        // Initialize pagination metadata
        int totalSymbolsAvailable = 0;
        int startIndex = request.getStartIndex();
        int effectiveEndIndex = startIndex;
        int symbolsInRange = 0;

        try {
            context.updateCurrentOperation("Retrieving symbols from database");
            context.throwIfCancellationRequested();

            // Get total count for pagination metadata
            totalSymbolsAvailable = databaseManager.getTotalSymbolsWithOhlcvDataCount();

            // Calculate pagination parameters
            effectiveEndIndex = calculateEffectiveEndIndex(request, totalSymbolsAvailable);
            symbolsInRange = Math.max(0, Math.min(effectiveEndIndex - startIndex, totalSymbolsAvailable - startIndex));

            // Get symbols to process with pagination
            List<String> symbols = getSymbolsToProcess(request, totalSymbolsAvailable);
            logger.debug("Found {} symbols to potentially process (total available: {}, range: {}-{})",
                        symbols.size(), totalSymbolsAvailable, startIndex, effectiveEndIndex - 1);

            // Update progress tracking
            context.updateProgress(0, symbols.size(), "Starting DMI calculations");

            // Determine calculation mode
            DMIRequest.CalculationMode mode = determineCalculationMode(request);
            logger.debug("Using calculation mode: {}", mode);

            for (int i = 0; i < symbols.size(); i++) {
                String symbol = symbols.get(i);

                // Check for cancellation before processing each symbol
                context.throwIfCancellationRequested();

                try {
                    context.updateCurrentOperation(String.format("Processing symbol %s (%d/%d)",
                            symbol, i + 1, symbols.size()));

                    // Check if symbol has sufficient data
                    int recordCount = databaseManager.countOhlcvRecords(symbol);
                    if (recordCount < request.getMinDataPoints()) {
                        logger.debug("Symbol {} has insufficient data: {} records (need {})",
                                   symbol, recordCount, request.getMinDataPoints());
                        symbolsWithInsufficientData.add(symbol);
                        continue;
                    }

                    // Check if we should skip this symbol
                    if (shouldSkipSymbol(symbol, mode, request)) {
                        logger.debug("Skipping symbol {} (already has DMI data)", symbol);
                        skippedSymbols.add(symbol);
                        continue;
                    }

                    // Calculate DMI for this symbol
                    int recordsUpdated = calculateDMIForSymbol(symbol, request, mode);
                    totalRecordsUpdated += recordsUpdated;
                    processedSymbols++;

                    logger.debug("Processed symbol {}: {} records updated", symbol, recordsUpdated);

                    // Update progress
                    context.updateProgress(i + 1, symbols.size(),
                            String.format("Completed %s - %d symbols processed, %d skipped",
                                    symbol, processedSymbols, skippedSymbols.size()));

                    // Check if we've reached the maximum number of symbols
                    if (request.getMaxSymbols() > 0 && processedSymbols >= request.getMaxSymbols()) {
                        logger.info("Reached maximum symbols limit: {}", request.getMaxSymbols());
                        break;
                    }

                } catch (Exception e) {
                    logger.error("Failed to calculate DMI for symbol: {}", symbol, e);
                    failedSymbols.add(symbol);
                    errors.add(String.format("Symbol %s: %s", symbol, e.getMessage()));
                }
            }

        } catch (ProcessContext.ProcessCancelledException e) {
            // Re-throw cancellation exceptions
            throw e;
        } catch (Exception e) {
            logger.error("Error during DMI calculation", e);
            errors.add("General error: " + e.getMessage());
        }

        long processingTime = System.currentTimeMillis() - startTime;
        String status = errors.isEmpty() ? "success" : "partial_success";

        // Determine calculation mode for response
        DMIRequest.CalculationMode finalMode = determineCalculationMode(request);

        DMIResponse.CalculationParameters parameters = new DMIResponse.CalculationParameters(
                request.getPeriod(),
                request.getMinDataPoints(),
                finalMode,
                request.getCalculationMethod()
        );

        DMIResponse response = new DMIResponse(
                status,
                processedSymbols,
                totalRecordsUpdated,
                symbolsWithInsufficientData,
                skippedSymbols,
                failedSymbols,
                processingTime,
                errors,
                request.isDryRun(),
                parameters,
                totalSymbolsAvailable,
                startIndex,
                effectiveEndIndex,
                symbolsInRange
        );

        context.updateCurrentOperation("DMI calculation completed");
        logger.info("DMI calculation completed: {}", response.getSummary());
        return response;
    }

    /**
     * Get the list of symbols to process based on request parameters.
     * Supports both specific symbol lists and pagination.
     */
    private List<String> getSymbolsToProcess(DMIRequest request, int totalSymbolsAvailable) throws SQLException {
        // If specific symbols are provided, use them (takes precedence over pagination)
        if (request.hasSpecificSymbols()) {
            logger.info("Processing specific symbols: {} symbols provided", request.getSymbols().size());
            logger.debug("Specific symbols: {}", request.getSymbols());

            // Validate that the symbols exist in the database
            List<String> validSymbols = new ArrayList<>();
            List<String> symbolsWithOhlcvData = databaseManager.getSymbolsWithOhlcvData();

            for (String symbol : request.getSymbols()) {
                if (symbolsWithOhlcvData.contains(symbol)) {
                    validSymbols.add(symbol);
                } else {
                    logger.warn("Symbol {} not found in OHLCV data, skipping", symbol);
                }
            }

            logger.info("Found {} valid symbols out of {} requested", validSymbols.size(), request.getSymbols().size());
            return validSymbols;
        }

        // Use pagination approach
        logger.info("Using pagination approach for symbol selection");

        // Calculate pagination parameters
        int startIndex = request.getStartIndex();
        int limit = calculateLimit(request, totalSymbolsAvailable);

        // Get symbols with pagination
        List<String> symbols = databaseManager.getSymbolsWithOhlcvData(true, startIndex, limit);

        logger.debug("Retrieved {} symbols starting from index {} with limit {}", symbols.size(), startIndex, limit);
        return symbols;
    }

    /**
     * Calculate the effective limit for database queries based on request parameters.
     */
    private int calculateLimit(DMIRequest request, int totalSymbolsAvailable) {
        int startIndex = request.getStartIndex();

        // Calculate the maximum possible symbols from start index
        int maxPossibleFromStart = Math.max(0, totalSymbolsAvailable - startIndex);

        // Start with the range from pagination parameters
        int limit = maxPossibleFromStart;

        // Apply endIndex if specified
        if (request.getEndIndex() != null) {
            int requestedRange = Math.max(0, request.getEndIndex() - startIndex);
            limit = Math.min(limit, requestedRange);
        }

        // Apply maxSymbols if specified
        if (request.getMaxSymbols() > 0) {
            limit = Math.min(limit, request.getMaxSymbols());
        }

        return Math.max(0, limit);
    }

    /**
     * Calculate the effective end index based on request parameters and total available symbols.
     */
    private int calculateEffectiveEndIndex(DMIRequest request, int totalSymbolsAvailable) {
        int startIndex = request.getStartIndex();

        // Start with the maximum possible end index
        int endIndex = totalSymbolsAvailable;

        // Apply explicit endIndex if specified
        if (request.getEndIndex() != null) {
            endIndex = Math.min(endIndex, request.getEndIndex());
        }

        // Apply maxSymbols limit if specified
        if (request.getMaxSymbols() > 0) {
            endIndex = Math.min(endIndex, startIndex + request.getMaxSymbols());
        }

        // Ensure end index is not less than start index
        return Math.max(startIndex, endIndex);
    }

    /**
     * Determine the calculation mode to use based on request parameters.
     */
    private DMIRequest.CalculationMode determineCalculationMode(DMIRequest request) {
        // Handle backward compatibility with deprecated forceRecalculate parameter
        if (request.isForceRecalculate()) {
            return DMIRequest.CalculationMode.FULL_RECALCULATION;
        }
        
        return request.getCalculationMode();
    }

    /**
     * Check if a symbol should be skipped based on the calculation mode.
     */
    private boolean shouldSkipSymbol(String symbol, DMIRequest.CalculationMode mode, DMIRequest request) throws SQLException {
        if (mode == DMIRequest.CalculationMode.SKIP_EXISTING) {
            return databaseManager.hasExistingDMIData(symbol);
        }
        
        // For INCREMENTAL and FULL_RECALCULATION modes, we don't skip symbols
        return false;
    }

    /**
     * Calculate DMI for a specific symbol using the specified calculation mode and method.
     */
    private int calculateDMIForSymbol(String symbol, DMIRequest request, DMIRequest.CalculationMode mode) throws SQLException {
        switch (mode) {
            case INCREMENTAL:
                logger.debug("Using incremental calculation mode for symbol: {} with method: {}", symbol, request.getCalculationMethod());
                return calculateDMIWithMethod(symbol, request, true);

            case FULL_RECALCULATION:
                logger.debug("Using full recalculation mode for symbol: {} with method: {}", symbol, request.getCalculationMethod());
                // Clear existing data first if not in dry run mode
                if (!request.isDryRun()) {
                    databaseManager.clearDMIData(symbol);
                }
                return calculateDMIWithMethod(symbol, request, false);

            case SKIP_EXISTING:
            default:
                logger.debug("Using skip existing mode for symbol: {} with method: {}", symbol, request.getCalculationMethod());
                return calculateDMIWithMethod(symbol, request, false);
        }
    }

    /**
     * Calculate DMI using the specified calculation method (Pure Java or Hybrid SQL+Java).
     */
    private int calculateDMIWithMethod(String symbol, DMIRequest request, boolean incremental) throws SQLException {
        switch (request.getCalculationMethod()) {
            case PURE_JAVA:
                logger.debug("Using pure Java calculation method for symbol: {}", symbol);
                if (incremental) {
                    return databaseManager.calculateAndUpdateDMIIncremental(
                            symbol,
                            request.getPeriod(),
                            request.isDryRun()
                    );
                } else {
                    return databaseManager.calculateAndUpdateDMI(
                            symbol,
                            request.getPeriod(),
                            request.isDryRun()
                    );
                }

            case HYBRID_SQL_JAVA:
                logger.debug("Using hybrid SQL+Java calculation method for symbol: {}", symbol);
                if (incremental) {
                    return databaseManager.calculateAndUpdateDMIHybridIncremental(
                            symbol,
                            request.getPeriod(),
                            request.isDryRun()
                    );
                } else {
                    return databaseManager.calculateAndUpdateDMIHybrid(
                            symbol,
                            request.getPeriod(),
                            request.isDryRun()
                    );
                }

            default:
                throw new IllegalArgumentException("Unsupported calculation method: " + request.getCalculationMethod());
        }
    }

    /**
     * Create an error response for validation failures.
     */
    private DMIResponse createErrorResponse(DMIRequest request, String errorMessage) {
        DMIResponse.CalculationParameters parameters = new DMIResponse.CalculationParameters(
                request.getPeriod(),
                request.getMinDataPoints(),
                request.getCalculationMode(),
                request.getCalculationMethod()
        );

        return new DMIResponse(
                "error",
                0,
                0,
                new ArrayList<>(),
                new ArrayList<>(),
                new ArrayList<>(),
                0L,
                List.of(errorMessage),
                request.isDryRun(),
                parameters,
                0,
                request.getStartIndex(),
                request.getStartIndex(),
                0
        );
    }
}
