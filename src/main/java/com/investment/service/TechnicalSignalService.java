package com.investment.service;

import com.investment.database.DatabaseManager;
import com.investment.model.OHLCV;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

/**
 * Service for calculating technical signal streaks for BUY indicators.
 * Implements garbage-free patterns for low-latency trading systems.
 */
@Service
public class TechnicalSignalService {
    
    private static final Logger logger = LoggerFactory.getLogger(TechnicalSignalService.class);
    
    private final DatabaseManager databaseManager;
    
    public TechnicalSignalService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }
    
    /**
     * Calculate all technical signal streaks for a given symbol.
     * Returns an array with [bullishBbStreak, dmiBullishStreak, combinedSignalStreak].
     */
    public int[] calculateTechnicalSignalStreaks(String symbol) throws SQLException {
        logger.debug("Calculating technical signal streaks for symbol: {}", symbol);
        
        // Get recent OHLCV data (last 100 days should be sufficient for streak calculation)
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(100);
        
        List<OHLCV> ohlcvData = databaseManager.getOHLCVData(symbol, startDate, endDate);
        
        if (ohlcvData.isEmpty()) {
            logger.debug("No OHLCV data found for symbol: {}", symbol);
            return new int[]{0, 0, 0};
        }
        
        // Sort by date descending (most recent first) for streak calculation
        ohlcvData.sort((a, b) -> b.getDate().compareTo(a.getDate()));
        
        int bullishBbStreak = calculateBullishBollingerStreak(ohlcvData);
        int dmiBullishStreak = calculateDMIBullishStreak(ohlcvData);
        int combinedSignalStreak = calculateCombinedSignalStreak(bullishBbStreak, dmiBullishStreak, ohlcvData);
        
        logger.debug("Calculated streaks for {}: BB={}, DMI={}, Combined={}", 
                    symbol, bullishBbStreak, dmiBullishStreak, combinedSignalStreak);
        
        return new int[]{bullishBbStreak, dmiBullishStreak, combinedSignalStreak};
    }
    
    /**
     * Calculate Bullish Bollinger Streak:
     * Count consecutive days where BOTH conditions are met:
     * 1. Close price > Bollinger Band middle line
     * 2. Candlestick is bullish (close > open)
     */
    private int calculateBullishBollingerStreak(List<OHLCV> ohlcvData) {
        int streak = 0;
        
        for (OHLCV data : ohlcvData) {
            // Check if we have required data
            if (data.getBbMiddleBand() == null) {
                break; // No Bollinger Band data available
            }
            
            boolean closeAboveBbMiddle = data.getClose() > data.getBbMiddleBand();
            boolean bullishCandle = data.getClose() > data.getOpen();
            
            if (closeAboveBbMiddle && bullishCandle) {
                streak++;
            } else {
                break; // Streak broken
            }
        }
        
        return streak;
    }
    
    /**
     * Calculate DMI Bullish Streak:
     * Count consecutive days where:
     * dmi_plus_di > dmi_minus_di AND dmi_plus_di > dmi_adx
     */
    private int calculateDMIBullishStreak(List<OHLCV> ohlcvData) {
        int streak = 0;
        
        for (OHLCV data : ohlcvData) {
            // Check if we have required DMI data
            if (data.getDmiPlusDi() == null || data.getDmiMinusDi() == null || data.getDmiAdx() == null) {
                break; // No DMI data available
            }
            
            boolean plusDiGreaterThanMinusDi = data.getDmiPlusDi() > data.getDmiMinusDi();
            boolean plusDiGreaterThanAdx = data.getDmiPlusDi() > data.getDmiAdx();
            
            if (plusDiGreaterThanMinusDi && plusDiGreaterThanAdx) {
                streak++;
            } else {
                break; // Streak broken
            }
        }
        
        return streak;
    }
    
    /**
     * Calculate Combined Signal Streak:
     * Count consecutive days where BOTH individual streaks are active.
     * This requires checking each day individually rather than just using the streak counts.
     */
    private int calculateCombinedSignalStreak(int bullishBbStreak, int dmiBullishStreak, List<OHLCV> ohlcvData) {
        // If either individual streak is 0, combined streak is also 0
        if (bullishBbStreak == 0 || dmiBullishStreak == 0) {
            return 0;
        }
        
        int combinedStreak = 0;
        
        for (OHLCV data : ohlcvData) {
            // Check Bollinger Band conditions
            boolean bullishBbCondition = false;
            if (data.getBbMiddleBand() != null) {
                boolean closeAboveBbMiddle = data.getClose() > data.getBbMiddleBand();
                boolean bullishCandle = data.getClose() > data.getOpen();
                bullishBbCondition = closeAboveBbMiddle && bullishCandle;
            }
            
            // Check DMI conditions
            boolean dmiBullishCondition = false;
            if (data.getDmiPlusDi() != null && data.getDmiMinusDi() != null && data.getDmiAdx() != null) {
                boolean plusDiGreaterThanMinusDi = data.getDmiPlusDi() > data.getDmiMinusDi();
                boolean plusDiGreaterThanAdx = data.getDmiPlusDi() > data.getDmiAdx();
                dmiBullishCondition = plusDiGreaterThanMinusDi && plusDiGreaterThanAdx;
            }
            
            // Both conditions must be true for combined signal
            if (bullishBbCondition && dmiBullishCondition) {
                combinedStreak++;
            } else {
                break; // Combined streak broken
            }
        }
        
        return combinedStreak;
    }
    
    /**
     * Update technical signal streaks for a specific watch list item.
     */
    public void updateTechnicalSignalStreaks(Long watchListId, String symbol) throws SQLException {
        logger.debug("Updating technical signal streaks for watch list item ID: {}, symbol: {}", watchListId, symbol);
        
        int[] streaks = calculateTechnicalSignalStreaks(symbol);
        
        databaseManager.updateWatchListTechnicalSignals(
            watchListId, 
            streaks[0], // bullishBbStreak
            streaks[1], // dmiBullishStreak
            streaks[2]  // combinedSignalStreak
        );
        
        logger.debug("Updated technical signal streaks for watch list item ID: {}", watchListId);
    }
}
