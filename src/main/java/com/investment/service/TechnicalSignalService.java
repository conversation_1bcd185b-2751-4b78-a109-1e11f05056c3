package com.investment.service;

import com.investment.database.DatabaseManager;
import com.investment.model.OHLCV;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

/**
 * Service for calculating technical signal streaks for BUY indicators.
 * Implements garbage-free patterns for low-latency trading systems.
 */
@Service
public class TechnicalSignalService {
    
    private static final Logger logger = LoggerFactory.getLogger(TechnicalSignalService.class);
    
    private final DatabaseManager databaseManager;
    
    public TechnicalSignalService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }
    
    /**
     * Calculate all technical signal streaks for a given symbol.
     * Returns an array with [bullishBbStreak, dmiBullishStreak, combinedSignalStreak].
     */
    public int[] calculateTechnicalSignalStreaks(String symbol) throws SQLException {
        logger.debug("Calculating technical signal streaks for symbol: {}", symbol);
        
        // Get recent OHLCV data (last 100 days should be sufficient for streak calculation)
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(100);
        
        List<OHLCV> ohlcvData = databaseManager.getOHLCVData(symbol, startDate, endDate);
        
        if (ohlcvData.isEmpty()) {
            logger.debug("No OHLCV data found for symbol: {}", symbol);
            return new int[]{0, 0, 0};
        }
        
        // Sort by date descending (most recent first) for streak calculation
        ohlcvData.sort((a, b) -> b.getDate().compareTo(a.getDate()));
        
        int bullishBbStreak = calculateBullishBollingerStreak(ohlcvData);
        int dmiBullishStreak = calculateDMIBullishStreak(ohlcvData);
        int combinedSignalStreak = calculateCombinedSignalStreak(bullishBbStreak, dmiBullishStreak, ohlcvData);
        
        logger.debug("Calculated streaks for {}: BB={}, DMI={}, Combined={}", 
                    symbol, bullishBbStreak, dmiBullishStreak, combinedSignalStreak);
        
        return new int[]{bullishBbStreak, dmiBullishStreak, combinedSignalStreak};
    }
    
    /**
     * Calculate Bullish Bollinger Streak using corrected two-phase algorithm:
     *
     * Phase 1: Count consecutive days where candlestick body is completely above BB middle
     * (BOTH open > bb_middle AND close > bb_middle)
     *
     * Phase 2: Starting from oldest day in qualified range, validate at least one bullish candlestick exists
     * (close > open). If not found, decrement streak and check next day forward until found or streak = 0.
     */
    private int calculateBullishBollingerStreak(List<OHLCV> ohlcvData) {
        if (ohlcvData.isEmpty()) {
            return 0;
        }

        // Phase 1: Initial streak count - candlestick body completely above BB middle
        int streak = 0;

        for (OHLCV data : ohlcvData) {
            // Check if we have required Bollinger Band data
            if (data.getBbMiddleBand() == null) {
                break; // No Bollinger Band data available - terminate streak calculation
            }

            // Check if candlestick body is completely above BB middle line
            boolean openAboveBbMiddle = data.getOpen() > data.getBbMiddleBand();
            boolean closeAboveBbMiddle = data.getClose() > data.getBbMiddleBand();

            if (openAboveBbMiddle && closeAboveBbMiddle) {
                streak++;
            } else {
                break; // Streak broken
            }
        }

        // If no qualified streak found, return 0
        if (streak == 0) {
            return 0;
        }

        // Phase 2: Bullish candlestick validation
        // Process from oldest day in qualified range toward most recent
        for (int i = streak - 1; i >= 0; i--) {
            OHLCV data = ohlcvData.get(i);

            // Check if candlestick is bullish (close > open)
            boolean bullishCandle = data.getClose() > data.getOpen();

            if (bullishCandle) {
                // Found bullish candlestick - validation complete
                return streak;
            } else {
                // Not bullish - decrement streak and continue validation
                streak--;
            }
        }

        // If we reach here, no bullish candlestick was found in the qualified range
        return 0;
    }
    
    /**
     * Calculate DMI Bullish Streak:
     * Count consecutive days where:
     * dmi_plus_di > dmi_minus_di AND dmi_plus_di > dmi_adx
     */
    private int calculateDMIBullishStreak(List<OHLCV> ohlcvData) {
        int streak = 0;
        
        for (OHLCV data : ohlcvData) {
            // Check if we have required DMI data
            if (data.getDmiPlusDi() == null || data.getDmiMinusDi() == null || data.getDmiAdx() == null) {
                break; // No DMI data available
            }
            
            boolean plusDiGreaterThanMinusDi = data.getDmiPlusDi() > data.getDmiMinusDi();
            boolean plusDiGreaterThanAdx = data.getDmiPlusDi() > data.getDmiAdx();
            
            if (plusDiGreaterThanMinusDi && plusDiGreaterThanAdx) {
                streak++;
            } else {
                break; // Streak broken
            }
        }
        
        return streak;
    }
    
    /**
     * Calculate Combined Signal Streak:
     * Count consecutive days where BOTH individual streaks are active.
     * This requires checking each day individually rather than just using the streak counts.
     */
    private int calculateCombinedSignalStreak(int bullishBbStreak, int dmiBullishStreak, List<OHLCV> ohlcvData) {
        // If either individual streak is 0, combined streak is also 0
        if (bullishBbStreak == 0 || dmiBullishStreak == 0) {
            return 0;
        }
        
        int combinedStreak = 0;
        
        for (OHLCV data : ohlcvData) {
            // Check Bollinger Band conditions
            boolean bullishBbCondition = false;
            if (data.getBbMiddleBand() != null) {
                boolean closeAboveBbMiddle = data.getClose() > data.getBbMiddleBand();
                boolean bullishCandle = data.getClose() > data.getOpen();
                bullishBbCondition = closeAboveBbMiddle && bullishCandle;
            }
            
            // Check DMI conditions
            boolean dmiBullishCondition = false;
            if (data.getDmiPlusDi() != null && data.getDmiMinusDi() != null && data.getDmiAdx() != null) {
                boolean plusDiGreaterThanMinusDi = data.getDmiPlusDi() > data.getDmiMinusDi();
                boolean plusDiGreaterThanAdx = data.getDmiPlusDi() > data.getDmiAdx();
                dmiBullishCondition = plusDiGreaterThanMinusDi && plusDiGreaterThanAdx;
            }
            
            // Both conditions must be true for combined signal
            if (bullishBbCondition && dmiBullishCondition) {
                combinedStreak++;
            } else {
                break; // Combined streak broken
            }
        }
        
        return combinedStreak;
    }
    
    /**
     * Update technical signal streaks for a specific watch list item.
     */
    public void updateTechnicalSignalStreaks(Long watchListId, String symbol) throws SQLException {
        logger.debug("Updating technical signal streaks for watch list item ID: {}, symbol: {}", watchListId, symbol);
        
        int[] streaks = calculateTechnicalSignalStreaks(symbol);
        
        databaseManager.updateWatchListTechnicalSignals(
            watchListId, 
            streaks[0], // bullishBbStreak
            streaks[1], // dmiBullishStreak
            streaks[2]  // combinedSignalStreak
        );
        
        logger.debug("Updated technical signal streaks for watch list item ID: {}", watchListId);
    }
}
