package com.investment.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.investment.api.model.ValidationResponse;
import com.investment.database.DatabaseManager;
import com.investment.model.SecCompany;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for validating instrument symbols against SEC official data.
 * Downloads and caches SEC company tickers data for validation.
 */
@Service
public class SymbolValidationService {
    private static final Logger logger = LoggerFactory.getLogger(SymbolValidationService.class);
    
    private static final String SEC_TICKERS_URL = "https://www.sec.gov/files/company_tickers.json";
    private static final String CACHE_DIR = "./data/sec_cache";
    private static final String CACHE_FILE = "company_tickers.json";
    private static final Duration CACHE_EXPIRY = Duration.ofHours(24); // Cache for 24 hours
    
    private final DatabaseManager databaseManager;
    private final ObjectMapper objectMapper;
    private final HttpClient httpClient;
    
    // Cache for SEC tickers to avoid repeated parsing
    private Set<String> cachedSecTickers;
    private LocalDateTime lastCacheUpdate;
    
    public SymbolValidationService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
        this.objectMapper = new ObjectMapper();
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
        
        // Ensure cache directory exists
        ensureCacheDirectoryExists();
    }
    
    /**
     * Validate all symbols in the database against SEC data.
     * 
     * @param dryRun If true, only report what would be deleted without actually deleting
     * @param forceRefresh If true, force download of fresh SEC data
     * @return ValidationResponse with results
     */
    public ValidationResponse validateSymbols(boolean dryRun, boolean forceRefresh) {
        logger.info("Starting symbol validation - dryRun: {}, forceRefresh: {}", dryRun, forceRefresh);
        
        try {
            // Get SEC tickers
            Set<String> secTickers = getSecTickers(forceRefresh);
            logger.info("Loaded {} SEC tickers for validation", secTickers.size());
            
            // Get all symbols from database
            List<String> databaseSymbols = databaseManager.getAllSymbols();
            logger.info("Found {} symbols in database", databaseSymbols.size());
            
            // Identify invalid symbols
            List<String> invalidSymbols = databaseSymbols.stream()
                    .filter(symbol -> !secTickers.contains(symbol.toUpperCase()))
                    .collect(Collectors.toList());
            
            logger.info("Found {} invalid symbols: {}", invalidSymbols.size(), invalidSymbols);
            
            // Count OHLCV records that would be affected
            int totalOhlcvRecords = 0;
            for (String symbol : invalidSymbols) {
                totalOhlcvRecords += databaseManager.countOhlcvRecords(symbol);
            }
            
            int deletedSymbols = 0;
            int deletedOhlcvRecords = 0;
            
            // Perform cleanup if not dry run
            if (!dryRun && !invalidSymbols.isEmpty()) {
                logger.info("Performing actual cleanup of {} invalid symbols", invalidSymbols.size());
                deletedOhlcvRecords = databaseManager.deleteInstrumentsAndOhlcvData(invalidSymbols);
                deletedSymbols = invalidSymbols.size();
                logger.info("Cleanup completed: deleted {} symbols and {} OHLCV records", 
                           deletedSymbols, deletedOhlcvRecords);
            } else if (dryRun) {
                logger.info("DRY RUN: Would delete {} symbols and {} OHLCV records", 
                           invalidSymbols.size(), totalOhlcvRecords);
                deletedOhlcvRecords = totalOhlcvRecords;
            }
            
            return new ValidationResponse(
                    databaseSymbols.size(),
                    databaseSymbols.size() - invalidSymbols.size(),
                    invalidSymbols.size(),
                    invalidSymbols,
                    deletedSymbols,
                    deletedOhlcvRecords,
                    dryRun
            );
            
        } catch (Exception e) {
            logger.error("Error during symbol validation", e);
            throw new RuntimeException("Symbol validation failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get SEC tickers, using cache if available and not expired.
     */
    private Set<String> getSecTickers(boolean forceRefresh) throws IOException, InterruptedException {
        // Return cached data if available and not expired
        if (!forceRefresh && cachedSecTickers != null && lastCacheUpdate != null) {
            if (Duration.between(lastCacheUpdate, LocalDateTime.now()).compareTo(CACHE_EXPIRY) < 0) {
                logger.info("Using cached SEC tickers data");
                return cachedSecTickers;
            }
        }
        
        Path cacheFilePath = Paths.get(CACHE_DIR, CACHE_FILE);
        
        // Check if cache file exists and is recent
        if (!forceRefresh && Files.exists(cacheFilePath)) {
            try {
                LocalDateTime fileTime = LocalDateTime.parse(
                    Files.getLastModifiedTime(cacheFilePath).toString().substring(0, 19),
                    DateTimeFormatter.ISO_LOCAL_DATE_TIME
                );
                
                if (Duration.between(fileTime, LocalDateTime.now()).compareTo(CACHE_EXPIRY) < 0) {
                    logger.info("Loading SEC tickers from cache file");
                    cachedSecTickers = loadTickersFromFile(cacheFilePath);
                    lastCacheUpdate = fileTime;
                    return cachedSecTickers;
                }
            } catch (Exception e) {
                logger.warn("Error reading cache file timestamp, will download fresh data", e);
            }
        }
        
        // Download fresh data
        logger.info("Downloading fresh SEC tickers data from: {}", SEC_TICKERS_URL);
        String jsonData = downloadSecData();
        
        // Save to cache
        Files.writeString(cacheFilePath, jsonData);
        logger.info("SEC data cached to: {}", cacheFilePath);
        
        // Parse and cache tickers
        cachedSecTickers = parseSecTickers(jsonData);
        lastCacheUpdate = LocalDateTime.now();
        
        return cachedSecTickers;
    }
    
    /**
     * Download SEC company tickers JSON data.
     */
    private String downloadSecData() throws IOException, InterruptedException {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(SEC_TICKERS_URL))
                .header("User-Agent", "InvestmentToolKit/1.0 (<EMAIL>)")
                .timeout(Duration.ofSeconds(60))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 200) {
            throw new IOException("Failed to download SEC data. HTTP status: " + response.statusCode());
        }
        
        logger.info("Successfully downloaded SEC data ({} bytes)", response.body().length());
        return response.body();
    }
    
    /**
     * Parse SEC JSON data and extract ticker symbols.
     */
    private Set<String> parseSecTickers(String jsonData) throws IOException {
        // The SEC JSON structure is: { "0": {"cik_str": "...", "ticker": "...", "title": "..."}, ... }
        Map<String, SecCompany> companies = objectMapper.readValue(
                jsonData, 
                new TypeReference<Map<String, SecCompany>>() {}
        );
        
        Set<String> tickers = companies.values().stream()
                .map(SecCompany::getTicker)
                .filter(Objects::nonNull)
                .map(String::toUpperCase)
                .collect(Collectors.toSet());
        
        logger.info("Parsed {} unique tickers from SEC data", tickers.size());
        return tickers;
    }
    
    /**
     * Load tickers from cached file.
     */
    private Set<String> loadTickersFromFile(Path filePath) throws IOException {
        String jsonData = Files.readString(filePath);
        return parseSecTickers(jsonData);
    }
    
    /**
     * Ensure the cache directory exists.
     */
    private void ensureCacheDirectoryExists() {
        File cacheDir = new File(CACHE_DIR);
        if (!cacheDir.exists()) {
            boolean created = cacheDir.mkdirs();
            if (created) {
                logger.info("Created SEC cache directory: {}", CACHE_DIR);
            } else {
                logger.warn("Failed to create SEC cache directory: {}", CACHE_DIR);
            }
        }
    }
    
    /**
     * Get the current cache status for debugging/monitoring.
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("cacheDirectory", CACHE_DIR);
        status.put("cacheFile", CACHE_FILE);
        status.put("cacheExpiry", CACHE_EXPIRY.toString());
        status.put("lastCacheUpdate", lastCacheUpdate);
        status.put("cachedTickersCount", cachedSecTickers != null ? cachedSecTickers.size() : 0);
        
        Path cacheFilePath = Paths.get(CACHE_DIR, CACHE_FILE);
        status.put("cacheFileExists", Files.exists(cacheFilePath));
        
        return status;
    }
}
