package com.investment.service;

import com.investment.api.model.RefreshAllResponse;
import com.investment.database.DatabaseManager;
import com.investment.model.Instrument;
import com.investment.model.InstrumentType;
import com.investment.model.OHLCV;
import com.investment.provider.DataProvider;
import com.investment.provider.YahooFinanceProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for handling OHLCV data operations.
 */
@Service
public class OHLCVService {
    private static final Logger logger = LoggerFactory.getLogger(OHLCVService.class);

    private final DatabaseManager dbManager;
    private final DataProvider dataProvider;

    public OHLCVService(DatabaseManager dbManager, DataProvider dataProvider) {
        this.dbManager = dbManager;
        this.dataProvider = dataProvider;
    }

    /**
     * Get OHLCV data for a specific symbol within a date range.
     *
     * @param symbol The stock symbol
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @return List of OHLCV data points
     */
    public List<OHLCV> getOHLCVData(String symbol, LocalDate startDate, LocalDate endDate) {
        try {
            return dbManager.getOHLCVData(symbol, startDate, endDate);
        } catch (Exception e) {
            logger.error("Error retrieving OHLCV data for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to retrieve OHLCV data", e);
        }
    }

    /**
     * Update OHLCV data for a specific symbol.
     *
     * @param symbol The stock symbol
     * @param name The instrument name (optional, used only if the instrument doesn't exist)
     * @param type The instrument type (optional, used only if the instrument doesn't exist)
     * @return Number of data points updated
     */
    public int updateOHLCVData(String symbol, String name, InstrumentType type) {
        try {
            // Get the last date we have data for
            LocalDate startDate = dbManager.getLastDataDate(symbol);
            if (startDate == null) {
                // If no data exists, start from 1962
                //startDate = LocalDate.now().minusYears(5);
                startDate = LocalDate.ofYearDay(YahooFinanceProvider.YAHOO_HISTORICAL_DATA_START_YEAR, 1);
            } else {
                // Start from the day after the last data point
                startDate = startDate.plusDays(1);
            }

            // Only download if we need new data
            if (startDate.isBefore(LocalDate.now()) || startDate.isEqual(LocalDate.now())) {
                logger.info("Updating data for {} from {} to today", symbol, startDate);

                // Create instrument object
                Instrument instrument = new Instrument(symbol, name != null ? name : symbol,
                        type != null ? type : InstrumentType.US_STOCK);

                // Download and save data
                dataProvider.downloadHistoricalData(instrument, startDate, LocalDate.now(), dbManager);

                // Return the number of days updated
                return (int) (LocalDate.now().toEpochDay() - startDate.toEpochDay() + 1);
            } else {
                logger.info("Data is already up to date for {}", symbol);
                return 0;
            }
        } catch (Exception e) {
            logger.error("Error updating OHLCV data for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to update OHLCV data", e);
        }
    }

    /**
     * Update OHLCV data for multiple symbols.
     *
     * @param symbols List of stock symbols
     * @return Number of symbols updated
     */
    public int updateOHLCVDataBatch(List<String> symbols) {
        int updatedCount = 0;

        for (String symbol : symbols) {
            try {
                int updated = updateOHLCVData(symbol, null, null);
                if (updated > 0) {
                    updatedCount++;
                }
            } catch (Exception e) {
                logger.error("Error updating OHLCV data for symbol: {}", symbol, e);
                // Continue with the next symbol
            }
        }

        return updatedCount;
    }

    /**
     * Refresh OHLCV data for all instruments in the database.
     * Processes instruments in descending order by market cap.
     *
     * @param dryRun If true, only validate and report without updating data
     * @param maxSymbols Maximum number of symbols to process
     * @param skipExisting If true, skip symbols that already have recent data
     * @return RefreshAllResponse with processing results
     */
    public RefreshAllResponse refreshAllOHLCVData(boolean dryRun, int maxSymbols, boolean skipExisting) {
        return refreshAllOHLCVData(dryRun, maxSymbols, skipExisting, 0, null);
    }

    /**
     * Refresh OHLCV data for instruments in the database with pagination support.
     * Processes instruments in descending order by market cap.
     *
     * @param dryRun If true, only validate and report without updating data
     * @param maxSymbols Maximum number of symbols to process
     * @param skipExisting If true, skip symbols that already have recent data
     * @param startIndex Starting position in the ordered instrument list (0-based)
     * @param endIndex Ending position in the ordered instrument list (exclusive), or null for startIndex + maxSymbols
     * @return RefreshAllResponse with processing results
     */
    public RefreshAllResponse refreshAllOHLCVData(boolean dryRun, int maxSymbols, boolean skipExisting,
                                                 int startIndex, Integer endIndex) {
        logger.info("Starting OHLCV refresh for all instruments - dryRun: {}, maxSymbols: {}, skipExisting: {}, startIndex: {}, endIndex: {}",
                   dryRun, maxSymbols, skipExisting, startIndex, endIndex);

        List<String> processedSymbolsList = new ArrayList<>();
        List<String> skippedSymbolsList = new ArrayList<>();
        List<String> failedSymbolsList = new ArrayList<>();

        int processedSymbols = 0;
        int skippedSymbols = 0;
        int successfulUpdates = 0;
        int failedUpdates = 0;
        int totalDataPointsUpdated = 0;

        try {
            // Calculate pagination parameters
            int effectiveEndIndex = endIndex != null ? endIndex : startIndex + maxSymbols;
            int effectiveLimit = Math.min(effectiveEndIndex - startIndex, maxSymbols);

            // Get total count for reporting
            int totalInstruments = dbManager.getTotalInstrumentCount();

            // Get instruments with pagination
            List<Instrument> instruments = dbManager.getAllInstrumentsOrderedByMarketCap(startIndex, effectiveLimit);

            logger.info("Found {} total instruments in database, fetching {} instruments from index {} to {}",
                       totalInstruments, instruments.size(), startIndex, startIndex + instruments.size() - 1);

            for (Instrument instrument : instruments) {
                // Check if we've reached the maximum number of symbols to process
                if (processedSymbols >= maxSymbols) {
                    logger.info("Reached maximum symbols limit: {}", maxSymbols);
                    break;
                }

                String symbol = instrument.getSymbol();

                try {
                    // Check if we should skip symbols with recent data
                    if (skipExisting && dbManager.hasRecentData(symbol)) {
                        skippedSymbols++;
                        skippedSymbolsList.add(symbol);
                        logger.debug("Skipping symbol {} - has recent data", symbol);
                        continue;
                    }

                    processedSymbols++;
                    processedSymbolsList.add(symbol);

                    if (!dryRun) {
                        // Actually update the data
                        logger.info("Updating OHLCV data for symbol: {} (market cap: {})",
                                   symbol, instrument.getMarketCap());

                        int dataPointsUpdated = updateOHLCVData(symbol, instrument.getName(), instrument.getType());

                        if (dataPointsUpdated > 0) {
                            successfulUpdates++;
                            totalDataPointsUpdated += dataPointsUpdated;
                            logger.info("Successfully updated {} data points for {}", dataPointsUpdated, symbol);

                        } else {
                            successfulUpdates++; // Still count as successful even if no new data
                            logger.info("No new data available for {}", symbol);
                        }

                        // Add rate limiting delay to be respectful to data providers
                        Thread.sleep(500); // 500ms delay between requests

                    } else {
                        // Dry run - just simulate the update
                        successfulUpdates++;
                        logger.debug("DRY RUN: Would update OHLCV data for symbol: {}", symbol);
                    }

                    logger.info("Progress: {}/{} symbols processed (index range: {}-{}), {} skipped",
                                processedSymbols, maxSymbols,
                                startIndex, startIndex + skippedSymbols + processedSymbols - 1, skippedSymbols);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.warn("Refresh process interrupted");
                    break;
                } catch (Exception e) {
                    failedUpdates++;
                    failedSymbolsList.add(symbol);
                    logger.error("Error updating OHLCV data for symbol: {}", symbol, e);
                    // Continue with the next symbol
                }
            }

            logger.info("OHLCV refresh completed - Processed: {}, Successful: {}, Failed: {}, Skipped: {}",
                       processedSymbols, successfulUpdates, failedUpdates, skippedSymbols);

            return new RefreshAllResponse(
                    totalInstruments, processedSymbols, skippedSymbols,
                    successfulUpdates, failedUpdates, totalDataPointsUpdated,
                    processedSymbolsList, skippedSymbolsList, failedSymbolsList, dryRun,
                    startIndex, effectiveEndIndex
            );

        } catch (Exception e) {
            logger.error("Error during OHLCV refresh operation", e);
            throw new RuntimeException("Failed to refresh OHLCV data", e);
        }
    }

    /**
     * Close database connection when service is destroyed.
     */
    public void cleanup() {
        if (dbManager != null) {
            dbManager.closeConnection();
        }
    }
}
