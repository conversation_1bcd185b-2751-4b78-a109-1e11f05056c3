package com.investment.api.controller;

import com.investment.api.model.ApiResponse;
import com.investment.api.model.CreateInstrumentRequest;
import com.investment.api.model.CsvUploadResponse;
import com.investment.api.model.InstrumentResponse;
import com.investment.api.model.PaginatedResponse;
import com.investment.api.model.SyncRequest;
import com.investment.api.model.SyncResponse;
import com.investment.api.model.ValidationRequest;
import com.investment.api.model.ValidationResponse;
import com.investment.database.DatabaseManager;
import com.investment.model.Instrument;
import com.investment.service.CsvInstrumentService;
import com.investment.service.InstrumentService;
import com.investment.service.SecSynchronizationService;
import com.investment.service.SymbolValidationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * REST controller for instrument management operations.
 */
@RestController
@RequestMapping("/api/instruments")
@Tag(name = "Instruments", description = "Instrument management operations")
public class InstrumentController {
    private static final Logger logger = LoggerFactory.getLogger(InstrumentController.class);

    private final SymbolValidationService symbolValidationService;
    private final SecSynchronizationService secSynchronizationService;
    private final CsvInstrumentService csvInstrumentService;
    private final InstrumentService instrumentService;
    private final DatabaseManager databaseManager;

    public InstrumentController(SymbolValidationService symbolValidationService,
                               SecSynchronizationService secSynchronizationService,
                               CsvInstrumentService csvInstrumentService,
                               InstrumentService instrumentService,
                               DatabaseManager databaseManager) {
        this.symbolValidationService = symbolValidationService;
        this.secSynchronizationService = secSynchronizationService;
        this.csvInstrumentService = csvInstrumentService;
        this.instrumentService = instrumentService;
        this.databaseManager = databaseManager;
    }

    /**
     * Get instruments with pagination and sorting support.
     * Returns instruments ordered by the specified criteria with pagination.
     *
     * @param page Page number (0-based)
     * @param size Number of items per page
     * @param sortBy Field to sort by (marketCap, symbol, name)
     * @param sortDir Sort direction (asc, desc)
     * @return Paginated list of instruments
     */
    @GetMapping
    @Operation(summary = "Get instruments with pagination and sorting",
               description = "Retrieves a paginated list of financial instruments with sorting support. " +
                           "Supports sorting by market cap (default), symbol, or name. " +
                           "Returns instruments in the specified order with pagination metadata.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Instruments retrieved successfully",
            content = @Content(schema = @Schema(implementation = PaginatedResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid pagination or sorting parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<PaginatedResponse<Instrument>>> getInstruments(
            @Parameter(description = "Page number (0-based)")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Number of items per page")
            @RequestParam(defaultValue = "50") int size,
            @Parameter(description = "Field to sort by (marketCap, symbol, name)")
            @RequestParam(defaultValue = "marketCap") String sortBy,
            @Parameter(description = "Sort direction (asc, desc)")
            @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            // Validate pagination parameters
            if (page < 0) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Page number must be >= 0"));
            }
            if (size <= 0 || size > 1000) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Page size must be between 1 and 1000"));
            }

            // Validate sort parameters
            if (!sortBy.matches("marketCap|symbol|name")) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("sortBy must be one of: marketCap, symbol, name"));
            }
            if (!sortDir.matches("asc|desc")) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("sortDir must be either 'asc' or 'desc'"));
            }

            logger.info("Retrieving instruments - page: {}, size: {}, sortBy: {}, sortDir: {}",
                       page, size, sortBy, sortDir);

            // Get total count for pagination metadata
            int totalElements = databaseManager.getTotalInstrumentCount();

            // Calculate offset for pagination
            int offset = page * size;

            // Check if page is beyond available data
            if (offset >= totalElements && totalElements > 0) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Page " + page + " is beyond available data. Total instruments: " + totalElements));
            }

            // Get instruments based on sort criteria
            List<Instrument> instruments;
            if ("marketCap".equals(sortBy)) {
                // Use existing market cap ordering (desc by default, reverse if asc requested)
                if ("desc".equals(sortDir)) {
                    instruments = databaseManager.getAllInstrumentsOrderedByMarketCap(offset, size);
                } else {
                    // For ascending market cap, we need all instruments and reverse
                    // This is less efficient but maintains consistency
                    instruments = databaseManager.getAllInstruments();
                    instruments.sort((a, b) -> {
                        if (a.getMarketCap() == null && b.getMarketCap() == null) return 0;
                        if (a.getMarketCap() == null) return 1;
                        if (b.getMarketCap() == null) return -1;
                        return a.getMarketCap().compareTo(b.getMarketCap());
                    });
                    // Apply pagination manually
                    int fromIndex = Math.min(offset, instruments.size());
                    int toIndex = Math.min(offset + size, instruments.size());
                    instruments = instruments.subList(fromIndex, toIndex);
                }
            } else {
                // For symbol or name sorting, get all and sort
                instruments = databaseManager.getAllInstruments();
                if ("symbol".equals(sortBy)) {
                    instruments.sort((a, b) -> "asc".equals(sortDir)
                        ? a.getSymbol().compareTo(b.getSymbol())
                        : b.getSymbol().compareTo(a.getSymbol()));
                } else { // name
                    instruments.sort((a, b) -> "asc".equals(sortDir)
                        ? a.getName().compareTo(b.getName())
                        : b.getName().compareTo(a.getName()));
                }
                // Apply pagination manually
                int fromIndex = Math.min(offset, instruments.size());
                int toIndex = Math.min(offset + size, instruments.size());
                instruments = instruments.subList(fromIndex, toIndex);
            }

            // Create paginated response
            PaginatedResponse<Instrument> paginatedResponse = new PaginatedResponse<>(
                instruments, page, size, totalElements);

            logger.info("Retrieved {} instruments for page {} (size {}), total: {}, totalPages: {}",
                       instruments.size(), page, size, totalElements, paginatedResponse.getTotalPages());

            return ResponseEntity.ok(ApiResponse.success("Instruments retrieved successfully", paginatedResponse));

        } catch (Exception e) {
            logger.error("Error retrieving instruments", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve instruments: " + e.getMessage()));
        }
    }

    /**
     * Get a specific instrument by symbol.
     *
     * @param symbol The instrument symbol
     * @return The instrument details
     */
    @GetMapping("/{symbol}")
    @Operation(summary = "Get instrument by symbol",
               description = "Retrieves detailed information for a specific financial instrument by its symbol.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Instrument found",
            content = @Content(schema = @Schema(implementation = Instrument.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404",
            description = "Instrument not found"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<Instrument>> getInstrumentBySymbol(
            @Parameter(description = "Instrument symbol", example = "AAPL")
            @PathVariable String symbol) {

        try {
            logger.info("Retrieving instrument by symbol: {}", symbol);

            String upperSymbol = symbol.toUpperCase();

            // Check if symbol exists first
            if (!databaseManager.symbolExists(upperSymbol)) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Instrument not found: " + symbol));
            }

            // Get the instrument from the list (not efficient but works for now)
            List<Instrument> allInstruments = databaseManager.getAllInstruments();
            Instrument instrument = allInstruments.stream()
                    .filter(i -> i.getSymbol().equals(upperSymbol))
                    .findFirst()
                    .orElse(null);

            if (instrument == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Instrument not found: " + symbol));
            }

            return ResponseEntity.ok(ApiResponse.success("Instrument found", instrument));

        } catch (Exception e) {
            logger.error("Error retrieving instrument by symbol: {}", symbol, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve instrument: " + e.getMessage()));
        }
    }

    /**
     * Search instruments by name or symbol.
     *
     * @param query Search query
     * @return List of matching instruments
     */
    @GetMapping("/search")
    @Operation(summary = "Search instruments",
               description = "Searches for instruments by symbol or company name. Supports partial matching.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Search completed successfully",
            content = @Content(schema = @Schema(implementation = Instrument.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid search query"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<List<Instrument>>> searchInstruments(
            @Parameter(description = "Search query (symbol or company name)", example = "AAPL")
            @RequestParam("q") String query) {

        try {
            if (query == null || query.trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Search query cannot be empty"));
            }

            logger.info("Searching instruments with query: {}", query);

            // Get all instruments and filter by query
            List<Instrument> allInstruments = databaseManager.getAllInstruments();
            String searchTerm = query.trim().toLowerCase();

            List<Instrument> matchingInstruments = allInstruments.stream()
                    .filter(instrument ->
                        instrument.getSymbol().toLowerCase().contains(searchTerm) ||
                        instrument.getName().toLowerCase().contains(searchTerm))
                    .limit(100) // Limit results to prevent overwhelming response
                    .collect(Collectors.toList());

            logger.info("Found {} instruments matching query: {}", matchingInstruments.size(), query);
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Found %d instruments", matchingInstruments.size()),
                matchingInstruments));

        } catch (Exception e) {
            logger.error("Error searching instruments with query: {}", query, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Search failed: " + e.getMessage()));
        }
    }

    /**
     * Get instrument statistics.
     *
     * @return Statistics about instruments in the database
     */
    @GetMapping("/statistics")
    @Operation(summary = "Get instrument statistics",
               description = "Returns statistical information about instruments in the database.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Statistics retrieved successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> getInstrumentStatistics() {
        try {
            logger.info("Retrieving instrument statistics");

            int totalInstruments = databaseManager.getTotalInstrumentCount();
            List<Instrument> allInstruments = databaseManager.getAllInstruments();

            long instrumentsWithMarketCap = allInstruments.stream()
                    .filter(i -> i.getMarketCap() != null)
                    .count();

            Map<String, Object> statistics = Map.of(
                "totalInstruments", totalInstruments,
                "instrumentsWithMarketCap", instrumentsWithMarketCap,
                "instrumentsWithoutMarketCap", totalInstruments - instrumentsWithMarketCap
            );

            return ResponseEntity.ok(ApiResponse.success("Statistics retrieved", statistics));

        } catch (Exception e) {
            logger.error("Error retrieving instrument statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve statistics: " + e.getMessage()));
        }
    }

    /**
     * Create a new financial instrument.
     * This endpoint allows creating a new instrument with all required and optional fields.
     *
     * @param request The instrument creation request
     * @return The created instrument details
     */
    @PostMapping
    @Operation(summary = "Create a new financial instrument",
               description = "Creates a new financial instrument in the database with the provided details. " +
                           "Symbol must be unique and follow the required format. All validation rules are applied.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "201",
            description = "Instrument created successfully",
            content = @Content(schema = @Schema(implementation = InstrumentResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request data or validation errors"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "409",
            description = "Symbol already exists"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<InstrumentResponse>> createInstrument(
            @Parameter(description = "Instrument creation request", required = true)
            @Valid @RequestBody CreateInstrumentRequest request) {

        try {
            logger.info("Creating new instrument: {}", request);

            // Create the instrument using the service
            Instrument createdInstrument = instrumentService.createInstrument(request);

            // Convert to response DTO
            InstrumentResponse response = new InstrumentResponse(createdInstrument);

            logger.info("Successfully created instrument: {}", createdInstrument.getSymbol());
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Instrument created successfully", response));

        } catch (IllegalArgumentException e) {
            logger.warn("Validation error creating instrument: {}", e.getMessage());
            if (e.getMessage().contains("already exists")) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                        .body(ApiResponse.error("Symbol already exists: " + request.getSymbol()));
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Validation error: " + e.getMessage()));
            }
        } catch (Exception e) {
            logger.error("Error creating instrument: {}", request, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create instrument: " + e.getMessage()));
        }
    }

    /**
     * Update an existing financial instrument.
     * This endpoint allows updating an instrument with comprehensive validation.
     *
     * @param symbol The symbol of the instrument to update
     * @param request The instrument update request
     * @return The updated instrument response
     */
    @PutMapping("/{symbol}")
    @Operation(summary = "Update an existing financial instrument",
               description = "Updates an existing financial instrument with comprehensive validation. " +
                           "Validates symbol format, business rules, and handles symbol changes.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Instrument updated successfully",
            content = @Content(mediaType = "application/json",
                             schema = @Schema(implementation = InstrumentResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request data or validation error",
            content = @Content(mediaType = "application/json",
                             schema = @Schema(implementation = ApiResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404",
            description = "Instrument not found",
            content = @Content(mediaType = "application/json",
                             schema = @Schema(implementation = ApiResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "409",
            description = "Symbol conflict (new symbol already exists)",
            content = @Content(mediaType = "application/json",
                             schema = @Schema(implementation = ApiResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json",
                             schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<InstrumentResponse>> updateInstrument(
            @Parameter(description = "Symbol of the instrument to update", example = "AAPL")
            @PathVariable String symbol,
            @Parameter(description = "Instrument update request", required = true)
            @Valid @RequestBody CreateInstrumentRequest request) {

        try {
            logger.info("Updating instrument: {} with data: {}", symbol, request);

            Instrument instrument = instrumentService.updateInstrument(symbol, request);
            InstrumentResponse response = new InstrumentResponse(instrument);

            logger.info("Successfully updated instrument: {}", symbol);

            return ResponseEntity.ok(ApiResponse.success("Instrument updated successfully", response));

        } catch (IllegalArgumentException e) {
            logger.warn("Validation error updating instrument {}: {}", symbol, e.getMessage());

            if (e.getMessage().contains("not found")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error(e.getMessage()));
            } else if (e.getMessage().contains("already exists")) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                        .body(ApiResponse.error(e.getMessage()));
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error(e.getMessage()));
            }

        } catch (Exception e) {
            logger.error("Error updating instrument: {}", symbol, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update instrument: " + e.getMessage()));
        }
    }

    /**
     * Delete a financial instrument.
     * This endpoint allows deleting an instrument and all its associated data.
     *
     * @param symbol The symbol of the instrument to delete
     * @return Deletion confirmation with summary
     */
    @DeleteMapping("/{symbol}")
    @Operation(summary = "Delete a financial instrument",
               description = "Deletes a financial instrument and all its associated data (OHLCV records, etc.). " +
                           "This operation is irreversible.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Instrument deleted successfully",
            content = @Content(mediaType = "application/json",
                             schema = @Schema(implementation = ApiResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404",
            description = "Instrument not found",
            content = @Content(mediaType = "application/json",
                             schema = @Schema(implementation = ApiResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(mediaType = "application/json",
                             schema = @Schema(implementation = ApiResponse.class))
        )
    })
    public ResponseEntity<ApiResponse<String>> deleteInstrument(
            @Parameter(description = "Symbol of the instrument to delete", example = "AAPL")
            @PathVariable String symbol) {

        try {
            logger.info("Deleting instrument: {}", symbol);

            String result = instrumentService.deleteInstrument(symbol);

            logger.info("Successfully deleted instrument: {}", symbol);

            return ResponseEntity.ok(ApiResponse.success("Instrument deleted successfully", result));

        } catch (IllegalArgumentException e) {
            logger.warn("Error deleting instrument {}: {}", symbol, e.getMessage());

            if (e.getMessage().contains("not found")) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error(e.getMessage()));
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error(e.getMessage()));
            }

        } catch (Exception e) {
            logger.error("Error deleting instrument: {}", symbol, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to delete instrument: " + e.getMessage()));
        }
    }

    /**
     * Validate symbols against SEC data with dry-run mode.
     * This endpoint performs validation without making any changes to the database.
     *
     * @param forceRefresh Whether to force refresh of SEC data from remote source
     * @return Validation results showing what would be cleaned up
     */
    @GetMapping("/validate-symbols")
    @Operation(summary = "Validate symbols against SEC data (dry-run)",
               description = "Validates all symbols in the database against official SEC ticker data. " +
                           "This is a safe operation that only reports what would be cleaned up without making changes.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Validation completed successfully",
            content = @Content(schema = @Schema(implementation = ValidationResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<ValidationResponse>> validateSymbolsDryRun(
            @Parameter(description = "Force refresh of SEC data from remote source")
            @RequestParam(defaultValue = "false") boolean forceRefresh) {

        try {
            logger.info("Starting symbol validation (dry-run) - forceRefresh: {}", forceRefresh);

            ValidationResponse response = symbolValidationService.validateSymbols(true, forceRefresh);

            logger.info("Symbol validation (dry-run) completed: {}", response.getSummary());
            return ResponseEntity.ok(ApiResponse.success("Symbol validation completed", response));

        } catch (Exception e) {
            logger.error("Error during symbol validation (dry-run)", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Symbol validation failed: " + e.getMessage()));
        }
    }

    /**
     * Validate and clean up symbols against SEC data.
     * This endpoint performs actual cleanup operations and removes invalid symbols.
     *
     * @param request Validation request with options
     * @return Validation results with cleanup summary
     */
    @PostMapping("/validate-symbols")
    @Operation(summary = "Validate and clean up symbols against SEC data",
               description = "Validates all symbols in the database against official SEC ticker data. " +
                           "Can perform actual cleanup (dryRun=false) or just report (dryRun=true). " +
                           "CAUTION: When dryRun=false, this will permanently delete invalid symbols and their data.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Validation/cleanup completed successfully",
            content = @Content(schema = @Schema(implementation = ValidationResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<ValidationResponse>> validateAndCleanupSymbols(
            @Parameter(description = "Validation request with dryRun and forceRefresh options", required = true)
            @RequestBody ValidationRequest request) {

        try {
            if (request == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Request body is required"));
            }

            logger.info("Starting symbol validation - dryRun: {}, forceRefresh: {}",
                       request.isDryRun(), request.isForceRefresh());

            // Log warning for actual cleanup operations
            if (!request.isDryRun()) {
                logger.warn("PERFORMING ACTUAL CLEANUP - This will permanently delete invalid symbols and their data");
            }

            ValidationResponse response = symbolValidationService.validateSymbols(
                    request.isDryRun(),
                    request.isForceRefresh()
            );

            String operation = request.isDryRun() ? "validation (dry-run)" : "cleanup";
            logger.info("Symbol {} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    "Symbol " + operation + " completed",
                    response
            ));

        } catch (Exception e) {
            logger.error("Error during symbol validation/cleanup", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Symbol validation/cleanup failed: " + e.getMessage()));
        }
    }

    /**
     * Get SEC data cache status for monitoring and debugging.
     *
     * @return Cache status information
     */
    @GetMapping("/sec-cache-status")
    @Operation(summary = "Get SEC data cache status",
               description = "Returns information about the SEC data cache including last update time and cache size")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Cache status retrieved successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSecCacheStatus() {
        try {
            Map<String, Object> cacheStatus = symbolValidationService.getCacheStatus();
            return ResponseEntity.ok(ApiResponse.success("Cache status retrieved", cacheStatus));

        } catch (Exception e) {
            logger.error("Error retrieving SEC cache status", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve cache status: " + e.getMessage()));
        }
    }

    /**
     * Synchronize SEC data with instruments database (dry-run mode).
     * This endpoint identifies missing instruments without making any changes to the database.
     *
     * @param forceRefresh Whether to force refresh of SEC data from remote source
     * @param maxInstruments Maximum number of instruments to process in one operation
     * @return Synchronization results showing what would be added
     */
    @GetMapping("/sync-sec-data")
    @Operation(summary = "Synchronize SEC data with database (dry-run)",
               description = "Identifies instruments from SEC data that are missing from our database. " +
                           "This is a safe operation that only reports what would be added without making changes.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Synchronization analysis completed successfully",
            content = @Content(schema = @Schema(implementation = SyncResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<SyncResponse>> syncSecDataDryRun(
            @Parameter(description = "Force refresh of SEC data from remote source")
            @RequestParam(defaultValue = "false") boolean forceRefresh,
            @Parameter(description = "Maximum number of instruments to process")
            @RequestParam(defaultValue = "1000") int maxInstruments) {

        try {
            logger.info("Starting SEC synchronization (dry-run) - forceRefresh: {}, maxInstruments: {}",
                       forceRefresh, maxInstruments);

            SyncResponse response = secSynchronizationService.synchronizeInstruments(
                    true, forceRefresh, maxInstruments);

            logger.info("SEC synchronization (dry-run) completed: {}", response.getSummary());
            return ResponseEntity.ok(ApiResponse.success("SEC synchronization analysis completed", response));

        } catch (Exception e) {
            logger.error("Error during SEC synchronization (dry-run)", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("SEC synchronization analysis failed: " + e.getMessage()));
        }
    }

    /**
     * Synchronize SEC data with instruments database.
     * This endpoint can perform actual synchronization operations and add missing instruments.
     *
     * @param request Synchronization request with options
     * @return Synchronization results with summary
     */
    @PostMapping("/sync-sec-data")
    @Operation(summary = "Synchronize SEC data with database",
               description = "Synchronizes SEC company ticker data with our instruments database. " +
                           "Can perform actual synchronization (dryRun=false) or just report (dryRun=true). " +
                           "CAUTION: When dryRun=false, this will add new instruments to the database.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Synchronization completed successfully",
            content = @Content(schema = @Schema(implementation = SyncResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<SyncResponse>> syncSecData(
            @Parameter(description = "Synchronization request with dryRun, forceRefresh, and maxInstruments options", required = true)
            @RequestBody SyncRequest request) {

        try {
            if (request == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Request body is required"));
            }

            logger.info("Starting SEC synchronization - dryRun: {}, forceRefresh: {}, maxInstruments: {}",
                       request.isDryRun(), request.isForceRefresh(), request.getMaxInstruments());

            // Log warning for actual synchronization operations
            if (!request.isDryRun()) {
                logger.warn("PERFORMING ACTUAL SYNCHRONIZATION - This will add new instruments to the database");
            }

            SyncResponse response = secSynchronizationService.synchronizeInstruments(
                    request.isDryRun(),
                    request.isForceRefresh(),
                    request.getMaxInstruments()
            );

            String operation = request.isDryRun() ? "synchronization (dry-run)" : "synchronization";
            logger.info("SEC {} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    "SEC " + operation + " completed",
                    response
            ));

        } catch (Exception e) {
            logger.error("Error during SEC synchronization", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("SEC synchronization failed: " + e.getMessage()));
        }
    }

    /**
     * Upload and process a CSV file containing instrument data.
     * This endpoint allows uploading a CSV file with financial instrument data and importing it into the database.
     *
     * @param file The CSV file to upload
     * @param dryRun Whether to perform a dry run (no actual insertions)
     * @param maxInstruments Maximum number of instruments to process
     * @param skipDuplicates Whether to skip instruments that already exist
     * @param validateData Whether to perform data validation
     * @return CSV processing results with summary
     */
    @PostMapping(value = "/upload-csv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Upload and process CSV file with instrument data",
               description = "Uploads a CSV file containing financial instrument data and imports it into the database. " +
                           "The CSV file must have headers: Symbol, Name, Last Sale, Net Change, % Change, Market Cap, Country, IPO Year, Volume, Sector, Industry. " +
                           "Can perform actual import (dryRun=false) or just validate and report (dryRun=true). " +
                           "CAUTION: When dryRun=false, this will add/update instruments in the database.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "CSV processing completed successfully",
            content = @Content(schema = @Schema(implementation = CsvUploadResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid file or request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<CsvUploadResponse>> uploadCsv(
            @Parameter(description = "CSV file containing instrument data", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "Perform dry run without actual database changes")
            @RequestParam(defaultValue = "true") boolean dryRun,
            @Parameter(description = "Maximum number of instruments to process")
            @RequestParam(defaultValue = "1000") int maxInstruments,
            @Parameter(description = "Skip instruments that already exist in database")
            @RequestParam(defaultValue = "true") boolean skipDuplicates,
            @Parameter(description = "Perform data validation on CSV content")
            @RequestParam(defaultValue = "true") boolean validateData) {

        try {
            if (file == null || file.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("File is required and cannot be empty"));
            }

            logger.info("Starting CSV upload processing - file: {}, size: {} bytes, dryRun: {}, maxInstruments: {}, skipDuplicates: {}, validateData: {}",
                       file.getOriginalFilename(), file.getSize(), dryRun, maxInstruments, skipDuplicates, validateData);

            // Log warning for actual import operations
            if (!dryRun) {
                logger.warn("PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database");
            }

            CsvUploadResponse response = csvInstrumentService.processCsvFile(
                    file, dryRun, maxInstruments, skipDuplicates, validateData);

            String operation = dryRun ? "CSV validation" : "CSV import";
            logger.info("{} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed",
                    response
            ));

        } catch (Exception e) {
            logger.error("Error during CSV processing", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("CSV processing failed: " + e.getMessage()));
        }
    }
}
