package com.investment.database;

import com.investment.model.Instrument;
import com.investment.model.InstrumentType;
import com.investment.model.OHLCV;
import org.eclipse.collections.impl.list.mutable.primitive.DoubleArrayList;
import java.io.File;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DatabaseManager {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseManager.class);
    private static String DB_URL = "************************************";
    private static final String DB_USER = "sa";
    private static final String DB_PASSWORD = "";
    private static final int CURRENT_SCHEMA_VERSION = 10;

    private Connection connection;

    // For testing purposes
    public static void setDbUrl(String dbUrl) {
        DB_URL = dbUrl;
    }

    public void initDatabase() {
        try {
            // Ensure the data directory exists
            ensureDataDirectoryExists();

            // Establish connection
            connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            logger.info("Connected to the database");

            // Create tables if they don't exist
            createTables();

            // Run migrations if needed
            runMigrations();
        } catch (SQLException e) {
            logger.error("Error initializing database", e);
            throw new RuntimeException("Failed to initialize database", e);
        }
    }

    private void ensureDataDirectoryExists() {
        // Extract the directory path from the DB_URL
        String dbPath = DB_URL.replace("jdbc:duckdb:", "");
        // Remove the file name to get the directory
        int lastSlashIndex = dbPath.lastIndexOf('/');
        if (lastSlashIndex > 0) {
            String dirPath = dbPath.substring(0, lastSlashIndex);
            // Create the directory if it doesn't exist
            File dataDir = new File(dirPath);
            if (!dataDir.exists()) {
                boolean created = dataDir.mkdirs();
                if (created) {
                    logger.info("Created data directory: {}", dirPath);
                } else {
                    logger.warn("Failed to create data directory: {}", dirPath);
                }
            }
        }
    }

    private void createTables() throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            // Create schema version table first
            stmt.execute(
                "CREATE TABLE IF NOT EXISTS schema_version (" +
                "version INTEGER PRIMARY KEY, " +
                "applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")"
            );

            // Create instruments table with new schema
            stmt.execute(
                "CREATE TABLE IF NOT EXISTS instruments (" +
                "symbol VARCHAR(20) PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "instrument_type VARCHAR(20) NOT NULL, " +
                "market_cap DECIMAL(20,2), " +
                "country VARCHAR(50), " +
                "ipo_year INTEGER, " +
                "sector VARCHAR(100), " +
                "industry VARCHAR(100), " +
                "created_at TIMESTAMP, " +
                "updated_at TIMESTAMP" +
                ")"
            );

            // Create OHLCV data table
            stmt.execute(
                "CREATE TABLE IF NOT EXISTS ohlcv (" +
                "symbol VARCHAR(20) NOT NULL, " +
                "date DATE NOT NULL, " +
                "open DECIMAL(20,6) NOT NULL, " +
                "high DECIMAL(20,6) NOT NULL, " +
                "low DECIMAL(20,6) NOT NULL, " +
                "close DECIMAL(20,6) NOT NULL, " +
                "volume BIGINT NOT NULL, " +
                "bb_middle_band DECIMAL(20,6), " +
                "bb_std_dev DECIMAL(20,6), " +
                "bb_upper_band DECIMAL(20,6), " +
                "bb_lower_band DECIMAL(20,6), " +
                "dmi_plus_di DECIMAL(20,6), " +
                "dmi_minus_di DECIMAL(20,6), " +
                "dmi_dx DECIMAL(20,6), " +
                "dmi_adx DECIMAL(20,6), " +
                "PRIMARY KEY (symbol, date), " +
                "FOREIGN KEY (symbol) REFERENCES instruments(symbol)" +
                ")"
            );

            logger.info("Database tables created or already exist");
        }
    }

    private void runMigrations() throws SQLException {
        int currentVersion = getCurrentSchemaVersion();
        logger.info("Current schema version: {}", currentVersion);

        if (currentVersion < CURRENT_SCHEMA_VERSION) {
            logger.info("Running migrations from version {} to {}", currentVersion, CURRENT_SCHEMA_VERSION);

            if (currentVersion < 1) {
                migrateToVersion1();
            }
            if (currentVersion < 2) {
                migrateToVersion2();
            }
            if (currentVersion < 3) {
                migrateToVersion3();
            }
            if (currentVersion < 4) {
                migrateToVersion4();
            }
            if (currentVersion < 5) {
                migrateToVersion5();
            }
            if (currentVersion < 6) {
                migrateToVersion6();
            }
            if (currentVersion < 7) {
                migrateToVersion7();
            }
            if (currentVersion < 8) {
                migrateToVersion8();
            }
            if (currentVersion < 9) {
                migrateToVersion9();
            }
            if (currentVersion < 10) {
                migrateToVersion10();
            }

            updateSchemaVersion(CURRENT_SCHEMA_VERSION);
            logger.info("Migrations completed successfully");
        }
    }

    private int getCurrentSchemaVersion() throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            // Check if schema_version table exists and has data
            try (ResultSet rs = stmt.executeQuery("SELECT MAX(version) as version FROM schema_version")) {
                if (rs.next()) {
                    Integer version = rs.getObject("version", Integer.class);
                    return version != null ? version : 0;
                }
            } catch (SQLException e) {
                // Table might not exist yet, return 0
                return 0;
            }
        }
        return 0;
    }

    private void migrateToVersion1() throws SQLException {
        logger.info("Migrating to schema version 1");
        // Version 1 is the base schema - no migration needed
        updateSchemaVersion(1);
    }

    private void migrateToVersion2() throws SQLException {
        logger.info("Migrating to schema version 2 - updating instruments table");

        try (Statement stmt = connection.createStatement()) {
            // Check if we need to migrate from old schema
            boolean hasOldSchema = false;
            try (ResultSet rs = stmt.executeQuery("SELECT column_name FROM information_schema.columns WHERE table_name = 'instruments' AND column_name = 'type'")) {
                if (rs.next()) {
                    hasOldSchema = true;
                }
            } catch (SQLException e) {
                // If information_schema doesn't work, try a different approach
                try (ResultSet rs2 = stmt.executeQuery("SELECT type FROM instruments LIMIT 0")) {
                    hasOldSchema = true; // If this succeeds, old schema exists
                } catch (SQLException e2) {
                    // Column doesn't exist, so we have new schema or no data
                    hasOldSchema = false;
                }
            }

            if (hasOldSchema) {
                logger.info("Detected old schema, performing migration");

                // Step 1: Drop foreign key constraint by dropping ohlcv table temporarily
                boolean ohlcvExists = false;
                try (ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as count FROM ohlcv LIMIT 1")) {
                    ohlcvExists = true;
                } catch (SQLException e) {
                    // Table doesn't exist
                }

                List<Map<String, Object>> ohlcvData = new ArrayList<>();
                if (ohlcvExists) {
                    // Backup OHLCV data
                    try (ResultSet rs = stmt.executeQuery("SELECT * FROM ohlcv")) {
                        while (rs.next()) {
                            Map<String, Object> row = new HashMap<>();
                            row.put("symbol", rs.getString("symbol"));
                            row.put("date", rs.getDate("date"));
                            row.put("open", rs.getDouble("open"));
                            row.put("high", rs.getDouble("high"));
                            row.put("low", rs.getDouble("low"));
                            row.put("close", rs.getDouble("close"));
                            row.put("volume", rs.getLong("volume"));
                            ohlcvData.add(row);
                        }
                    }

                    // Drop OHLCV table to remove foreign key constraint
                    stmt.execute("DROP TABLE ohlcv");
                }

                // Step 2: Create new instruments table with updated schema
                // Drop any existing temp table first
                try {
                    stmt.execute("DROP TABLE IF EXISTS instruments_new");
                } catch (SQLException e) {
                    // Ignore if table doesn't exist
                }

                stmt.execute(
                    "CREATE TABLE instruments_new (" +
                    "symbol VARCHAR(20) PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "instrument_type VARCHAR(20) NOT NULL, " +
                    "market_cap DECIMAL(20,2), " +
                    "country VARCHAR(50), " +
                    "ipo_year INTEGER, " +
                    "sector VARCHAR(100), " +
                    "industry VARCHAR(100), " +
                    "created_at TIMESTAMP, " +
                    "updated_at TIMESTAMP" +
                    ")"
                );

                // Step 3: Copy data from old table to new table
                stmt.execute(
                    "INSERT INTO instruments_new (symbol, name, instrument_type, created_at, updated_at) " +
                    "SELECT symbol, name, type, created_at, updated_at FROM instruments"
                );

                // Step 4: Drop old table and rename new table
                stmt.execute("DROP TABLE instruments");
                stmt.execute("ALTER TABLE instruments_new RENAME TO instruments");

                // Step 5: Recreate OHLCV table with foreign key
                if (ohlcvExists) {
                    stmt.execute(
                        "CREATE TABLE ohlcv (" +
                        "symbol VARCHAR(20) NOT NULL, " +
                        "date DATE NOT NULL, " +
                        "open DECIMAL(20,6) NOT NULL, " +
                        "high DECIMAL(20,6) NOT NULL, " +
                        "low DECIMAL(20,6) NOT NULL, " +
                        "close DECIMAL(20,6) NOT NULL, " +
                        "volume BIGINT NOT NULL, " +
                        "bb_middle_band DECIMAL(20,6), " +
                        "bb_std_dev DECIMAL(20,6), " +
                        "bb_upper_band DECIMAL(20,6), " +
                        "bb_lower_band DECIMAL(20,6), " +
                        "PRIMARY KEY (symbol, date), " +
                        "FOREIGN KEY (symbol) REFERENCES instruments(symbol)" +
                        ")"
                    );

                    // Restore OHLCV data (without Bollinger Band data since it didn't exist in old schema)
                    for (Map<String, Object> row : ohlcvData) {
                        try (PreparedStatement pstmt = connection.prepareStatement(
                            "INSERT INTO ohlcv (symbol, date, open, high, low, close, volume, bb_middle_band, bb_std_dev, bb_upper_band, bb_lower_band) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")) {
                            pstmt.setString(1, (String) row.get("symbol"));
                            pstmt.setDate(2, (Date) row.get("date"));
                            pstmt.setDouble(3, (Double) row.get("open"));
                            pstmt.setDouble(4, (Double) row.get("high"));
                            pstmt.setDouble(5, (Double) row.get("low"));
                            pstmt.setDouble(6, (Double) row.get("close"));
                            pstmt.setLong(7, (Long) row.get("volume"));
                            // Set Bollinger Band columns to NULL for old data
                            pstmt.setNull(8, java.sql.Types.DECIMAL);
                            pstmt.setNull(9, java.sql.Types.DECIMAL);
                            pstmt.setNull(10, java.sql.Types.DECIMAL);
                            pstmt.setNull(11, java.sql.Types.DECIMAL);
                            pstmt.executeUpdate();
                        }
                    }
                }

                logger.info("Schema migration completed successfully");
            } else {
                logger.info("Schema already up to date");
            }
        }

        updateSchemaVersion(2);
    }

    private void migrateToVersion3() throws SQLException {
        logger.info("Migrating to schema version 3 - adding Bollinger Band columns to OHLCV table");

        // Check if Bollinger Band columns already exist
        boolean columnsExist = false;
        try (Statement checkStmt = connection.createStatement();
             ResultSet rs = checkStmt.executeQuery("SELECT bb_middle_band FROM ohlcv LIMIT 0")) {
            columnsExist = true; // If this succeeds, columns already exist
        } catch (SQLException e) {
            // Columns don't exist, we need to add them
            columnsExist = false;
        }

        if (!columnsExist) {
            // Add Bollinger Band columns to existing OHLCV table
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN bb_middle_band DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN bb_std_dev DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN bb_upper_band DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN bb_lower_band DECIMAL(20,6)");

                logger.info("Successfully added Bollinger Band columns to OHLCV table");
            }
        } else {
            logger.info("Bollinger Band columns already exist in OHLCV table");
        }

        updateSchemaVersion(3);
    }

    private void migrateToVersion4() throws SQLException {
        logger.info("Migrating to schema version 4 - adding DMI columns to OHLCV table");

        // Check if DMI columns already exist
        boolean columnsExist = false;
        try (Statement checkStmt = connection.createStatement();
             ResultSet rs = checkStmt.executeQuery("SELECT dmi_plus_di FROM ohlcv LIMIT 0")) {
            columnsExist = true; // If this succeeds, columns already exist
        } catch (SQLException e) {
            // Columns don't exist, we need to add them
            columnsExist = false;
        }

        if (!columnsExist) {
            // Add DMI columns to existing OHLCV table
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_plus_di DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_minus_di DECIMAL(20,6)");
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_adx DECIMAL(20,6)");

                logger.info("Successfully added DMI columns to OHLCV table");
            }
        } else {
            logger.info("DMI columns already exist in OHLCV table");
        }

        updateSchemaVersion(4);
    }

    private void migrateToVersion5() throws SQLException {
        logger.info("Migrating to schema version 5 - adding dmi_dx column to OHLCV table");

        // Check if dmi_dx column already exists
        boolean columnExists = false;
        try (Statement checkStmt = connection.createStatement();
             ResultSet rs = checkStmt.executeQuery("SELECT dmi_dx FROM ohlcv LIMIT 0")) {
            columnExists = true; // If this succeeds, column already exists
        } catch (SQLException e) {
            // Column doesn't exist, we need to add it
            columnExists = false;
        }

        if (!columnExists) {
            // Add dmi_dx column to existing OHLCV table
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_dx DECIMAL(20,6)");
                logger.info("Successfully added dmi_dx column to OHLCV table");
            }
        } else {
            logger.info("dmi_dx column already exists in OHLCV table");
        }

        updateSchemaVersion(5);
    }

    private void migrateToVersion6() throws SQLException {
        logger.info("Migrating to schema version 6 - creating positions table");

        try (Statement stmt = connection.createStatement()) {
            // Create positions table
            stmt.execute(
                "CREATE TABLE IF NOT EXISTS positions (" +
                "id BIGINT PRIMARY KEY, " +
                "symbol VARCHAR(20) NOT NULL, " +
                "position DECIMAL(20,6) NOT NULL, " +
                "side VARCHAR(4) NOT NULL CHECK (side IN ('BUY', 'SELL')), " +
                "status VARCHAR(6) NOT NULL CHECK (status IN ('OPEN', 'CLOSED')), " +
                "trade_price DECIMAL(20,6) NOT NULL, " +
                "trade_value DECIMAL(20,6) NOT NULL, " +
                "init_portfolio_net_value DECIMAL(20,6), " +
                "last_price DECIMAL(20,6), " +
                "last_value DECIMAL(20,6), " +
                "risk_unit DECIMAL(20,6), " +
                "stop_percent DECIMAL(10,6), " +
                "highest_after_trade DECIMAL(20,6), " +
                "stop_value_from_highest DECIMAL(20,6), " +
                "last_bbmb DECIMAL(20,6), " +
                "bbmb_adj_percent DECIMAL(10,6), " +
                "stop_value_from_bbmb DECIMAL(20,6), " +
                "expand_or_contract VARCHAR(11) CHECK (expand_or_contract IN ('EXPANDING', 'CONTRACTING')), " +
                "effective_stop_value DECIMAL(20,6), " +
                "pnl_value DECIMAL(20,6), " +
                "pnl_percent DECIMAL(10,6), " +
                "created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (symbol) REFERENCES instruments(symbol)" +
                ")"
            );

            // Create sequence for position IDs
            stmt.execute("CREATE SEQUENCE IF NOT EXISTS positions_id_seq START 1");

            // Create indexes for frequently queried fields
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_positions_side ON positions(side)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_positions_symbol_status ON positions(symbol, status)");

            logger.info("Positions table and indexes created successfully");
        }

        updateSchemaVersion(6);
    }

    private void migrateToVersion7() throws SQLException {
        logger.info("Migrating to schema version 7 - creating watch_list table");

        try (Statement stmt = connection.createStatement()) {
            // Create watch_list table
            stmt.execute(
                "CREATE TABLE IF NOT EXISTS watch_list (" +
                "id BIGINT PRIMARY KEY, " +
                "display_index INTEGER NOT NULL, " +
                "symbol VARCHAR(20) NOT NULL, " +
                "start_date DATE NOT NULL, " +
                "remarks VARCHAR(128), " +
                "one_mo_perf DECIMAL(10,6), " +
                "three_mo_perf DECIMAL(10,6), " +
                "six_mo_perf DECIMAL(10,6), " +
                "bullish_bb_streak INTEGER DEFAULT 0, " +
                "dmi_bullish_streak INTEGER DEFAULT 0, " +
                "combined_signal_streak INTEGER DEFAULT 0, " +
                "created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (symbol) REFERENCES instruments(symbol)" +
                ")"
            );

            // Create sequence for watch list IDs
            stmt.execute("CREATE SEQUENCE IF NOT EXISTS watch_list_id_seq START 1");

            // Create indexes for frequently queried fields
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_watch_list_symbol ON watch_list(symbol)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_watch_list_display_index ON watch_list(display_index)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_watch_list_start_date ON watch_list(start_date)");

            // Create unique constraint to prevent duplicate symbols
            stmt.execute("CREATE UNIQUE INDEX IF NOT EXISTS idx_watch_list_symbol_unique ON watch_list(symbol)");

            logger.info("Watch list table and indexes created successfully");
        }

        updateSchemaVersion(7);
    }

    private void migrateToVersion8() throws SQLException {
        logger.info("Migrating to schema version 8 - adding technical signal columns to watch_list table");

        try (Statement stmt = connection.createStatement()) {
            // Check if columns already exist (in case migration is re-run)
            boolean needsMigration = false;
            try (ResultSet rs = stmt.executeQuery("PRAGMA table_info(watch_list)")) {
                boolean hasBullishBbStreak = false;
                boolean hasDmiBullishStreak = false;
                boolean hasCombinedSignalStreak = false;

                while (rs.next()) {
                    String columnName = rs.getString("name");
                    if ("bullish_bb_streak".equals(columnName)) {
                        hasBullishBbStreak = true;
                    } else if ("dmi_bullish_streak".equals(columnName)) {
                        hasDmiBullishStreak = true;
                    } else if ("combined_signal_streak".equals(columnName)) {
                        hasCombinedSignalStreak = true;
                    }
                }

                needsMigration = !hasBullishBbStreak || !hasDmiBullishStreak || !hasCombinedSignalStreak;
            } catch (SQLException e) {
                // DuckDB doesn't support PRAGMA, use alternative approach
                needsMigration = true;
                try {
                    stmt.executeQuery("SELECT bullish_bb_streak FROM watch_list LIMIT 1");
                    needsMigration = false;
                } catch (SQLException ignored) {
                    // Column doesn't exist, migration needed
                }
            }

            if (needsMigration) {
                logger.info("Adding technical signal columns to watch_list table");

                // Add the new columns
                try {
                    stmt.execute("ALTER TABLE watch_list ADD COLUMN bullish_bb_streak INTEGER DEFAULT 0");
                    logger.debug("Added bullish_bb_streak column");
                } catch (SQLException e) {
                    if (!e.getMessage().contains("already exists")) {
                        throw e;
                    }
                }

                try {
                    stmt.execute("ALTER TABLE watch_list ADD COLUMN dmi_bullish_streak INTEGER DEFAULT 0");
                    logger.debug("Added dmi_bullish_streak column");
                } catch (SQLException e) {
                    if (!e.getMessage().contains("already exists")) {
                        throw e;
                    }
                }

                try {
                    stmt.execute("ALTER TABLE watch_list ADD COLUMN combined_signal_streak INTEGER DEFAULT 0");
                    logger.debug("Added combined_signal_streak column");
                } catch (SQLException e) {
                    if (!e.getMessage().contains("already exists")) {
                        throw e;
                    }
                }

                logger.info("Technical signal columns added successfully");
            } else {
                logger.info("Technical signal columns already exist, skipping migration");
            }
        }

        updateSchemaVersion(8);
    }

    private void migrateToVersion9() throws SQLException {
        logger.info("Migrating to schema version 9 - adding close_price column to positions table");

        // Check if close_price column already exists
        boolean columnExists = false;
        try (Statement checkStmt = connection.createStatement();
             ResultSet rs = checkStmt.executeQuery("SELECT close_price FROM positions LIMIT 1")) {
            columnExists = true;
        } catch (SQLException e) {
            // Column doesn't exist, migration needed
            logger.debug("close_price column does not exist, will add it");
        }

        if (!columnExists) {
            // Add close_price column to existing positions table
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("ALTER TABLE positions ADD COLUMN close_price DECIMAL(20,6)");
                logger.info("Successfully added close_price column to positions table");
            }
        } else {
            logger.info("close_price column already exists in positions table");
        }

        updateSchemaVersion(9);
    }

    private void migrateToVersion10() throws SQLException {
        logger.info("Migrating to schema version 10 - adding enhanced risk management columns to positions table");

        try (Statement stmt = connection.createStatement()) {
            // Add enhanced risk management columns (without constraints first)
            stmt.execute("ALTER TABLE positions ADD COLUMN IF NOT EXISTS aggressive_stop_percent DECIMAL(10,6)");
            stmt.execute("ALTER TABLE positions ADD COLUMN IF NOT EXISTS conservative_stop_percent DECIMAL(10,6)");
            stmt.execute("ALTER TABLE positions ADD COLUMN IF NOT EXISTS conservative_period_end_date TIMESTAMP");
            stmt.execute("ALTER TABLE positions ADD COLUMN IF NOT EXISTS risk_mode VARCHAR(12)");

            // Note: DuckDB doesn't support adding CHECK constraints via ALTER TABLE
            // The constraint will be enforced at the application level in the Position enum

            logger.info("Successfully added enhanced risk management columns to positions table");
        }

        updateSchemaVersion(10);
    }

    // Position-related database operations

    /**
     * Create a new position in the database.
     */
    public Long createPosition(String symbol, BigDecimal position, String side, String status,
                              BigDecimal tradePrice, BigDecimal tradeValue, BigDecimal initPortfolioNetValue,
                              BigDecimal riskUnit, BigDecimal stopPercent, BigDecimal bbmbAdjPercent) throws SQLException {
        return createPosition(symbol, position, side, status, tradePrice, tradeValue, initPortfolioNetValue,
                             riskUnit, stopPercent, bbmbAdjPercent, null, null, null);
    }

    /**
     * Create a new position in the database with enhanced risk management parameters.
     */
    public Long createPosition(String symbol, BigDecimal position, String side, String status,
                              BigDecimal tradePrice, BigDecimal tradeValue, BigDecimal initPortfolioNetValue,
                              BigDecimal riskUnit, BigDecimal stopPercent, BigDecimal bbmbAdjPercent,
                              BigDecimal aggressiveStopPercent, BigDecimal conservativeStopPercent, String riskMode) throws SQLException {

        // First, get the next ID from the sequence
        Long nextId;
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT nextval('positions_id_seq') as next_id")) {
            if (rs.next()) {
                nextId = rs.getLong("next_id");
            } else {
                throw new SQLException("Failed to get next ID from sequence");
            }
        }

        String sql = """
            INSERT INTO positions (
                id, symbol, position, side, status, trade_price, trade_value,
                init_portfolio_net_value, risk_unit, stop_percent, bbmb_adj_percent,
                aggressive_stop_percent, conservative_stop_percent, risk_mode,
                created_date, updated_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

            stmt.setLong(1, nextId);
            stmt.setString(2, symbol);
            stmt.setBigDecimal(3, position);
            stmt.setString(4, side);
            stmt.setString(5, status);
            stmt.setBigDecimal(6, tradePrice);
            stmt.setBigDecimal(7, tradeValue);
            setBigDecimalOrNull(stmt, 8, initPortfolioNetValue);
            setBigDecimalOrNull(stmt, 9, riskUnit);
            setBigDecimalOrNull(stmt, 10, stopPercent);
            setBigDecimalOrNull(stmt, 11, bbmbAdjPercent);
            setBigDecimalOrNull(stmt, 12, aggressiveStopPercent);
            setBigDecimalOrNull(stmt, 13, conservativeStopPercent);
            setStringOrNull(stmt, 14, riskMode);
            stmt.setTimestamp(15, now);
            stmt.setTimestamp(16, now);

            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating position failed, no rows affected");
            }

            return nextId;
        }
    }

    /**
     * Update a position in the database.
     */
    public void updatePosition(Long id, BigDecimal lastPrice, BigDecimal lastValue, BigDecimal riskUnit,
                              BigDecimal stopPercent, BigDecimal highestAfterTrade, BigDecimal stopValueFromHighest,
                              BigDecimal lastBbmb, BigDecimal bbmbAdjPercent, BigDecimal stopValueFromBbmb,
                              String expandOrContract, BigDecimal effectiveStopValue, BigDecimal pnlValue,
                              BigDecimal pnlPercent, String status, BigDecimal closePrice) throws SQLException {
        updatePosition(id, lastPrice, lastValue, riskUnit, stopPercent, highestAfterTrade, stopValueFromHighest,
                      lastBbmb, bbmbAdjPercent, stopValueFromBbmb, expandOrContract, effectiveStopValue,
                      pnlValue, pnlPercent, status, closePrice, null, null, null, null);
    }

    /**
     * Update a position in the database with enhanced risk management parameters.
     */
    public void updatePosition(Long id, BigDecimal lastPrice, BigDecimal lastValue, BigDecimal riskUnit,
                              BigDecimal stopPercent, BigDecimal highestAfterTrade, BigDecimal stopValueFromHighest,
                              BigDecimal lastBbmb, BigDecimal bbmbAdjPercent, BigDecimal stopValueFromBbmb,
                              String expandOrContract, BigDecimal effectiveStopValue, BigDecimal pnlValue,
                              BigDecimal pnlPercent, String status, BigDecimal closePrice,
                              BigDecimal aggressiveStopPercent, BigDecimal conservativeStopPercent,
                              String riskMode, java.sql.Timestamp conservativePeriodEndDate) throws SQLException {
        String sql = """
            UPDATE positions SET
                last_price = ?, last_value = ?, risk_unit = ?, stop_percent = ?,
                highest_after_trade = ?, stop_value_from_highest = ?, last_bbmb = ?,
                bbmb_adj_percent = ?, stop_value_from_bbmb = ?, expand_or_contract = ?,
                effective_stop_value = ?, pnl_value = ?, pnl_percent = ?, status = ?,
                close_price = ?, aggressive_stop_percent = ?, conservative_stop_percent = ?,
                risk_mode = ?, conservative_period_end_date = ?, updated_date = ?
            WHERE id = ?
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

            setBigDecimalOrNull(stmt, 1, lastPrice);
            setBigDecimalOrNull(stmt, 2, lastValue);
            setBigDecimalOrNull(stmt, 3, riskUnit);
            setBigDecimalOrNull(stmt, 4, stopPercent);
            setBigDecimalOrNull(stmt, 5, highestAfterTrade);
            setBigDecimalOrNull(stmt, 6, stopValueFromHighest);
            setBigDecimalOrNull(stmt, 7, lastBbmb);
            setBigDecimalOrNull(stmt, 8, bbmbAdjPercent);
            setBigDecimalOrNull(stmt, 9, stopValueFromBbmb);
            setStringOrNull(stmt, 10, expandOrContract);
            setBigDecimalOrNull(stmt, 11, effectiveStopValue);
            setBigDecimalOrNull(stmt, 12, pnlValue);
            setBigDecimalOrNull(stmt, 13, pnlPercent);
            stmt.setString(14, status);
            setBigDecimalOrNull(stmt, 15, closePrice);
            setBigDecimalOrNull(stmt, 16, aggressiveStopPercent);
            setBigDecimalOrNull(stmt, 17, conservativeStopPercent);
            setStringOrNull(stmt, 18, riskMode);
            if (conservativePeriodEndDate != null) {
                stmt.setTimestamp(19, conservativePeriodEndDate);
            } else {
                stmt.setNull(19, java.sql.Types.TIMESTAMP);
            }
            stmt.setTimestamp(20, now);
            stmt.setLong(21, id);

            stmt.executeUpdate();
        }
    }

    /**
     * Delete a position from the database.
     */
    public boolean deletePosition(Long id) throws SQLException {
        String sql = "DELETE FROM positions WHERE id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setLong(1, id);
            int affectedRows = stmt.executeUpdate();
            return affectedRows > 0;
        }
    }



    /**
     * Helper method to set BigDecimal or null in PreparedStatement.
     */
    private void setBigDecimalOrNull(PreparedStatement stmt, int index, BigDecimal value) throws SQLException {
        if (value != null) {
            stmt.setBigDecimal(index, value);
        } else {
            stmt.setNull(index, Types.DECIMAL);
        }
    }

    /**
     * Helper method to set String or null in PreparedStatement.
     */
    private void setStringOrNull(PreparedStatement stmt, int index, String value) throws SQLException {
        if (value != null) {
            stmt.setString(index, value);
        } else {
            stmt.setNull(index, Types.VARCHAR);
        }
    }

    /**
     * Get a position by ID from the database.
     */
    public Map<String, Object> getPositionById(Long id) throws SQLException {
        String sql = """
            SELECT id, symbol, position, side, status, trade_price, trade_value,
                   init_portfolio_net_value, last_price, last_value, risk_unit, stop_percent,
                   highest_after_trade, stop_value_from_highest, last_bbmb, bbmb_adj_percent,
                   stop_value_from_bbmb, expand_or_contract, effective_stop_value,
                   pnl_value, pnl_percent, close_price, aggressive_stop_percent,
                   conservative_stop_percent, risk_mode, conservative_period_end_date,
                   created_date, updated_date
            FROM positions WHERE id = ?
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setLong(1, id);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("id", rs.getLong("id"));
                    result.put("symbol", rs.getString("symbol"));
                    result.put("position", rs.getBigDecimal("position"));
                    result.put("side", rs.getString("side"));
                    result.put("status", rs.getString("status"));
                    result.put("trade_price", rs.getBigDecimal("trade_price"));
                    result.put("trade_value", rs.getBigDecimal("trade_value"));
                    result.put("init_portfolio_net_value", rs.getBigDecimal("init_portfolio_net_value"));
                    result.put("last_price", rs.getBigDecimal("last_price"));
                    result.put("last_value", rs.getBigDecimal("last_value"));
                    result.put("risk_unit", rs.getBigDecimal("risk_unit"));
                    result.put("stop_percent", rs.getBigDecimal("stop_percent"));
                    result.put("highest_after_trade", rs.getBigDecimal("highest_after_trade"));
                    result.put("stop_value_from_highest", rs.getBigDecimal("stop_value_from_highest"));
                    result.put("last_bbmb", rs.getBigDecimal("last_bbmb"));
                    result.put("bbmb_adj_percent", rs.getBigDecimal("bbmb_adj_percent"));
                    result.put("stop_value_from_bbmb", rs.getBigDecimal("stop_value_from_bbmb"));
                    result.put("expand_or_contract", rs.getString("expand_or_contract"));
                    result.put("effective_stop_value", rs.getBigDecimal("effective_stop_value"));
                    result.put("pnl_value", rs.getBigDecimal("pnl_value"));
                    result.put("pnl_percent", rs.getBigDecimal("pnl_percent"));
                    result.put("close_price", rs.getBigDecimal("close_price"));
                    result.put("aggressive_stop_percent", rs.getBigDecimal("aggressive_stop_percent"));
                    result.put("conservative_stop_percent", rs.getBigDecimal("conservative_stop_percent"));
                    result.put("risk_mode", rs.getString("risk_mode"));
                    result.put("conservative_period_end_date", rs.getTimestamp("conservative_period_end_date"));
                    result.put("created_date", rs.getTimestamp("created_date"));
                    result.put("updated_date", rs.getTimestamp("updated_date"));
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * Get positions with optional filtering.
     */
    public List<Map<String, Object>> getPositions(String symbol, String status, String side) throws SQLException {
        StringBuilder sql = new StringBuilder("""
            SELECT id, symbol, position, side, status, trade_price, trade_value,
                   init_portfolio_net_value, last_price, last_value, risk_unit, stop_percent,
                   highest_after_trade, stop_value_from_highest, last_bbmb, bbmb_adj_percent,
                   stop_value_from_bbmb, expand_or_contract, effective_stop_value,
                   pnl_value, pnl_percent, close_price, aggressive_stop_percent,
                   conservative_stop_percent, risk_mode, conservative_period_end_date,
                   created_date, updated_date
            FROM positions WHERE 1=1
            """);

        List<Object> parameters = new ArrayList<>();

        if (symbol != null && !symbol.trim().isEmpty()) {
            sql.append(" AND symbol = ?");
            parameters.add(symbol.trim().toUpperCase());
        }

        if (status != null) {
            sql.append(" AND status = ?");
            parameters.add(status);
        }

        if (side != null) {
            sql.append(" AND side = ?");
            parameters.add(side);
        }

        sql.append(" ORDER BY created_date DESC");

        List<Map<String, Object>> positions = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
            for (int i = 0; i < parameters.size(); i++) {
                stmt.setObject(i + 1, parameters.get(i));
            }

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("id", rs.getLong("id"));
                    result.put("symbol", rs.getString("symbol"));
                    result.put("position", rs.getBigDecimal("position"));
                    result.put("side", rs.getString("side"));
                    result.put("status", rs.getString("status"));
                    result.put("trade_price", rs.getBigDecimal("trade_price"));
                    result.put("trade_value", rs.getBigDecimal("trade_value"));
                    result.put("init_portfolio_net_value", rs.getBigDecimal("init_portfolio_net_value"));
                    result.put("last_price", rs.getBigDecimal("last_price"));
                    result.put("last_value", rs.getBigDecimal("last_value"));
                    result.put("risk_unit", rs.getBigDecimal("risk_unit"));
                    result.put("stop_percent", rs.getBigDecimal("stop_percent"));
                    result.put("highest_after_trade", rs.getBigDecimal("highest_after_trade"));
                    result.put("stop_value_from_highest", rs.getBigDecimal("stop_value_from_highest"));
                    result.put("last_bbmb", rs.getBigDecimal("last_bbmb"));
                    result.put("bbmb_adj_percent", rs.getBigDecimal("bbmb_adj_percent"));
                    result.put("stop_value_from_bbmb", rs.getBigDecimal("stop_value_from_bbmb"));
                    result.put("expand_or_contract", rs.getString("expand_or_contract"));
                    result.put("effective_stop_value", rs.getBigDecimal("effective_stop_value"));
                    result.put("pnl_value", rs.getBigDecimal("pnl_value"));
                    result.put("pnl_percent", rs.getBigDecimal("pnl_percent"));
                    result.put("close_price", rs.getBigDecimal("close_price"));
                    result.put("aggressive_stop_percent", rs.getBigDecimal("aggressive_stop_percent"));
                    result.put("conservative_stop_percent", rs.getBigDecimal("conservative_stop_percent"));
                    result.put("risk_mode", rs.getString("risk_mode"));
                    result.put("conservative_period_end_date", rs.getTimestamp("conservative_period_end_date"));
                    result.put("created_date", rs.getTimestamp("created_date"));
                    result.put("updated_date", rs.getTimestamp("updated_date"));
                    positions.add(result);
                }
            }
        }

        return positions;
    }

    // Watch list-related database operations

    /**
     * Create a new watch list item in the database.
     */
    public Long createWatchListItem(Integer displayIndex, String symbol, java.sql.Date startDate, String remarks) throws SQLException {

        // First, get the next ID from the sequence
        Long nextId;
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT nextval('watch_list_id_seq') as next_id")) {
            if (rs.next()) {
                nextId = rs.getLong("next_id");
            } else {
                throw new SQLException("Failed to get next ID from sequence");
            }
        }

        String sql = """
            INSERT INTO watch_list (
                id, display_index, symbol, start_date, remarks, created_date, updated_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

            stmt.setLong(1, nextId);
            stmt.setInt(2, displayIndex);
            stmt.setString(3, symbol);
            stmt.setDate(4, startDate);
            setStringOrNull(stmt, 5, remarks);
            stmt.setTimestamp(6, now);
            stmt.setTimestamp(7, now);

            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating watch list item failed, no rows affected");
            }

            return nextId;
        }
    }

    /**
     * Update a watch list item in the database.
     */
    public void updateWatchListItem(Long id, Integer displayIndex, String remarks,
                                   BigDecimal oneMonthPerf, BigDecimal threeMonthPerf, BigDecimal sixMonthPerf) throws SQLException {
        String sql = """
            UPDATE watch_list SET
                display_index = ?, remarks = ?, one_mo_perf = ?, three_mo_perf = ?, six_mo_perf = ?, updated_date = ?
            WHERE id = ?
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

            stmt.setInt(1, displayIndex);
            setStringOrNull(stmt, 2, remarks);
            setBigDecimalOrNull(stmt, 3, oneMonthPerf);
            setBigDecimalOrNull(stmt, 4, threeMonthPerf);
            setBigDecimalOrNull(stmt, 5, sixMonthPerf);
            stmt.setTimestamp(6, now);
            stmt.setLong(7, id);

            stmt.executeUpdate();
        }
    }

    /**
     * Update performance metrics for a watch list item.
     */
    public void updateWatchListPerformance(Long id, BigDecimal oneMonthPerf, BigDecimal threeMonthPerf, BigDecimal sixMonthPerf) throws SQLException {
        String sql = """
            UPDATE watch_list SET
                one_mo_perf = ?, three_mo_perf = ?, six_mo_perf = ?, updated_date = ?
            WHERE id = ?
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

            setBigDecimalOrNull(stmt, 1, oneMonthPerf);
            setBigDecimalOrNull(stmt, 2, threeMonthPerf);
            setBigDecimalOrNull(stmt, 3, sixMonthPerf);
            stmt.setTimestamp(4, now);
            stmt.setLong(5, id);

            stmt.executeUpdate();
        }
    }

    /**
     * Update technical signal streaks for a watch list item.
     */
    public void updateWatchListTechnicalSignals(Long id, Integer bullishBbStreak, Integer dmiBullishStreak, Integer combinedSignalStreak) throws SQLException {
        String sql = """
            UPDATE watch_list SET
                bullish_bb_streak = ?, dmi_bullish_streak = ?, combined_signal_streak = ?, updated_date = ?
            WHERE id = ?
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

            stmt.setInt(1, bullishBbStreak != null ? bullishBbStreak : 0);
            stmt.setInt(2, dmiBullishStreak != null ? dmiBullishStreak : 0);
            stmt.setInt(3, combinedSignalStreak != null ? combinedSignalStreak : 0);
            stmt.setTimestamp(4, now);
            stmt.setLong(5, id);

            stmt.executeUpdate();
        }
    }

    /**
     * Delete a watch list item from the database.
     */
    public boolean deleteWatchListItem(Long id) throws SQLException {
        String sql = "DELETE FROM watch_list WHERE id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setLong(1, id);
            int affectedRows = stmt.executeUpdate();
            return affectedRows > 0;
        }
    }

    /**
     * Check if a symbol already exists in the watch list.
     */
    public boolean symbolExistsInWatchList(String symbol) throws SQLException {
        String sql = "SELECT 1 FROM watch_list WHERE symbol = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, symbol.toUpperCase());

            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    /**
     * Get a watch list item by ID from the database.
     */
    public Map<String, Object> getWatchListItemById(Long id) throws SQLException {
        String sql = """
            SELECT id, display_index, symbol, start_date, remarks,
                   one_mo_perf, three_mo_perf, six_mo_perf,
                   bullish_bb_streak, dmi_bullish_streak, combined_signal_streak,
                   created_date, updated_date
            FROM watch_list WHERE id = ?
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setLong(1, id);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("id", rs.getLong("id"));
                    result.put("display_index", rs.getInt("display_index"));
                    result.put("symbol", rs.getString("symbol"));
                    result.put("start_date", rs.getDate("start_date"));
                    result.put("remarks", rs.getString("remarks"));
                    result.put("one_mo_perf", rs.getBigDecimal("one_mo_perf"));
                    result.put("three_mo_perf", rs.getBigDecimal("three_mo_perf"));
                    result.put("six_mo_perf", rs.getBigDecimal("six_mo_perf"));
                    result.put("bullish_bb_streak", rs.getInt("bullish_bb_streak"));
                    result.put("dmi_bullish_streak", rs.getInt("dmi_bullish_streak"));
                    result.put("combined_signal_streak", rs.getInt("combined_signal_streak"));
                    result.put("created_date", rs.getTimestamp("created_date"));
                    result.put("updated_date", rs.getTimestamp("updated_date"));
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * Get all watch list items ordered by display_index.
     */
    public List<Map<String, Object>> getWatchListItems() throws SQLException {
        String sql = """
            SELECT id, display_index, symbol, start_date, remarks,
                   one_mo_perf, three_mo_perf, six_mo_perf,
                   bullish_bb_streak, dmi_bullish_streak, combined_signal_streak,
                   created_date, updated_date
            FROM watch_list ORDER BY display_index ASC
            """;

        List<Map<String, Object>> items = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                Map<String, Object> result = new HashMap<>();
                result.put("id", rs.getLong("id"));
                result.put("display_index", rs.getInt("display_index"));
                result.put("symbol", rs.getString("symbol"));
                result.put("start_date", rs.getDate("start_date"));
                result.put("remarks", rs.getString("remarks"));
                result.put("one_mo_perf", rs.getBigDecimal("one_mo_perf"));
                result.put("three_mo_perf", rs.getBigDecimal("three_mo_perf"));
                result.put("six_mo_perf", rs.getBigDecimal("six_mo_perf"));
                result.put("bullish_bb_streak", rs.getInt("bullish_bb_streak"));
                result.put("dmi_bullish_streak", rs.getInt("dmi_bullish_streak"));
                result.put("combined_signal_streak", rs.getInt("combined_signal_streak"));
                result.put("created_date", rs.getTimestamp("created_date"));
                result.put("updated_date", rs.getTimestamp("updated_date"));
                items.add(result);
            }
        }

        return items;
    }

    /**
     * Bulk update display_index values for reordering.
     */
    public void updateWatchListDisplayIndexes(Map<Long, Integer> idToIndexMap) throws SQLException {
        String sql = "UPDATE watch_list SET display_index = ?, updated_date = ? WHERE id = ?";

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

                for (Map.Entry<Long, Integer> entry : idToIndexMap.entrySet()) {
                    stmt.setInt(1, entry.getValue());
                    stmt.setTimestamp(2, now);
                    stmt.setLong(3, entry.getKey());
                    stmt.addBatch();
                }

                stmt.executeBatch();
                connection.commit();
            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }
        } catch (SQLException e) {
            throw e;
        }
    }

    private void updateSchemaVersion(int version) throws SQLException {
        String sql = "INSERT INTO schema_version (version) VALUES (?) ON CONFLICT (version) DO NOTHING";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setInt(1, version);
            pstmt.executeUpdate();
        }
    }

    public void saveInstrument(String symbol, String name, String type) {
        // Backward compatibility method - maps to new schema
        saveInstrumentWithDetails(symbol, name, type, null, null, null, null, null);
    }

    /**
     * Save instrument only if it doesn't already exist in the database.
     * This method preserves existing instrument data and only creates new records.
     * Used during OHLCV updates to avoid overwriting existing instrument metadata.
     *
     * @param symbol The instrument symbol
     * @param name The instrument name
     * @param type The instrument type
     * @return true if a new instrument was created, false if it already existed
     */
    public boolean saveInstrumentIfNotExists(String symbol, String name, String type) {
        // First check if the instrument already exists
        if (symbolExists(symbol)) {
            logger.debug("Instrument {} already exists, skipping save to preserve existing data", symbol);
            return false;
        }

        // Only save if it doesn't exist
        logger.info("Creating new instrument record for symbol: {}", symbol);
        saveInstrumentWithDetails(symbol, name, type, null, null, null, null, null);
        return true;
    }

    public void saveInstrumentWithDetails(String symbol, String name, String instrumentType,
                                        BigDecimal marketCap, String country, Integer ipoYear,
                                        String sector, String industry) {
        String sql = "INSERT INTO instruments (symbol, name, instrument_type, market_cap, country, ipo_year, sector, industry, created_at, updated_at) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                     "ON CONFLICT (symbol) DO UPDATE SET " +
                     "name = EXCLUDED.name, instrument_type = EXCLUDED.instrument_type, " +
                     "market_cap = EXCLUDED.market_cap, country = EXCLUDED.country, " +
                     "ipo_year = EXCLUDED.ipo_year, sector = EXCLUDED.sector, " +
                     "industry = EXCLUDED.industry, updated_at = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());
            pstmt.setString(1, symbol);
            pstmt.setString(2, name);
            pstmt.setString(3, instrumentType);

            if (marketCap != null) {
                pstmt.setBigDecimal(4, marketCap);
            } else {
                pstmt.setNull(4, Types.DECIMAL);
            }

            pstmt.setString(5, country);

            if (ipoYear != null) {
                pstmt.setInt(6, ipoYear);
            } else {
                pstmt.setNull(6, Types.INTEGER);
            }

            pstmt.setString(7, sector);
            pstmt.setString(8, industry);
            pstmt.setTimestamp(9, now); // created_at
            pstmt.setTimestamp(10, now); // updated_at
            pstmt.setTimestamp(11, now); // updated_at for UPDATE

            pstmt.executeUpdate();
        } catch (SQLException e) {
            logger.error("Error saving instrument: {}", symbol, e);
            throw new RuntimeException("Failed to save instrument", e);
        }
    }

    /**
     * Update an existing instrument with new details.
     * This method allows updating all fields of an instrument, including the symbol itself.
     *
     * @param currentSymbol The current symbol of the instrument to update
     * @param newSymbol The new symbol (can be the same as current)
     * @param name The new name
     * @param instrumentType The new instrument type
     * @param marketCap The new market cap
     * @param country The new country
     * @param ipoYear The new IPO year
     * @param sector The new sector
     * @param industry The new industry
     * @throws SQLException if the update fails
     */
    public void updateInstrument(String currentSymbol, String newSymbol, String name, String instrumentType,
                               BigDecimal marketCap, String country, Integer ipoYear,
                               String sector, String industry) throws SQLException {

        // If symbol is changing, we need to update related data as well
        if (!currentSymbol.equals(newSymbol)) {
            updateInstrumentWithSymbolChange(currentSymbol, newSymbol, name, instrumentType,
                                           marketCap, country, ipoYear, sector, industry);
        } else {
            updateInstrumentSameSymbol(currentSymbol, name, instrumentType,
                                     marketCap, country, ipoYear, sector, industry);
        }
    }

    /**
     * Update instrument when symbol is not changing.
     */
    private void updateInstrumentSameSymbol(String symbol, String name, String instrumentType,
                                          BigDecimal marketCap, String country, Integer ipoYear,
                                          String sector, String industry) throws SQLException {
        String sql = "UPDATE instruments SET " +
                     "name = ?, instrument_type = ?, market_cap = ?, country = ?, " +
                     "ipo_year = ?, sector = ?, industry = ?, updated_at = ? " +
                     "WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

            pstmt.setString(1, name);
            pstmt.setString(2, instrumentType);
            if (marketCap != null) {
                pstmt.setBigDecimal(3, marketCap);
            } else {
                pstmt.setNull(3, Types.DECIMAL);
            }
            pstmt.setString(4, country);
            if (ipoYear != null) {
                pstmt.setInt(5, ipoYear);
            } else {
                pstmt.setNull(5, Types.INTEGER);
            }
            pstmt.setString(6, sector);
            pstmt.setString(7, industry);
            pstmt.setTimestamp(8, now);
            pstmt.setString(9, symbol);

            int updatedRows = pstmt.executeUpdate();
            if (updatedRows == 0) {
                throw new SQLException("No instrument found with symbol: " + symbol);
            }

            logger.debug("Updated instrument: {}", symbol);

        } catch (SQLException e) {
            logger.error("Error updating instrument: {}", symbol, e);
            throw e;
        }
    }

    /**
     * Update instrument when symbol is changing - requires updating related data.
     */
    private void updateInstrumentWithSymbolChange(String currentSymbol, String newSymbol, String name, String instrumentType,
                                                BigDecimal marketCap, String country, Integer ipoYear,
                                                String sector, String industry) throws SQLException {
        try {
            connection.setAutoCommit(false);

            // Update the instrument record
            String updateInstrumentSql = "UPDATE instruments SET " +
                                       "symbol = ?, name = ?, instrument_type = ?, market_cap = ?, country = ?, " +
                                       "ipo_year = ?, sector = ?, industry = ?, updated_at = ? " +
                                       "WHERE symbol = ?";

            try (PreparedStatement pstmt = connection.prepareStatement(updateInstrumentSql)) {
                java.sql.Timestamp now = new java.sql.Timestamp(System.currentTimeMillis());

                pstmt.setString(1, newSymbol);
                pstmt.setString(2, name);
                pstmt.setString(3, instrumentType);
                if (marketCap != null) {
                    pstmt.setBigDecimal(4, marketCap);
                } else {
                    pstmt.setNull(4, Types.DECIMAL);
                }
                pstmt.setString(5, country);
                if (ipoYear != null) {
                    pstmt.setInt(6, ipoYear);
                } else {
                    pstmt.setNull(6, Types.INTEGER);
                }
                pstmt.setString(7, sector);
                pstmt.setString(8, industry);
                pstmt.setTimestamp(9, now);
                pstmt.setString(10, currentSymbol);

                int updatedRows = pstmt.executeUpdate();
                if (updatedRows == 0) {
                    throw new SQLException("No instrument found with symbol: " + currentSymbol);
                }
            }

            // Update related OHLCV data
            String updateOhlcvSql = "UPDATE ohlcv SET symbol = ? WHERE symbol = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(updateOhlcvSql)) {
                pstmt.setString(1, newSymbol);
                pstmt.setString(2, currentSymbol);
                int updatedOhlcvRows = pstmt.executeUpdate();
                logger.debug("Updated {} OHLCV records for symbol change: {} -> {}",
                           updatedOhlcvRows, currentSymbol, newSymbol);
            }

            // Update related positions data if exists
            String updatePositionsSql = "UPDATE positions SET symbol = ? WHERE symbol = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(updatePositionsSql)) {
                pstmt.setString(1, newSymbol);
                pstmt.setString(2, currentSymbol);
                int updatedPositionsRows = pstmt.executeUpdate();
                if (updatedPositionsRows > 0) {
                    logger.debug("Updated {} positions records for symbol change: {} -> {}",
                               updatedPositionsRows, currentSymbol, newSymbol);
                }
            }

            // Update related watchlist data if exists
            String updateWatchlistSql = "UPDATE watch_list SET symbol = ? WHERE symbol = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(updateWatchlistSql)) {
                pstmt.setString(1, newSymbol);
                pstmt.setString(2, currentSymbol);
                int updatedWatchlistRows = pstmt.executeUpdate();
                if (updatedWatchlistRows > 0) {
                    logger.debug("Updated {} watchlist records for symbol change: {} -> {}",
                               updatedWatchlistRows, currentSymbol, newSymbol);
                }
            }

            connection.commit();
            logger.info("Successfully updated instrument and related data: {} -> {}", currentSymbol, newSymbol);

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                logger.error("Error rolling back transaction", rollbackEx);
            }
            logger.error("Error updating instrument with symbol change: {} -> {}", currentSymbol, newSymbol, e);
            throw e;
        } finally {
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                logger.error("Error resetting auto-commit", e);
            }
        }
    }

    /**
     * Delete an instrument and all its associated data.
     * Uses separate transactions for each child table deletion to ensure proper
     * foreign key constraint handling in DuckDB.
     *
     * @param symbol The symbol of the instrument to delete
     * @return Summary of the deletion operation
     * @throws SQLException if the deletion fails
     */
    public String deleteInstrumentAndData(String symbol) throws SQLException {
        logger.info("Deleting instrument and all associated data: {}", symbol);

        // Check if instrument exists
        if (!symbolExists(symbol)) {
            throw new IllegalArgumentException("Instrument not found: " + symbol);
        }

        // Count records before deletion for summary
        int ohlcvRecords = countOhlcvRecords(symbol);
        int positionsRecords = countPositionsRecords(symbol);
        int watchlistRecords = countWatchlistRecords(symbol);

        logger.info("Found {} OHLCV records, {} positions, {} watchlist items for symbol: {}",
                   ohlcvRecords, positionsRecords, watchlistRecords, symbol);

        try {
            // Delete child records in separate transactions to ensure proper foreign key handling
            logger.debug("Deleting all related data for symbol: {}", symbol);

            // Delete positions in separate transaction
            deleteChildRecordsInSeparateTransaction(symbol, "positions", "DELETE FROM positions WHERE symbol = ?");

            // Delete watchlist items in separate transaction
            deleteChildRecordsInSeparateTransaction(symbol, "watch_list", "DELETE FROM watch_list WHERE symbol = ?");

            // Delete OHLCV data in separate transaction
            deleteChildRecordsInSeparateTransaction(symbol, "ohlcv", "DELETE FROM ohlcv WHERE symbol = ?");

            // Verify that all child records have been deleted before deleting parent
            logger.debug("Verifying all child records are deleted for symbol: {}", symbol);
            validateNoForeignKeyReferences(symbol);

            // Finally delete the parent record in its own transaction
            deleteInstrumentInSeparateTransaction(symbol);

            String summary = String.format("Deleted instrument '%s' and associated data: %d OHLCV records, %d positions, %d watchlist items",
                                          symbol, ohlcvRecords, positionsRecords, watchlistRecords);

            logger.info(summary);
            return summary;

        } catch (SQLException e) {
            logger.error("Error deleting instrument and data: {}", symbol, e);
            throw e;
        }
    }

    /**
     * Delete child records in a separate transaction to ensure proper foreign key handling.
     */
    private void deleteChildRecordsInSeparateTransaction(String symbol, String tableName, String deleteSql) throws SQLException {
        boolean originalAutoCommit = connection.getAutoCommit();

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(deleteSql)) {
                pstmt.setString(1, symbol);
                int deletedRecords = pstmt.executeUpdate();
                logger.debug("Deleted {} {} records for symbol: {}", deletedRecords, tableName, symbol);
            }

            connection.commit();
            logger.debug("Committed deletion of {} records for symbol: {}", tableName, symbol);

        } catch (SQLException e) {
            try {
                connection.rollback();
                logger.debug("Rolled back deletion of {} records for symbol: {}", tableName, symbol);
            } catch (SQLException rollbackEx) {
                logger.error("Error rolling back {} deletion transaction", tableName, rollbackEx);
            }
            throw new SQLException("Failed to delete " + tableName + " records for symbol: " + symbol, e);
        } finally {
            try {
                connection.setAutoCommit(originalAutoCommit);
            } catch (SQLException e) {
                logger.error("Error resetting auto-commit after {} deletion", tableName, e);
            }
        }
    }

    /**
     * Delete the instrument record in a separate transaction.
     */
    private void deleteInstrumentInSeparateTransaction(String symbol) throws SQLException {
        boolean originalAutoCommit = connection.getAutoCommit();

        try {
            connection.setAutoCommit(false);

            String deleteInstrumentsSql = "DELETE FROM instruments WHERE symbol = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteInstrumentsSql)) {
                pstmt.setString(1, symbol);
                int deletedInstruments = pstmt.executeUpdate();
                logger.debug("Deleted {} instrument records for symbol: {}", deletedInstruments, symbol);

                if (deletedInstruments == 0) {
                    throw new SQLException("No instrument found with symbol: " + symbol);
                }
            }

            connection.commit();
            logger.debug("Committed deletion of instrument record for symbol: {}", symbol);

        } catch (SQLException e) {
            try {
                connection.rollback();
                logger.debug("Rolled back deletion of instrument record for symbol: {}", symbol);
            } catch (SQLException rollbackEx) {
                logger.error("Error rolling back instrument deletion transaction", rollbackEx);
            }
            throw new SQLException("Failed to delete instrument record for symbol: " + symbol, e);
        } finally {
            try {
                connection.setAutoCommit(originalAutoCommit);
            } catch (SQLException e) {
                logger.error("Error resetting auto-commit after instrument deletion", e);
            }
        }
    }

    // Helper methods for deletion
    private int countPositionsRecords(String symbol) throws SQLException {
        String sql = "SELECT COUNT(*) FROM positions WHERE symbol = ?";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next() ? rs.getInt(1) : 0;
            }
        }
    }

    private int countWatchlistRecords(String symbol) throws SQLException {
        String sql = "SELECT COUNT(*) FROM watch_list WHERE symbol = ?";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next() ? rs.getInt(1) : 0;
            }
        }
    }



    /**
     * Check for any remaining foreign key references before deletion.
     * This helps identify which table still has references to the symbol.
     * Enhanced to check for any unknown foreign key references.
     */
    private void validateNoForeignKeyReferences(String symbol) throws SQLException {
        int remainingPositions = countPositionsRecords(symbol);
        int remainingWatchlist = countWatchlistRecords(symbol);
        int remainingOhlcv = countOhlcvRecords(symbol);

        if (remainingPositions > 0 || remainingWatchlist > 0 || remainingOhlcv > 0) {
            String errorMsg = String.format(
                "Foreign key constraint violation: Symbol '%s' still has references - " +
                "Positions: %d, Watchlist: %d, OHLCV: %d",
                symbol, remainingPositions, remainingWatchlist, remainingOhlcv
            );
            logger.error(errorMsg);
            throw new SQLException(errorMsg);
        }

        // Additional check: Try to identify any other tables that might have foreign key references
        // This is a defensive measure to catch any tables we might have missed
        try {
            checkForUnknownForeignKeyReferences(symbol);
        } catch (SQLException e) {
            logger.warn("Could not perform comprehensive foreign key check: {}", e.getMessage());
            // Don't fail the operation for this, but log the warning
        }
    }

    /**
     * Attempt to identify any unknown foreign key references by querying database metadata.
     * This is a best-effort check to catch any tables we might have missed.
     */
    private void checkForUnknownForeignKeyReferences(String symbol) throws SQLException {
        // Query all tables to see if any contain the symbol
        // This is a heuristic approach since DuckDB's metadata support is limited
        String[] knownTables = {"positions", "watch_list", "ohlcv"};

        try (Statement stmt = connection.createStatement()) {
            // Get list of all tables
            try (ResultSet tables = stmt.executeQuery("SHOW TABLES")) {
                while (tables.next()) {
                    String tableName = tables.getString(1);

                    // Skip known tables and system tables
                    if (tableName.equals("instruments") ||
                        tableName.equals("schema_version") ||
                        java.util.Arrays.asList(knownTables).contains(tableName)) {
                        continue;
                    }

                    // Check if this table has a symbol column that might reference our symbol
                    try {
                        String checkSql = String.format("SELECT COUNT(*) FROM %s WHERE symbol = ?", tableName);
                        try (PreparedStatement pstmt = connection.prepareStatement(checkSql)) {
                            pstmt.setString(1, symbol);
                            try (ResultSet rs = pstmt.executeQuery()) {
                                if (rs.next() && rs.getInt(1) > 0) {
                                    logger.warn("Found {} records in unexpected table '{}' for symbol: {}",
                                              rs.getInt(1), tableName, symbol);
                                }
                            }
                        }
                    } catch (SQLException e) {
                        // Table might not have a symbol column, which is fine
                        logger.debug("Table '{}' does not have a symbol column or is not accessible", tableName);
                    }
                }
            }
        }
    }

    private void deleteFromPositions(String symbol) throws SQLException {
        String sql = "DELETE FROM positions WHERE symbol = ?";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            int deletedRows = pstmt.executeUpdate();
            logger.debug("Deleted {} position records for symbol: {}", deletedRows, symbol);
        } catch (SQLException e) {
            logger.error("Error deleting positions for symbol: {}", symbol, e);
            throw e;
        }
    }

    private void deleteFromWatchlist(String symbol) throws SQLException {
        String sql = "DELETE FROM watch_list WHERE symbol = ?";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            int deletedRows = pstmt.executeUpdate();
            logger.debug("Deleted {} watchlist records for symbol: {}", deletedRows, symbol);
        } catch (SQLException e) {
            logger.error("Error deleting watchlist items for symbol: {}", symbol, e);
            throw e;
        }
    }

    private void deleteFromOhlcv(String symbol) throws SQLException {
        String sql = "DELETE FROM ohlcv WHERE symbol = ?";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            int deletedRows = pstmt.executeUpdate();
            logger.debug("Deleted {} OHLCV records for symbol: {}", deletedRows, symbol);
        } catch (SQLException e) {
            logger.error("Error deleting OHLCV data for symbol: {}", symbol, e);
            throw e;
        }
    }

    private void deleteFromInstruments(String symbol) throws SQLException {
        String sql = "DELETE FROM instruments WHERE symbol = ?";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            int deletedRows = pstmt.executeUpdate();
            if (deletedRows == 0) {
                throw new SQLException("No instrument found with symbol: " + symbol);
            }
            logger.debug("Deleted {} instrument record for symbol: {}", deletedRows, symbol);
        } catch (SQLException e) {
            logger.error("Error deleting instrument for symbol: {}", symbol, e);
            throw e;
        }
    }

    /**
     * Alternative deletion method that uses CASCADE-like behavior by temporarily
     * disabling foreign key checks. This is a fallback method for cases where
     * the standard deletion approach fails due to DuckDB-specific issues.
     *
     * WARNING: This method should only be used as a last resort and with extreme caution.
     */
    public String deleteInstrumentAndDataWithForcedCascade(String symbol) throws SQLException {
        logger.warn("Using forced cascade deletion for instrument: {}", symbol);

        // Check if instrument exists
        if (!symbolExists(symbol)) {
            throw new IllegalArgumentException("Instrument not found: " + symbol);
        }

        // Count records before deletion for summary
        int ohlcvRecords = countOhlcvRecords(symbol);
        int positionsRecords = countPositionsRecords(symbol);
        int watchlistRecords = countWatchlistRecords(symbol);

        logger.info("Found {} OHLCV records, {} positions, {} watchlist items for symbol: {}",
                   ohlcvRecords, positionsRecords, watchlistRecords, symbol);

        boolean originalAutoCommit = connection.getAutoCommit();

        try {
            connection.setAutoCommit(false);

            // Note: DuckDB doesn't support PRAGMA foreign_keys=OFF like SQLite
            // So we'll use a more aggressive approach with explicit ordering

            logger.debug("Force deleting all related data for symbol: {}", symbol);

            // Delete in reverse dependency order with explicit commits
            String[] deletionQueries = {
                "DELETE FROM positions WHERE symbol = ?",
                "DELETE FROM watch_list WHERE symbol = ?",
                "DELETE FROM ohlcv WHERE symbol = ?",
                "DELETE FROM instruments WHERE symbol = ?"
            };

            for (String query : deletionQueries) {
                try (PreparedStatement pstmt = connection.prepareStatement(query)) {
                    pstmt.setString(1, symbol);
                    int deletedRows = pstmt.executeUpdate();
                    logger.debug("Force deleted {} records with query: {}", deletedRows, query);
                }
            }

            connection.commit();

            String summary = String.format("Force deleted instrument '%s' and associated data: %d OHLCV records, %d positions, %d watchlist items",
                                          symbol, ohlcvRecords, positionsRecords, watchlistRecords);

            logger.info(summary);
            return summary;

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                logger.error("Error rolling back forced cascade transaction", rollbackEx);
            }
            logger.error("Error in forced cascade deletion for symbol: {}", symbol, e);
            throw e;
        } finally {
            try {
                connection.setAutoCommit(originalAutoCommit);
            } catch (SQLException e) {
                logger.error("Error resetting auto-commit after forced cascade deletion", e);
            }
        }
    }

    public void saveOHLCVData(List<OHLCV> dataPoints) {
        if (dataPoints.isEmpty()) {
            return;
        }

        String sql = "INSERT INTO ohlcv (symbol, date, open, high, low, close, volume, bb_middle_band, bb_std_dev, bb_upper_band, bb_lower_band, dmi_plus_di, dmi_minus_di, dmi_dx, dmi_adx) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                     "ON CONFLICT (symbol, date) DO UPDATE SET " +
                     "open = EXCLUDED.open, high = EXCLUDED.high, low = EXCLUDED.low, " +
                     "close = EXCLUDED.close, volume = EXCLUDED.volume, " +
                     "bb_middle_band = EXCLUDED.bb_middle_band, bb_std_dev = EXCLUDED.bb_std_dev, " +
                     "bb_upper_band = EXCLUDED.bb_upper_band, bb_lower_band = EXCLUDED.bb_lower_band, " +
                     "dmi_plus_di = EXCLUDED.dmi_plus_di, dmi_minus_di = EXCLUDED.dmi_minus_di, dmi_dx = EXCLUDED.dmi_dx, dmi_adx = EXCLUDED.dmi_adx";

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
                for (OHLCV data : dataPoints) {
                    pstmt.setString(1, data.getSymbol());
                    pstmt.setDate(2, Date.valueOf(data.getDate()));
                    pstmt.setDouble(3, data.getOpen());
                    pstmt.setDouble(4, data.getHigh());
                    pstmt.setDouble(5, data.getLow());
                    pstmt.setDouble(6, data.getClose());
                    pstmt.setLong(7, data.getVolume());

                    // Handle nullable Bollinger Band values
                    if (data.getBbMiddleBand() != null) {
                        pstmt.setDouble(8, data.getBbMiddleBand());
                    } else {
                        pstmt.setNull(8, java.sql.Types.DECIMAL);
                    }

                    if (data.getBbStdDev() != null) {
                        pstmt.setDouble(9, data.getBbStdDev());
                    } else {
                        pstmt.setNull(9, java.sql.Types.DECIMAL);
                    }

                    if (data.getBbUpperBand() != null) {
                        pstmt.setDouble(10, data.getBbUpperBand());
                    } else {
                        pstmt.setNull(10, java.sql.Types.DECIMAL);
                    }

                    if (data.getBbLowerBand() != null) {
                        pstmt.setDouble(11, data.getBbLowerBand());
                    } else {
                        pstmt.setNull(11, java.sql.Types.DECIMAL);
                    }

                    // Handle nullable DMI values
                    if (data.getDmiPlusDi() != null) {
                        pstmt.setDouble(12, data.getDmiPlusDi());
                    } else {
                        pstmt.setNull(12, java.sql.Types.DECIMAL);
                    }

                    if (data.getDmiMinusDi() != null) {
                        pstmt.setDouble(13, data.getDmiMinusDi());
                    } else {
                        pstmt.setNull(13, java.sql.Types.DECIMAL);
                    }

                    if (data.getDmiDx() != null) {
                        pstmt.setDouble(14, data.getDmiDx());
                    } else {
                        pstmt.setNull(14, java.sql.Types.DECIMAL);
                    }

                    if (data.getDmiAdx() != null) {
                        pstmt.setDouble(15, data.getDmiAdx());
                    } else {
                        pstmt.setNull(15, java.sql.Types.DECIMAL);
                    }

                    pstmt.addBatch();
                }

                int[] results = pstmt.executeBatch();
                connection.commit();

                logger.info("Saved {} OHLCV data points for {}", results.length, dataPoints.get(0).getSymbol());
            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }
        } catch (SQLException e) {
            logger.error("Error saving OHLCV data", e);
            throw new RuntimeException("Failed to save OHLCV data", e);
        }
    }

    public LocalDate getLastDataDate(String symbol) {
        String sql = "SELECT MAX(date) as last_date FROM ohlcv WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Date date = rs.getDate("last_date");
                    return date != null ? date.toLocalDate() : null;
                }
                return null;
            }
        } catch (SQLException e) {
            logger.error("Error getting last data date for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to get last data date", e);
        }
    }

    /**
     * Check if a symbol has recent OHLCV data (within the last 3 days).
     * @param symbol The symbol to check
     * @return true if the symbol has recent data, false otherwise
     */
    public boolean hasRecentData(String symbol) {
        LocalDate lastDataDate = getLastDataDate(symbol);
        if (lastDataDate == null) {
            return false;
        }

        // Consider data recent if it's within the last 3 days
        LocalDate cutoffDate = LocalDate.now();
        //logger.info("cutoffDate: {} lastDataDate: {}", cutoffDate, lastDataDate);
        return lastDataDate.isAfter(cutoffDate) || lastDataDate.isEqual(cutoffDate);
    }

    /**
     * Get the latest OHLCV data for a symbol.
     * @param symbol The symbol to get data for
     * @param limit Maximum number of records to return (most recent first)
     * @return List of OHLCV data maps, ordered by date descending
     */
    public List<Map<String, Object>> getLatestOHLCVData(String symbol, int limit) throws SQLException {
        // Ensure read-after-write consistency by forcing a connection sync
        ensureReadAfterWriteConsistency();

        String sql = "SELECT symbol, date, open, high, low, close, volume " +
                    "FROM ohlcv WHERE symbol = ? ORDER BY date DESC LIMIT ?";

        List<Map<String, Object>> result = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            pstmt.setInt(2, limit);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Map<String, Object> row = new HashMap<>();
                    row.put("symbol", rs.getString("symbol"));
                    row.put("date", rs.getDate("date"));
                    row.put("open", rs.getDouble("open"));
                    row.put("high", rs.getDouble("high"));
                    row.put("low", rs.getDouble("low"));
                    row.put("close", rs.getDouble("close"));
                    row.put("volume", rs.getLong("volume"));
                    result.add(row);
                }
            }
        }

        return result;
    }

    /**
     * Ensure read-after-write consistency by forcing a database sync.
     * This helps with potential connection isolation issues where recent writes
     * might not be immediately visible to subsequent reads.
     */
    private void ensureReadAfterWriteConsistency() throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            // Execute a simple query to ensure the connection sees all committed changes
            stmt.execute("SELECT 1");
        }
    }

    public List<OHLCV> getOHLCVData(String symbol, LocalDate startDate, LocalDate endDate) {
        // Build dynamic SQL based on whether date filtering is needed
        StringBuilder sqlBuilder = new StringBuilder("SELECT * FROM ohlcv WHERE symbol = ?");
        boolean hasDateFilter = false;

        if (startDate != null && endDate != null) {
            sqlBuilder.append(" AND date BETWEEN ? AND ?");
            hasDateFilter = true;
        } else if (startDate != null) {
            sqlBuilder.append(" AND date >= ?");
            hasDateFilter = true;
        } else if (endDate != null) {
            sqlBuilder.append(" AND date <= ?");
            hasDateFilter = true;
        }

        sqlBuilder.append(" ORDER BY date");
        String sql = sqlBuilder.toString();
        List<OHLCV> result = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            // Set date parameters conditionally
            int paramIndex = 2;
            if (startDate != null && endDate != null) {
                pstmt.setDate(paramIndex++, Date.valueOf(startDate));
                pstmt.setDate(paramIndex++, Date.valueOf(endDate));
            } else if (startDate != null) {
                pstmt.setDate(paramIndex++, Date.valueOf(startDate));
            } else if (endDate != null) {
                pstmt.setDate(paramIndex++, Date.valueOf(endDate));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    // Handle nullable Bollinger Band columns - use getDouble and check wasNull()
                    Double bbMiddleBand = null;
                    double bbMiddleValue = rs.getDouble("bb_middle_band");
                    if (!rs.wasNull()) {
                        bbMiddleBand = bbMiddleValue;
                    }

                    Double bbStdDev = null;
                    double bbStdDevValue = rs.getDouble("bb_std_dev");
                    if (!rs.wasNull()) {
                        bbStdDev = bbStdDevValue;
                    }

                    Double bbUpperBand = null;
                    double bbUpperValue = rs.getDouble("bb_upper_band");
                    if (!rs.wasNull()) {
                        bbUpperBand = bbUpperValue;
                    }

                    Double bbLowerBand = null;
                    double bbLowerValue = rs.getDouble("bb_lower_band");
                    if (!rs.wasNull()) {
                        bbLowerBand = bbLowerValue;
                    }

                    // Handle nullable DMI columns
                    Double dmiPlusDi = null;
                    double dmiPlusDiValue = rs.getDouble("dmi_plus_di");
                    if (!rs.wasNull()) {
                        dmiPlusDi = dmiPlusDiValue;
                    }

                    Double dmiMinusDi = null;
                    double dmiMinusDiValue = rs.getDouble("dmi_minus_di");
                    if (!rs.wasNull()) {
                        dmiMinusDi = dmiMinusDiValue;
                    }

                    Double dmiDx = null;
                    double dmiDxValue = rs.getDouble("dmi_dx");
                    if (!rs.wasNull()) {
                        dmiDx = dmiDxValue;
                    }

                    Double dmiAdx = null;
                    double dmiAdxValue = rs.getDouble("dmi_adx");
                    if (!rs.wasNull()) {
                        dmiAdx = dmiAdxValue;
                    }

                    result.add(new OHLCV(
                        rs.getString("symbol"),
                        rs.getDate("date").toLocalDate(),
                        rs.getDouble("open"),
                        rs.getDouble("high"),
                        rs.getDouble("low"),
                        rs.getDouble("close"),
                        rs.getLong("volume"),
                        bbMiddleBand,
                        bbStdDev,
                        bbUpperBand,
                        bbLowerBand,
                        dmiPlusDi,
                        dmiMinusDi,
                        dmiDx,
                        dmiAdx
                    ));
                }
            }
        } catch (SQLException e) {
            logger.error("Error retrieving OHLCV data for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to retrieve OHLCV data", e);
        }

        return result;
    }

    /**
     * Get all symbols from the instruments table.
     * @return List of all symbols in the database
     */
    public List<String> getAllSymbols() {
        String sql = "SELECT symbol FROM instruments ORDER BY symbol";
        List<String> symbols = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                symbols.add(rs.getString("symbol"));
            }
        } catch (SQLException e) {
            logger.error("Error retrieving all symbols", e);
            throw new RuntimeException("Failed to retrieve symbols", e);
        }

        return symbols;
    }

    /**
     * Get unique symbols that actually have OHLCV data.
     * This is more efficient than getAllSymbols() when the instruments table
     * contains more symbols than those with actual price data.
     * @return List of symbols that have OHLCV data, ordered by market cap (descending)
     */
    public List<String> getSymbolsWithOhlcvData() {
        return getSymbolsWithOhlcvData(true);
    }

    /**
     * Get unique symbols that actually have OHLCV data with configurable ordering.
     * @param orderByMarketCap If true, order by market cap (descending), otherwise alphabetically
     * @return List of symbols that have OHLCV data
     */
    public List<String> getSymbolsWithOhlcvData(boolean orderByMarketCap) {
        return getSymbolsWithOhlcvData(orderByMarketCap, 0, Integer.MAX_VALUE);
    }

    /**
     * Get unique symbols that actually have OHLCV data with pagination support.
     * @param orderByMarketCap If true, order by market cap (descending), otherwise alphabetically
     * @param startIndex Starting position (0-based)
     * @param limit Maximum number of symbols to return
     * @return List of symbols that have OHLCV data
     */
    public List<String> getSymbolsWithOhlcvData(boolean orderByMarketCap, int startIndex, int limit) {
        String sql;
        if (orderByMarketCap) {
            // Join with instruments table to get market cap for ordering
            sql = "SELECT DISTINCT o.symbol " +
                  "FROM ohlcv o " +
                  "INNER JOIN instruments i ON o.symbol = i.symbol " +
                  "ORDER BY i.market_cap DESC NULLS LAST, o.symbol " +
                  "LIMIT ? OFFSET ?";
        } else {
            // Original alphabetical ordering
            sql = "SELECT DISTINCT symbol FROM ohlcv ORDER BY symbol LIMIT ? OFFSET ?";
        }

        List<String> symbols = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setInt(1, limit);
            pstmt.setInt(2, startIndex);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    symbols.add(rs.getString("symbol"));
                }
            }

            logger.debug("Retrieved {} unique symbols with OHLCV data (orderByMarketCap: {}, startIndex: {}, limit: {})",
                        symbols.size(), orderByMarketCap, startIndex, limit);
            return symbols;

        } catch (SQLException e) {
            logger.error("Error retrieving symbols with OHLCV data", e);
            throw new RuntimeException("Failed to retrieve symbols with OHLCV data", e);
        }
    }

    /**
     * Get the total count of unique symbols that have OHLCV data.
     * @return Total number of symbols with OHLCV data
     */
    public int getTotalSymbolsWithOhlcvDataCount() {
        String sql = "SELECT COUNT(DISTINCT o.symbol) " +
                     "FROM ohlcv o " +
                     "INNER JOIN instruments i ON o.symbol = i.symbol";

        try (PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            if (rs.next()) {
                int count = rs.getInt(1);
                logger.debug("Total symbols with OHLCV data: {}", count);
                return count;
            }
            return 0;

        } catch (SQLException e) {
            logger.error("Error counting symbols with OHLCV data", e);
            throw new RuntimeException("Failed to count symbols with OHLCV data", e);
        }
    }

    /**
     * Check if a symbol exists in the instruments table.
     * @param symbol The symbol to check
     * @return true if the symbol exists, false otherwise
     */
    public boolean symbolExists(String symbol) {
        String sql = "SELECT 1 FROM instruments WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol.toUpperCase());

            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next();
            }
        } catch (SQLException e) {
            logger.error("Error checking if symbol exists: {}", symbol, e);
            throw new RuntimeException("Failed to check symbol existence", e);
        }
    }

    /**
     * Get all instruments from the database with their details.
     * @return List of all instruments in the database
     */
    public List<Instrument> getAllInstruments() {
        String sql = "SELECT symbol, name, instrument_type, market_cap, country, ipo_year, sector, industry FROM instruments ORDER BY symbol";
        List<Instrument> instruments = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                String symbol = rs.getString("symbol");
                String name = rs.getString("name");
                String instrumentTypeStr = rs.getString("instrument_type");
                BigDecimal marketCap = rs.getBigDecimal("market_cap");
                String country = rs.getString("country");
                Integer ipoYear = rs.getObject("ipo_year", Integer.class);
                String sector = rs.getString("sector");
                String industry = rs.getString("industry");

                // Parse instrument type
                InstrumentType instrumentType;
                try {
                    instrumentType = InstrumentType.valueOf(instrumentTypeStr);
                } catch (IllegalArgumentException e) {
                    logger.warn("Unknown instrument type '{}' for symbol '{}', defaulting to US_STOCK", instrumentTypeStr, symbol);
                    instrumentType = InstrumentType.US_STOCK;
                }

                instruments.add(new Instrument(symbol, name, instrumentType, marketCap, country, ipoYear, sector, industry));
            }
        } catch (SQLException e) {
            logger.error("Error retrieving all instruments", e);
            throw new RuntimeException("Failed to retrieve instruments", e);
        }

        return instruments;
    }

    /**
     * Get all instruments from the database ordered by market cap (descending).
     * Instruments with null market cap are placed at the end.
     * @return List of all instruments ordered by market cap (highest first)
     */
    public List<Instrument> getAllInstrumentsOrderedByMarketCap() {
        return getAllInstrumentsOrderedByMarketCap(0, Integer.MAX_VALUE);
    }

    /**
     * Get instruments from the database ordered by market cap (descending) with pagination.
     * Instruments with null market cap are placed at the end.
     * @param offset Starting position (0-based)
     * @param limit Maximum number of records to return
     * @return List of instruments ordered by market cap (highest first)
     */
    public List<Instrument> getAllInstrumentsOrderedByMarketCap(int offset, int limit) {
        String sql = "SELECT symbol, name, instrument_type, market_cap, country, ipo_year, sector, industry " +
                     "FROM instruments " +
                     "ORDER BY market_cap DESC NULLS LAST, symbol " +
                     "LIMIT ? OFFSET ?";
        List<Instrument> instruments = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setInt(1, limit);
            pstmt.setInt(2, offset);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    String symbol = rs.getString("symbol");
                    String name = rs.getString("name");
                    String instrumentTypeStr = rs.getString("instrument_type");
                    BigDecimal marketCap = rs.getBigDecimal("market_cap");
                    String country = rs.getString("country");
                    Integer ipoYear = rs.getObject("ipo_year", Integer.class);
                    String sector = rs.getString("sector");
                    String industry = rs.getString("industry");

                    // Parse instrument type
                    InstrumentType instrumentType;
                    try {
                        instrumentType = InstrumentType.valueOf(instrumentTypeStr);
                    } catch (IllegalArgumentException e) {
                        logger.warn("Unknown instrument type '{}' for symbol '{}', defaulting to US_STOCK", instrumentTypeStr, symbol);
                        instrumentType = InstrumentType.US_STOCK;
                    }

                    instruments.add(new Instrument(symbol, name, instrumentType, marketCap, country, ipoYear, sector, industry));
                }
            }
        } catch (SQLException e) {
            logger.error("Error retrieving instruments ordered by market cap with pagination (offset: {}, limit: {})", offset, limit, e);
            throw new RuntimeException("Failed to retrieve instruments", e);
        }

        return instruments;
    }

    /**
     * Get the total count of instruments in the database.
     * @return Total number of instruments
     */
    public int getTotalInstrumentCount() {
        String sql = "SELECT COUNT(*) as count FROM instruments";

        try (PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt("count");
            }
            return 0;
        } catch (SQLException e) {
            logger.error("Error counting total instruments", e);
            throw new RuntimeException("Failed to count instruments", e);
        }
    }

    /**
     * Count OHLCV records for a specific symbol.
     * @param symbol The symbol to count records for
     * @return Number of OHLCV records for the symbol
     */
    public int countOhlcvRecords(String symbol) {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
                return 0;
            }
        } catch (SQLException e) {
            logger.error("Error counting OHLCV records for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to count OHLCV records", e);
        }
    }

    /**
     * Delete an instrument and all its associated OHLCV data.
     * This operation is transactional - either both deletions succeed or both fail.
     *
     * @param symbol The symbol to delete
     * @return Number of OHLCV records that were deleted
     */
    public int deleteInstrumentAndOhlcvData(String symbol) {
        int deletedOhlcvRecords = 0;

        try {
            connection.setAutoCommit(false);

            // First, count and delete OHLCV records
            deletedOhlcvRecords = countOhlcvRecords(symbol);
            String deleteOhlcvSql = "DELETE FROM ohlcv WHERE symbol = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteOhlcvSql)) {
                pstmt.setString(1, symbol);
                pstmt.executeUpdate();
            }

            // Then delete the instrument
            String deleteInstrumentSql = "DELETE FROM instruments WHERE symbol = ?";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteInstrumentSql)) {
                pstmt.setString(1, symbol);
                int deletedInstruments = pstmt.executeUpdate();

                if (deletedInstruments == 0) {
                    logger.warn("No instrument found with symbol: {}", symbol);
                }
            }

            connection.commit();
            logger.info("Deleted instrument {} and {} OHLCV records", symbol, deletedOhlcvRecords);

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                logger.error("Error rolling back transaction", rollbackEx);
            }
            logger.error("Error deleting instrument and OHLCV data for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to delete instrument and OHLCV data", e);
        } finally {
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                logger.error("Error resetting auto-commit", e);
            }
        }

        return deletedOhlcvRecords;
    }

    /**
     * Delete multiple instruments and their associated OHLCV data in a single transaction.
     *
     * @param symbols List of symbols to delete
     * @return Total number of OHLCV records that were deleted
     */
    public int deleteInstrumentsAndOhlcvData(List<String> symbols) {
        if (symbols.isEmpty()) {
            return 0;
        }

        int totalDeletedOhlcvRecords = 0;

        try {
            connection.setAutoCommit(false);

            // Count total OHLCV records that will be deleted
            for (String symbol : symbols) {
                totalDeletedOhlcvRecords += countOhlcvRecords(symbol);
            }

            // Create parameterized queries for batch deletion
            String placeholders = String.join(",", symbols.stream().map(s -> "?").toArray(String[]::new));

            // Delete OHLCV records first
            String deleteOhlcvSql = "DELETE FROM ohlcv WHERE symbol IN (" + placeholders + ")";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteOhlcvSql)) {
                for (int i = 0; i < symbols.size(); i++) {
                    pstmt.setString(i + 1, symbols.get(i));
                }
                pstmt.executeUpdate();
            }

            // Then delete instruments
            String deleteInstrumentsSql = "DELETE FROM instruments WHERE symbol IN (" + placeholders + ")";
            try (PreparedStatement pstmt = connection.prepareStatement(deleteInstrumentsSql)) {
                for (int i = 0; i < symbols.size(); i++) {
                    pstmt.setString(i + 1, symbols.get(i));
                }
                int deletedInstruments = pstmt.executeUpdate();
                logger.info("Deleted {} instruments and {} OHLCV records", deletedInstruments, totalDeletedOhlcvRecords);
            }

            connection.commit();

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                logger.error("Error rolling back transaction", rollbackEx);
            }
            logger.error("Error deleting instruments and OHLCV data", e);
            throw new RuntimeException("Failed to delete instruments and OHLCV data", e);
        } finally {
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                logger.error("Error resetting auto-commit", e);
            }
        }

        return totalDeletedOhlcvRecords;
    }

    /**
     * Check if a symbol has existing Bollinger Band data.
     * @param symbol The symbol to check
     * @return true if the symbol has at least one record with Bollinger Band data
     */
    public boolean hasExistingBollingerBandData(String symbol) throws SQLException {
        String sql = "SELECT 1 FROM ohlcv WHERE symbol = ? AND bb_middle_band IS NOT NULL LIMIT 1";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    /**
     * Get the second-to-last date for which Bollinger Band data has been calculated for a symbol.
     * This is used for incremental calculations to reference N-1 instead of N.
     * @param symbol The symbol to check
     * @return The second-to-last date with Bollinger Band data, or null if insufficient data exists
     */
    public LocalDate getLastBollingerBandCalculationDate(String symbol) throws SQLException {
        String sql = "SELECT date FROM ohlcv WHERE symbol = ? AND bb_middle_band IS NOT NULL ORDER BY date DESC LIMIT 2";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                // Skip the first row (most recent date - N)
                if (rs.next() && rs.next()) {
                    // Return the second row (second-to-last date - N-1)
                    Date date = rs.getDate("date");
                    return date != null ? date.toLocalDate() : null;
                }
                return null; // Less than 2 records with Bollinger Band data
            }
        }
    }

    /**
     * Clear existing Bollinger Band data for a symbol.
     * @param symbol The symbol to clear data for
     * @return Number of records cleared
     */
    public int clearBollingerBandData(String symbol) throws SQLException {
        String sql = "UPDATE ohlcv SET bb_middle_band = NULL, bb_std_dev = NULL, bb_upper_band = NULL, bb_lower_band = NULL WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            int clearedRecords = pstmt.executeUpdate();
            logger.debug("Cleared Bollinger Band data for {} records of symbol: {}", clearedRecords, symbol);
            return clearedRecords;
        }
    }

    /**
     * Calculate and update Bollinger Bands incrementally for a specific symbol.
     * Only processes data points from the second-to-last calculated date onwards (N-1 reference point).
     * @param symbol The symbol to calculate for
     * @param period The number of periods for moving average
     * @param stdDevMultiplier The standard deviation multiplier
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateBollingerBandsIncremental(String symbol, int period, double stdDevMultiplier, boolean dryRun) throws SQLException {
        logger.debug("Calculating Bollinger Bands incrementally for symbol: {} (period: {}, stdDev: {}, dryRun: {})",
                    symbol, period, stdDevMultiplier, dryRun);

        // Get the second-to-last date with Bollinger Band data (N-1 reference point)
        LocalDate lastCalculatedDate = getLastBollingerBandCalculationDate(symbol);

        if (lastCalculatedDate == null) {
            // No existing data or insufficient data (less than 2 records), fall back to full calculation
            logger.debug("Insufficient existing Bollinger Band data for symbol: {}, performing full calculation", symbol);
            return calculateAndUpdateBollingerBands(symbol, period, stdDevMultiplier, dryRun);
        }

        logger.debug("Using N-1 reference date: {} for incremental calculation of symbol: {}", lastCalculatedDate, symbol);

        // FIXED: For incremental calculation, we need to use ALL historical data for accurate window functions
        // but only update records from N-1 onwards to maintain calculation accuracy
        logger.debug("Incremental calculation will use complete historical context, updating from: {} for symbol: {}",
                    lastCalculatedDate, symbol);

        if (dryRun) {
            return countRecordsForIncrementalBollingerBandCalculation(symbol, lastCalculatedDate);
        }

        // FIXED: Use DuckDB's window functions with COMPLETE historical context for accurate calculations
        // The key fix is to calculate over ALL historical data but only update records from N-1 onwards
        String updateSql =
            "UPDATE ohlcv SET " +
            "bb_middle_band = calculated.bb_middle_band, " +
            "bb_std_dev = calculated.bb_std_dev, " +
            "bb_upper_band = calculated.bb_upper_band, " +
            "bb_lower_band = calculated.bb_lower_band " +
            "FROM (" +
            "  SELECT " +
            "    symbol, date, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_middle_band, " +
            "    STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_std_dev, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) + ? * STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_upper_band, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) - ? * STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_lower_band " +
            "  FROM ohlcv " +
            "  WHERE symbol = ? " +
            "  ORDER BY date" +
            ") AS calculated " +
            "WHERE ohlcv.symbol = calculated.symbol AND ohlcv.date = calculated.date AND ohlcv.date >= ?";

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(updateSql)) {
                int paramIndex = 1;
                // Set parameters for window functions (8 times for the 4 calculations)
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setDouble(paramIndex++, stdDevMultiplier); // stdDevMultiplier
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setDouble(paramIndex++, stdDevMultiplier); // stdDevMultiplier
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setString(paramIndex++, symbol); // WHERE symbol = ? (fetch ALL historical data)
                pstmt.setDate(paramIndex, Date.valueOf(lastCalculatedDate)); // WHERE date >= ? (update from N-1 onwards, inclusive)

                int updatedRecords = pstmt.executeUpdate();
                connection.commit();

                logger.debug("Updated {} records with incremental Bollinger Band data for symbol: {} (updates from: {})",
                           updatedRecords, symbol, lastCalculatedDate);
                return updatedRecords;

            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }

        } catch (SQLException e) {
            logger.error("Error calculating incremental Bollinger Bands for symbol: {}", symbol, e);
            throw new SQLException("Failed to calculate incremental Bollinger Bands for symbol: " + symbol, e);
        }
    }

    /**
     * Calculate and update Bollinger Bands for a specific symbol using optimized SQL.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for moving average (default: 20)
     * @param stdDevMultiplier The standard deviation multiplier (default: 2.0)
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateBollingerBands(String symbol, int period, double stdDevMultiplier, boolean dryRun) throws SQLException {
        logger.debug("Calculating Bollinger Bands for symbol: {} (period: {}, stdDev: {}, dryRun: {})",
                    symbol, period, stdDevMultiplier, dryRun);

        if (dryRun) {
            // For dry run, just count the records that would be updated
            return countRecordsForBollingerBandCalculation(symbol, period);
        }

        // Use DuckDB's window functions to calculate Bollinger Bands efficiently
        String updateSql =
            "UPDATE ohlcv SET " +
            "bb_middle_band = calculated.bb_middle_band, " +
            "bb_std_dev = calculated.bb_std_dev, " +
            "bb_upper_band = calculated.bb_upper_band, " +
            "bb_lower_band = calculated.bb_lower_band " +
            "FROM (" +
            "  SELECT " +
            "    symbol, date, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_middle_band, " +
            "    STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_std_dev, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) + ? * STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_upper_band, " +
            "    AVG(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) - ? * STDDEV(close) OVER (" +
            "      PARTITION BY symbol " +
            "      ORDER BY date " +
            "      ROWS BETWEEN ? PRECEDING AND CURRENT ROW" +
            "    ) AS bb_lower_band " +
            "  FROM ohlcv " +
            "  WHERE symbol = ? " +
            "  ORDER BY date" +
            ") AS calculated " +
            "WHERE ohlcv.symbol = calculated.symbol AND ohlcv.date = calculated.date";

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(updateSql)) {
                int paramIndex = 1;
                // Set parameters for all the window function calculations
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setDouble(paramIndex++, stdDevMultiplier); // multiplier for upper band
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setDouble(paramIndex++, stdDevMultiplier); // multiplier for lower band
                pstmt.setInt(paramIndex++, period - 1); // ROWS BETWEEN (period-1) PRECEDING
                pstmt.setString(paramIndex, symbol); // WHERE symbol = ?

                int updatedRecords = pstmt.executeUpdate();
                connection.commit();

                logger.debug("Updated {} records with Bollinger Band data for symbol: {}", updatedRecords, symbol);
                return updatedRecords;

            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }

        } catch (SQLException e) {
            logger.error("Error calculating Bollinger Bands for symbol: {}", symbol, e);
            throw new SQLException("Failed to calculate Bollinger Bands for symbol: " + symbol, e);
        }
    }

    /**
     * Count records that would be updated for Bollinger Band calculation (for dry run).
     */
    private int countRecordsForBollingerBandCalculation(String symbol, int period) throws SQLException {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
                return 0;
            }
        }
    }

    /**
     * Count records that would be updated for incremental Bollinger Band calculation (for dry run).
     * Uses N-1 reference point, so counts from the second-to-last calculated date onwards (inclusive).
     */
    private int countRecordsForIncrementalBollingerBandCalculation(String symbol, LocalDate lastCalculatedDate) throws SQLException {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ? AND date >= ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            pstmt.setDate(2, Date.valueOf(lastCalculatedDate)); // Count from N-1 date onwards (inclusive)

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    int count = rs.getInt("count");
                    logger.debug("Dry run count for incremental Bollinger Band calculation: {} records from {} onwards for symbol: {}",
                               count, lastCalculatedDate, symbol);
                    return count;
                }
                return 0;
            }
        }
    }

    /**
     * Check if a symbol has existing DMI data.
     * @param symbol The symbol to check
     * @return true if the symbol has at least one record with DMI data
     */
    public boolean hasExistingDMIData(String symbol) throws SQLException {
        String sql = "SELECT 1 FROM ohlcv WHERE symbol = ? AND dmi_plus_di IS NOT NULL LIMIT 1";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    /**
     * Get the second-to-last date for which DMI data has been calculated for a symbol.
     * This is used for incremental calculations to reference N-1 instead of N.
     * @param symbol The symbol to check
     * @return The second-to-last date with DMI data, or null if insufficient data exists
     */
    public LocalDate getLastDMICalculationDate(String symbol) throws SQLException {
        String sql = "SELECT date FROM ohlcv WHERE symbol = ? AND dmi_plus_di IS NOT NULL ORDER BY date DESC LIMIT 2";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                // Skip the first row (most recent date - N)
                if (rs.next() && rs.next()) {
                    // Return the second row (second-to-last date - N-1)
                    Date date = rs.getDate("date");
                    return date != null ? date.toLocalDate() : null;
                }
                return null; // Less than 2 records with DMI data
            }
        }
    }

    /**
     * Clear existing DMI data for a symbol.
     * @param symbol The symbol to clear data for
     * @return Number of records cleared
     */
    public int clearDMIData(String symbol) throws SQLException {
        String sql = "UPDATE ohlcv SET dmi_plus_di = NULL, dmi_minus_di = NULL, dmi_dx = NULL, dmi_adx = NULL WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            int clearedRecords = pstmt.executeUpdate();
            logger.debug("Cleared DMI data for {} records of symbol: {}", clearedRecords, symbol);
            return clearedRecords;
        }
    }

    /**
     * Calculate and update DMI incrementally for a specific symbol.
     * Only processes data points after the last calculated date.
     * FIXED: Properly handles incremental calculation by preserving smoothed state continuity.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for DMI calculation
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateDMIIncremental(String symbol, int period, boolean dryRun) throws SQLException {
        logger.debug("Calculating DMI incrementally for symbol: {} (period: {}, dryRun: {})",
                    symbol, period, dryRun);

        // Get the second-to-last date with DMI data (N-1 reference point)
        LocalDate lastCalculatedDate = getLastDMICalculationDate(symbol);

        if (lastCalculatedDate == null) {
            // No existing data or insufficient data (less than 2 records), fall back to full calculation
            logger.debug("Insufficient existing DMI data for symbol: {}, performing full calculation", symbol);
            return calculateAndUpdateDMI(symbol, period, dryRun);
        }

        if (dryRun) {
            return countRecordsForIncrementalDMICalculation(symbol, lastCalculatedDate);
        }

        // For incremental calculation, we need to ensure we have enough historical data
        // Check total available data first to avoid false "insufficient data" errors
        int totalRecords = countOhlcvRecords(symbol);
        logger.debug("Symbol {} has {} total OHLCV records for incremental DMI calculation", symbol, totalRecords);

        if (totalRecords < period * 2) {
            logger.debug("Insufficient total data for DMI calculation for symbol: {} (need: {}, have: {})",
                        symbol, period * 2, totalRecords);
            return 0;
        }

        // FIXED: For proper incremental calculation, we need to recalculate from scratch
        // but only update records from the lastCalculatedDate onwards.
        // This ensures smoothed values and ADX calculations maintain proper continuity.
        logger.debug("Performing incremental DMI calculation for symbol: {} from date: {}", symbol, lastCalculatedDate);

        // Calculate DMI for all data but only update from lastCalculatedDate onwards
        return calculateDMIForDateRange(symbol, period, null, lastCalculatedDate);
    }

    /**
     * Calculate and update DMI for a specific symbol using the complete algorithm.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for DMI calculation (default: 14)
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateDMI(String symbol, int period, boolean dryRun) throws SQLException {
        logger.debug("Calculating DMI for symbol: {} (period: {}, dryRun: {})", symbol, period, dryRun);

        if (dryRun) {
            // For dry run, just count the records that would be updated
            return countRecordsForDMICalculation(symbol, period);
        }

        // Calculate DMI for all data
        return calculateDMIForDateRange(symbol, period, null, null);
    }

    /**
     * Calculate and update DMI for a specific symbol using hybrid SQL+Java approach.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for DMI calculation (default: 14)
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateDMIHybrid(String symbol, int period, boolean dryRun) throws SQLException {
        logger.debug("Calculating DMI using hybrid approach for symbol: {} (period: {}, dryRun: {})", symbol, period, dryRun);

        if (dryRun) {
            // For dry run, just count the records that would be updated
            return countRecordsForDMICalculation(symbol, period);
        }

        // Calculate DMI using hybrid SQL+Java approach
        return calculateDMIHybrid(symbol, period, null, null);
    }

    /**
     * Calculate and update DMI incrementally using hybrid SQL+Java approach.
     * Only calculates for new data points since the last calculation.
     * FIXED: Properly handles incremental calculation by preserving smoothed state continuity.
     * @param symbol The symbol to calculate for
     * @param period The number of periods for DMI calculation (default: 14)
     * @param dryRun If true, only count records that would be updated
     * @return Number of records updated (or would be updated in dry run)
     */
    public int calculateAndUpdateDMIHybridIncremental(String symbol, int period, boolean dryRun) throws SQLException {
        logger.debug("Calculating DMI incrementally using hybrid approach for symbol: {} (period: {}, dryRun: {})", symbol, period, dryRun);

        // Get the second-to-last date with DMI data (N-1 reference point)
        LocalDate lastCalculatedDate = getLastDMICalculationDate(symbol);

        if (lastCalculatedDate == null) {
            // No existing data or insufficient data (less than 2 records), fall back to full calculation
            logger.debug("Insufficient existing DMI data for symbol: {}, performing full hybrid calculation", symbol);
            return calculateAndUpdateDMIHybrid(symbol, period, dryRun);
        }

        if (dryRun) {
            return countRecordsForIncrementalDMICalculation(symbol, lastCalculatedDate);
        }

        // For incremental calculation, we need to ensure we have enough historical data
        // Check total available data first to avoid false "insufficient data" errors
        int totalRecords = countOhlcvRecords(symbol);
        logger.debug("Symbol {} has {} total OHLCV records for incremental hybrid DMI calculation", symbol, totalRecords);

        if (totalRecords < period * 2) {
            logger.debug("Insufficient total data for hybrid DMI calculation for symbol: {} (need: {}, have: {})",
                        symbol, period * 2, totalRecords);
            return 0;
        }

        // FIXED: For proper incremental calculation, we need to recalculate from scratch
        // but only update records from the lastCalculatedDate onwards.
        // This ensures smoothed values and ADX calculations maintain proper continuity.
        logger.debug("Performing incremental hybrid DMI calculation for symbol: {} from date: {}", symbol, lastCalculatedDate);

        // Calculate DMI for all data but only update from lastCalculatedDate onwards
        return calculateDMIHybrid(symbol, period, null, lastCalculatedDate);
    }

    /**
     * Calculate DMI for a specific date range or all data if dates are null.
     * Implements the complete DMI algorithm from the provided sample code.
     */
    private int calculateDMIForDateRange(String symbol, int period, LocalDate startDate, LocalDate fromDate) throws SQLException {
        logger.debug("Calculating DMI for symbol: {} with period: {}, startDate: {}, fromDate: {}",
                    symbol, period, startDate, fromDate);

        // Fetch OHLCV data for the symbol
        String fetchSql = "SELECT date, high, low, close FROM ohlcv WHERE symbol = ?";
        if (startDate != null) {
            fetchSql += " AND date >= ?";
        }
        fetchSql += " ORDER BY date";

        logger.debug("DMI data fetch SQL: {}", fetchSql);

        DoubleArrayList highs = new DoubleArrayList(2000);
        DoubleArrayList lows = new DoubleArrayList(2000);
        DoubleArrayList closes = new DoubleArrayList(2000);
        List<LocalDate> dates = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(fetchSql)) {
            pstmt.setString(1, symbol);
            if (startDate != null) {
                pstmt.setDate(2, Date.valueOf(startDate));
                logger.debug("Using startDate filter: {}", startDate);
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    dates.add(rs.getDate("date").toLocalDate());
                    highs.add(rs.getDouble("high"));
                    lows.add(rs.getDouble("low"));
                    closes.add(rs.getDouble("close"));
                }
            }
        }

        logger.debug("Retrieved {} OHLCV records for DMI calculation (symbol: {}, period: {}, need: {})",
                    dates.size(), symbol, period, period * 2);

        if (dates.size() < period * 2) {
            logger.debug("Insufficient data for DMI calculation for symbol: {} (need: {}, have: {}, startDate: {})",
                        symbol, period * 2, dates.size(), startDate);
            return 0;
        }

        // Calculate DMI using the algorithm from the sample code
        return calculateAndStoreDMI(symbol, dates, highs, lows, closes, period, fromDate);
    }

    /**
     * Calculate DMI using hybrid SQL+Java approach for optimal performance.
     * FIXED: Uses SQL for TR, +DM, -DM calculations and Java for smoothing, +DI, -DI, DX, and ADX
     * to ensure exact consistency with the pure Java algorithm.
     */
    private int calculateDMIHybrid(String symbol, int period, LocalDate startDate, LocalDate fromDate) throws SQLException {
        logger.debug("Calculating DMI using hybrid SQL+Java approach for symbol: {} (period: {}, startDate: {}, fromDate: {})",
                    symbol, period, startDate, fromDate);

        // Step 1: Use SQL to calculate TR, +DM, -DM only (no smoothing in SQL)
        String hybridSql = buildHybridDMIQuery(symbol, period, startDate);
        logger.debug("Generated hybrid DMI SQL: {}", hybridSql);

        List<LocalDate> dates = new ArrayList<>();
        DoubleArrayList tr = new DoubleArrayList(2000);
        DoubleArrayList plusDM = new DoubleArrayList(2000);
        DoubleArrayList minusDM = new DoubleArrayList(2000);

        try (PreparedStatement pstmt = connection.prepareStatement(hybridSql)) {
            pstmt.setString(1, symbol);
            if (startDate != null) {
                pstmt.setDate(2, Date.valueOf(startDate));
                logger.debug("Using startDate filter for hybrid DMI: {}", startDate);
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    dates.add(rs.getDate("date").toLocalDate());

                    // FIXED: Use getDouble() and check wasNull() after each call to handle DuckDB properly
                    double trValue = rs.getDouble("tr");
                    boolean trIsNull = rs.wasNull();

                    double plusDMValue = rs.getDouble("plus_dm");
                    boolean plusDMIsNull = rs.wasNull();

                    double minusDMValue = rs.getDouble("minus_dm");
                    boolean minusDMIsNull = rs.wasNull();

                    tr.add(trIsNull ? Double.NaN : trValue);
                    plusDM.add(plusDMIsNull ? Double.NaN : plusDMValue);
                    minusDM.add(minusDMIsNull ? Double.NaN : minusDMValue);
                }
            }
        }

        logger.debug("Retrieved {} records for hybrid DMI calculation (symbol: {}, period: {}, need: {})",
                    dates.size(), symbol, period, period * 2);

        if (dates.size() < period * 2) {
            logger.debug("Insufficient data for hybrid DMI calculation for symbol: {} (need: {}, have: {}, startDate: {})",
                        symbol, period * 2, dates.size(), startDate);
            return 0;
        }

        // Step 2: Apply Java smoothing to match pure Java algorithm exactly
        DoubleArrayList smoothedTR = calculateSmoothedValues(tr, period);
        DoubleArrayList smoothedPlusDM = calculateSmoothedValues(plusDM, period);
        DoubleArrayList smoothedMinusDM = calculateSmoothedValues(minusDM, period);

        // Step 3: Calculate +DI, -DI, and DX using Java (same as pure Java method)
        DoubleArrayList plusDI = new DoubleArrayList(2000);
        DoubleArrayList minusDI = new DoubleArrayList(2000);
        DoubleArrayList dx = new DoubleArrayList(2000);

        for (int i = 0; i < dates.size(); i++) {
            double smoothedTRValue = smoothedTR.get(i);
            if (Double.isNaN(smoothedTRValue) || smoothedTRValue == 0) {
                plusDI.add(Double.NaN);
                minusDI.add(Double.NaN);
                dx.add(Double.NaN);
                continue;
            }

            double pdi = (smoothedPlusDM.get(i) / smoothedTRValue) * 100;
            double mdi = (smoothedMinusDM.get(i) / smoothedTRValue) * 100;
            plusDI.add(pdi);
            minusDI.add(mdi);

            double sumDI = pdi + mdi;
            dx.add(sumDI == 0 ? Double.NaN : (Math.abs(pdi - mdi) / sumDI) * 100);
        }

        // Step 4: Calculate ADX using Java (same as pure Java method)
        logger.debug("Hybrid DMI: About to calculate ADX with {} DX values, period={}", dx.size(), period);
        DoubleArrayList adx = calculateADX(dx, period);

        // Step 5: Store results in database
        return storeDMIResults(symbol, dates, plusDI, minusDI, dx, adx, fromDate);
    }

    /**
     * Build the hybrid DMI SQL query that calculates TR, +DM, -DM only.
     * The smoothing will be done in Java to match the pure Java algorithm exactly.
     * This ensures consistency between hybrid and pure Java approaches.
     */
    String buildHybridDMIQuery(String symbol, int period, LocalDate startDate) {
        // FIXED: Only calculate TR, +DM, -DM in SQL. Smoothing will be done in Java
        // to match the pure Java Wilder's smoothing algorithm exactly.
        String sql = String.format("""
            WITH dmi_base AS (
                SELECT
                    symbol,
                    date,
                    high,
                    low,
                    close,
                    high - LAG(high) OVER (PARTITION BY symbol ORDER BY date) AS up_move,
                    LAG(low) OVER (PARTITION BY symbol ORDER BY date) - low AS down_move,
                    GREATEST(
                        high - low,
                        ABS(high - LAG(close) OVER (PARTITION BY symbol ORDER BY date)),
                        ABS(low - LAG(close) OVER (PARTITION BY symbol ORDER BY date))
                    ) AS tr
                FROM ohlcv
                WHERE symbol = ?%s
            )
            SELECT
                symbol,
                date,
                tr,
                CASE
                    WHEN up_move > down_move AND up_move > 0 THEN up_move
                    ELSE 0
                END AS plus_dm,
                CASE
                    WHEN down_move > up_move AND down_move > 0 THEN down_move
                    ELSE 0
                END AS minus_dm
            FROM dmi_base
            ORDER BY date
            """,
            startDate != null ? " AND date >= ?" : ""
        );

        return sql;
    }



    /**
     * Core DMI calculation algorithm based on the provided sample code.
     * Calculates True Range, +DM, -DM, smoothed values, +DI, -DI, DX, and ADX.
     */
    private int calculateAndStoreDMI(String symbol, List<LocalDate> dates, DoubleArrayList highs,
                                   DoubleArrayList lows, DoubleArrayList closes, int period, LocalDate fromDate) throws SQLException {

        DoubleArrayList tr = new DoubleArrayList(2000);
        DoubleArrayList plusDM = new DoubleArrayList(2000);
        DoubleArrayList minusDM = new DoubleArrayList(2000);

        // Step 1: Calculate TR, +DM, and -DM
        for (int i = 0; i < closes.size(); i++) {
            if (i == 0) {
                tr.add(0.0);
                plusDM.add(0.0);
                minusDM.add(0.0);
                continue;
            }

            double upMove = highs.get(i) - highs.get(i - 1);
            double downMove = lows.get(i - 1) - lows.get(i);
            double trValue = Math.max(highs.get(i) - lows.get(i),
                    Math.max(Math.abs(highs.get(i) - closes.get(i - 1)),
                            Math.abs(lows.get(i) - closes.get(i - 1))));
            double plusDMValue = (upMove > downMove && upMove > 0) ? upMove : 0;
            double minusDMValue = (downMove > upMove && downMove > 0) ? downMove : 0;

            tr.add(trValue);
            plusDM.add(plusDMValue);
            minusDM.add(minusDMValue);
        }

        // Step 2: Calculate Smoothed TR, +DM, -DM
        DoubleArrayList smoothedTR = calculateSmoothedValues(tr, period);
        DoubleArrayList smoothedPlusDM = calculateSmoothedValues(plusDM, period);
        DoubleArrayList smoothedMinusDM = calculateSmoothedValues(minusDM, period);

        // Step 3: Calculate +DI, -DI, and DX
        DoubleArrayList plusDI = new DoubleArrayList(2000);
        DoubleArrayList minusDI = new DoubleArrayList(2000);
        DoubleArrayList dx = new DoubleArrayList(2000);

        for (int i = 0; i < closes.size(); i++) {
            double smoothedTRValue = smoothedTR.get(i);
            if (Double.isNaN(smoothedTRValue) || smoothedTRValue == 0) {
                plusDI.add(Double.NaN);
                minusDI.add(Double.NaN);
                dx.add(Double.NaN);
                continue;
            }

            double pdi = (smoothedPlusDM.get(i) / smoothedTRValue) * 100;
            double mdi = (smoothedMinusDM.get(i) / smoothedTRValue) * 100;
            plusDI.add(pdi);
            minusDI.add(mdi);

            double sumDI = pdi + mdi;
            dx.add(sumDI == 0 ? Double.NaN : (Math.abs(pdi - mdi) / sumDI) * 100);
        }

        // Step 4: Calculate ADX (smoothed DX)
        DoubleArrayList adx = calculateADX(dx, period);

        // Step 5: Store results in database
        return storeDMIResults(symbol, dates, plusDI, minusDI, dx, adx, fromDate);
    }

    /**
     * Calculate smoothed values using the DMI smoothing algorithm.
     * Uses NaN as sentinel value for missing data to maintain garbage-free performance.
     */
    private DoubleArrayList calculateSmoothedValues(DoubleArrayList values, int period) {
        DoubleArrayList smoothed = new DoubleArrayList(2000);

        for (int i = 0; i < values.size(); i++) {
            if (i < period) {
                smoothed.add(Double.NaN);
                continue;
            }

            if (i == period) {
                // Initial sum for the first smoothed value
                double sum = 0;
                for (int j = 1; j <= period; j++) {
                    sum += values.get(i - period + j);
                }
                smoothed.add(sum);
            } else {
                // Smoothed value = previous smoothed - (previous smoothed / period) + current value
                double prevSmoothed = smoothed.get(i - 1);
                smoothed.add(prevSmoothed - (prevSmoothed / period) + values.get(i));
            }
        }

        return smoothed;
    }

    /**
     * Calculate ADX (smoothed DX values).
     * Uses NaN as sentinel value for missing data to maintain garbage-free performance.
     *
     * ADX Algorithm:
     * 1. First ADX = Simple average of first 'period' DX values
     * 2. Subsequent ADX = (Previous ADX * (period-1) + Current DX) / period
     */
    private DoubleArrayList calculateADX(DoubleArrayList dx, int period) {
        DoubleArrayList adx = new DoubleArrayList(2000);

        // Validate input
        if (dx == null || dx.isEmpty()) {
            logger.warn("calculateADX: Empty or null DX array provided");
            return adx;
        }

        logger.debug("calculateADX: Processing {} DX values with period {}", dx.size(), period);

        // Enhanced debugging: Log first few DX values to understand the input
        if (logger.isDebugEnabled()) {
            StringBuilder dxSample = new StringBuilder("calculateADX: First 10 DX values: ");
            for (int i = 0; i < Math.min(10, dx.size()); i++) {
                dxSample.append(String.format("[%d]=%.2f ", i, dx.get(i)));
            }
            logger.debug(dxSample.toString());
        }

        for (int i = 0; i < dx.size(); i++) {
            // STANDARD DMI ALGORITHM: ADX calculation starts when we have 'period' consecutive valid DX values
            // Since DX values start from index (period-1), first ADX should be at index (2*period-1)
            // This gives us exactly 'period' DX values to average: from (period-1) to (2*period-2)
            if (i < (2 * period - 1)) {
                adx.add(Double.NaN);
                continue;
            }

            if (i == (2 * period - 1)) {
                // Initial ADX calculation - average of the first 'period' DX values
                // These are the DX values from index (period-1) to (2*period-2)
                double sumDX = 0;
                int count = 0;
                int startIdx = period - 1; // First valid DX index
                int endIdx = 2 * period - 2; // Last DX index for averaging (i-1)

                logger.debug("calculateADX: Initial ADX calculation at index {} - averaging DX from {} to {}",
                           i, startIdx, endIdx);

                for (int j = startIdx; j <= endIdx; j++) {
                    double dxValue = dx.get(j);
                    if (!Double.isNaN(dxValue)) {
                        sumDX += dxValue;
                        count++;
                        logger.debug("calculateADX: Adding DX[{}] = {} to sum", j, dxValue);
                    }
                }

                double initialADX = count == 0 ? Double.NaN : sumDX / count;
                adx.add(initialADX);
                logger.debug("calculateADX: Initial ADX = {} (sum={}, count={})", initialADX, sumDX, count);

            } else {
                // Smoothed ADX = (previous ADX * (period-1) + current DX) / period
                double currentDX = dx.get(i);
                double prevADX = adx.get(i - 1);

                if (Double.isNaN(currentDX) && Double.isNaN(prevADX)) {
                    // Both current DX and previous ADX are NaN - cannot calculate
                    adx.add(Double.NaN);
                    logger.debug("calculateADX: ADX[{}] = NaN (currentDX=NaN, prevADX=NaN)", i);
                } else if (Double.isNaN(currentDX)) {
                    // Current DX is NaN but previous ADX is valid - skip this DX value and keep previous ADX
                    // This allows ADX to recover from temporary NaN DX values
                    adx.add(prevADX);
                    logger.debug("calculateADX: ADX[{}] = {} (skipping NaN currentDX, keeping prevADX)", i, prevADX);
                } else if (Double.isNaN(prevADX)) {
                    // Previous ADX is NaN but current DX is valid - try to restart ADX calculation
                    // Look back to find the last valid ADX or recalculate from available DX values
                    double restartedADX = attemptADXRestart(dx, adx, i, period);
                    adx.add(restartedADX);
                    logger.debug("calculateADX: ADX[{}] = {} (restarted from NaN prevADX, currentDX={})", i, restartedADX, currentDX);
                } else {
                    // Both values are valid - normal calculation
                    double smoothedADX = (prevADX * (period - 1) + currentDX) / period;
                    adx.add(smoothedADX);
                    logger.debug("calculateADX: ADX[{}] = {} (prevADX={}, currentDX={})", i, smoothedADX, prevADX, currentDX);
                }
            }
        }

        // Log summary
        int validADXCount = 0;
        for (int i = 0; i < adx.size(); i++) {
            if (!Double.isNaN(adx.get(i))) {
                validADXCount++;
            }
        }
        logger.debug("calculateADX: Completed - {} total values, {} valid ADX values", adx.size(), validADXCount);

        return adx;
    }

    /**
     * Attempt to restart ADX calculation when previous ADX is NaN but current DX is valid.
     * This method tries to find a valid starting point for ADX calculation by looking back
     * at recent DX values or using the current DX as a new starting point.
     */
    private double attemptADXRestart(DoubleArrayList dx, DoubleArrayList adx, int currentIndex, int period) {
        // Strategy 1: Look back for the last valid ADX within a reasonable window
        int lookBackLimit = Math.min(period * 2, currentIndex);
        for (int j = currentIndex - 1; j >= currentIndex - lookBackLimit; j--) {
            if (j >= 0 && j < adx.size() && !Double.isNaN(adx.get(j))) {
                double lastValidADX = adx.get(j);
                double currentDX = dx.get(currentIndex);
                // Use exponential smoothing with adjusted weight based on gap
                int gap = currentIndex - j;
                double weight = Math.pow(0.9, gap); // Decay factor for older values
                double restartedADX = (lastValidADX * weight + currentDX * (1 - weight));
                logger.debug("attemptADXRestart: Found valid ADX at index {} (gap={}), restarting with weight={}, result={}",
                           j, gap, weight, restartedADX);
                return restartedADX;
            }
        }

        // Strategy 2: Calculate simple average of recent valid DX values
        double sumDX = 0;
        int count = 0;
        int lookBackForDX = Math.min(period, currentIndex + 1);

        for (int j = currentIndex; j > currentIndex - lookBackForDX && j >= 0; j--) {
            double dxValue = dx.get(j);
            if (!Double.isNaN(dxValue)) {
                sumDX += dxValue;
                count++;
            }
        }

        if (count > 0) {
            double avgDX = sumDX / count;
            logger.debug("attemptADXRestart: Using average of {} recent DX values: {}", count, avgDX);
            return avgDX;
        }

        // Strategy 3: Use current DX value as new starting point
        double currentDX = dx.get(currentIndex);
        if (!Double.isNaN(currentDX)) {
            logger.debug("attemptADXRestart: Using current DX as restart value: {}", currentDX);
            return currentDX;
        }

        // All strategies failed
        logger.debug("attemptADXRestart: All restart strategies failed, returning NaN");
        return Double.NaN;
    }

    /**
     * Store DMI calculation results in the database.
     */
    private int storeDMIResults(String symbol, List<LocalDate> dates, DoubleArrayList plusDI,
                              DoubleArrayList minusDI, DoubleArrayList dx, DoubleArrayList adx, LocalDate fromDate) throws SQLException {

        String updateSql = "UPDATE ohlcv SET dmi_plus_di = ?, dmi_minus_di = ?, dmi_dx = ?, dmi_adx = ? WHERE symbol = ? AND date = ?";
        int updatedRecords = 0;

        try {
            connection.setAutoCommit(false);

            try (PreparedStatement pstmt = connection.prepareStatement(updateSql)) {
                for (int i = 0; i < dates.size(); i++) {
                    LocalDate date = dates.get(i);

                    // If fromDate is specified, only update records from that date onwards (inclusive)
                    if (fromDate != null && date.isBefore(fromDate)) {
                        continue;
                    }

                    double pdi = plusDI.get(i);
                    double mdi = minusDI.get(i);
                    double dxVal = dx.get(i);
                    double adxVal = adx.get(i);

                    // Set parameters - use NaN check instead of null check
                    if (!Double.isNaN(pdi)) {
                        pstmt.setDouble(1, pdi);
                    } else {
                        pstmt.setNull(1, java.sql.Types.DECIMAL);
                    }

                    if (!Double.isNaN(mdi)) {
                        pstmt.setDouble(2, mdi);
                    } else {
                        pstmt.setNull(2, java.sql.Types.DECIMAL);
                    }

                    if (!Double.isNaN(dxVal)) {
                        pstmt.setDouble(3, dxVal);
                    } else {
                        pstmt.setNull(3, java.sql.Types.DECIMAL);
                    }

                    if (!Double.isNaN(adxVal)) {
                        pstmt.setDouble(4, adxVal);
                    } else {
                        pstmt.setNull(4, java.sql.Types.DECIMAL);
                    }

                    // Enhanced debugging: Always log when we have DX values to trace the ADX issue
                    if (!Double.isNaN(dxVal)) {
                        logger.debug("Storing DMI data for symbol: {} on {}: +DI={}, -DI={}, DX={}, ADX={} (index={})",
                                   symbol, date,
                                   Double.isNaN(pdi) ? "null" : String.format("%.2f", pdi),
                                   Double.isNaN(mdi) ? "null" : String.format("%.2f", mdi),
                                   String.format("%.2f", dxVal),
                                   Double.isNaN(adxVal) ? "null" : String.format("%.2f", adxVal),
                                   i);
                    }

                    // Note: DX values without ADX values are EXPECTED behavior in DMI calculation
                    // ADX requires 'period' consecutive DX values, so there's always a gap where DX exists but ADX doesn't
                    // This is mathematically correct and not an error condition

                    pstmt.setString(5, symbol);
                    pstmt.setDate(6, Date.valueOf(date));

                    int rowsUpdated = pstmt.executeUpdate();
                    updatedRecords += rowsUpdated;
                }

                connection.commit();
                logger.debug("Updated {} records with DMI data for symbol: {}", updatedRecords, symbol);

            } catch (SQLException e) {
                connection.rollback();
                throw e;
            } finally {
                connection.setAutoCommit(true);
            }

        } catch (SQLException e) {
            logger.error("Error storing DMI results for symbol: {}", symbol, e);
            throw new SQLException("Failed to store DMI results for symbol: " + symbol, e);
        }

        return updatedRecords;
    }

    /**
     * Count records that would be updated for DMI calculation (for dry run).
     */
    private int countRecordsForDMICalculation(String symbol, int period) throws SQLException {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    int totalRecords = rs.getInt("count");
                    // DMI needs 2*period records minimum, so return records that would get DMI values
                    return Math.max(0, totalRecords - (2 * period - 1));
                }
                return 0;
            }
        }
    }

    /**
     * Count records that would be updated for incremental DMI calculation (for dry run).
     * Uses N-1 reference point, so counts from the second-to-last calculated date onwards.
     */
    private int countRecordsForIncrementalDMICalculation(String symbol, LocalDate lastCalculatedDate) throws SQLException {
        String sql = "SELECT COUNT(*) as count FROM ohlcv WHERE symbol = ? AND date >= ?";

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, symbol);
            pstmt.setDate(2, Date.valueOf(lastCalculatedDate)); // Count from N-1 date onwards (inclusive)

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count");
                }
                return 0;
            }
        }
    }

    public void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
                logger.info("Database connection closed");
            } catch (SQLException e) {
                logger.error("Error closing database connection", e);
            }
        }
    }
}
