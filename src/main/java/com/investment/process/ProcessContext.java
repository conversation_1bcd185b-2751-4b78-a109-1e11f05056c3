package com.investment.process;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

/**
 * Thread-safe context for managing process execution state and cancellation.
 * This class provides a way for long-running operations to check for cancellation
 * requests and report progress updates.
 */
public class ProcessContext {
    private final ProcessInfo processInfo;
    private final AtomicBoolean cancellationRequested;
    private final AtomicReference<Consumer<ProcessInfo>> progressCallback;
    
    /**
     * Create a new ProcessContext.
     */
    public ProcessContext(ProcessInfo processInfo) {
        this.processInfo = processInfo;
        this.cancellationRequested = new AtomicBoolean(false);
        this.progressCallback = new AtomicReference<>();
    }
    
    /**
     * Get the associated ProcessInfo.
     */
    public ProcessInfo getProcessInfo() {
        return processInfo;
    }
    
    /**
     * Check if cancellation has been requested for this process.
     * Long-running operations should check this periodically and abort gracefully.
     */
    public boolean isCancellationRequested() {
        return cancellationRequested.get();
    }
    
    /**
     * Request cancellation of this process.
     * This sets a flag that the running process should check periodically.
     */
    public void requestCancellation() {
        cancellationRequested.set(true);
        processInfo.updateStatus(ProcessStatus.ABORTING);
        processInfo.updateCurrentOperation("Cancellation requested - cleaning up...");
    }
    
    /**
     * Throw an exception if cancellation has been requested.
     * This provides a convenient way for operations to abort when cancelled.
     */
    public void throwIfCancellationRequested() throws ProcessCancelledException {
        if (isCancellationRequested()) {
            throw new ProcessCancelledException("Process " + processInfo.getProcessId() + " was cancelled");
        }
    }
    
    /**
     * Update the current operation description.
     */
    public void updateCurrentOperation(String operation) {
        processInfo.updateCurrentOperation(operation);
        notifyProgressCallback();
    }
    
    /**
     * Update progress with processed and total item counts.
     */
    public void updateProgress(int processed, int total) {
        processInfo.updateProgress(processed, total);
        notifyProgressCallback();
    }
    
    /**
     * Update progress percentage directly.
     */
    public void updateProgressPercentage(int percentage) {
        processInfo.updateProgressPercentage(percentage);
        notifyProgressCallback();
    }
    
    /**
     * Update progress with a custom message.
     */
    public void updateProgress(int processed, int total, String operation) {
        processInfo.updateProgress(processed, total);
        processInfo.updateCurrentOperation(operation);
        notifyProgressCallback();
    }
    
    /**
     * Set a callback to be notified of progress updates.
     */
    public void setProgressCallback(Consumer<ProcessInfo> callback) {
        progressCallback.set(callback);
    }
    
    /**
     * Mark the process as completed successfully.
     */
    public void markCompleted() {
        processInfo.markCompleted();
        notifyProgressCallback();
    }
    
    /**
     * Mark the process as failed with an error message.
     */
    public void markFailed(String errorMessage) {
        processInfo.setError(errorMessage);
        notifyProgressCallback();
    }
    
    /**
     * Mark the process as aborted.
     */
    public void markAborted() {
        processInfo.markAborted();
        notifyProgressCallback();
    }
    
    /**
     * Notify the progress callback if one is set.
     */
    private void notifyProgressCallback() {
        Consumer<ProcessInfo> callback = progressCallback.get();
        if (callback != null) {
            try {
                callback.accept(processInfo);
            } catch (Exception e) {
                // Ignore callback errors to prevent them from affecting the main process
            }
        }
    }
    
    /**
     * Exception thrown when a process is cancelled.
     */
    public static class ProcessCancelledException extends Exception {
        public ProcessCancelledException(String message) {
            super(message);
        }
        
        public ProcessCancelledException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
