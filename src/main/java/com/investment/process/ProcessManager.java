package com.investment.process;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Central manager for tracking and controlling long-running processes in the Investment Toolkit.
 * This service provides thread-safe operations for process registration, monitoring, and termination.
 */
@Service
public class ProcessManager {
    private static final Logger logger = LoggerFactory.getLogger(ProcessManager.class);
    
    private final ConcurrentHashMap<String, ProcessContext> activeProcesses;
    private final AtomicInteger processCounter;
    
    public ProcessManager() {
        this.activeProcesses = new ConcurrentHashMap<>();
        this.processCounter = new AtomicInteger(0);
    }
    
    /**
     * Generate a unique process ID.
     */
    public String generateProcessId(ProcessType processType) {
        int counter = processCounter.incrementAndGet();
        String timestamp = LocalDateTime.now().toString().replaceAll("[^0-9]", "").substring(0, 12);
        return String.format("%s_%s_%04d", 
                processType.name().toLowerCase(), 
                timestamp, 
                counter);
    }
    
    /**
     * Register a new process and return its context.
     */
    public ProcessContext registerProcess(ProcessType processType, String description, String initiatedBy) {
        String processId = generateProcessId(processType);
        ProcessInfo processInfo = new ProcessInfo(processId, processType, description, initiatedBy);
        ProcessContext context = new ProcessContext(processInfo);
        
        activeProcesses.put(processId, context);
        
        logger.info("Registered new process: {} [{}] - {}", processId, processType.getDisplayName(), description);
        return context;
    }
    
    /**
     * Get a process context by ID.
     */
    public Optional<ProcessContext> getProcess(String processId) {
        return Optional.ofNullable(activeProcesses.get(processId));
    }
    
    /**
     * Get all active processes (not in terminal state).
     */
    public List<ProcessInfo> getActiveProcesses() {
        return activeProcesses.values().stream()
                .map(ProcessContext::getProcessInfo)
                .filter(info -> !info.isTerminal())
                .collect(Collectors.toList());
    }
    
    /**
     * Get all processes (including completed/failed ones).
     */
    public List<ProcessInfo> getAllProcesses() {
        return activeProcesses.values().stream()
                .map(ProcessContext::getProcessInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * Get processes by type.
     */
    public List<ProcessInfo> getProcessesByType(ProcessType processType) {
        return activeProcesses.values().stream()
                .map(ProcessContext::getProcessInfo)
                .filter(info -> info.getProcessType() == processType)
                .collect(Collectors.toList());
    }
    
    /**
     * Get processes by status.
     */
    public List<ProcessInfo> getProcessesByStatus(ProcessStatus status) {
        return activeProcesses.values().stream()
                .map(ProcessContext::getProcessInfo)
                .filter(info -> info.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    /**
     * Request cancellation of a specific process.
     */
    public boolean abortProcess(String processId) {
        ProcessContext context = activeProcesses.get(processId);
        if (context != null && !context.getProcessInfo().isTerminal()) {
            logger.info("Requesting abort for process: {}", processId);
            context.requestCancellation();
            return true;
        }
        return false;
    }
    
    /**
     * Request cancellation of all active processes.
     */
    public AbortAllResult abortAllProcesses() {
        List<ProcessInfo> activeProcesses = getActiveProcesses();
        int totalActive = activeProcesses.size();
        int abortRequested = 0;
        List<String> abortedProcessIds = new ArrayList<>();
        List<String> failedToAbortIds = new ArrayList<>();
        
        logger.info("Requesting abort for {} active processes", totalActive);
        
        for (ProcessInfo processInfo : activeProcesses) {
            try {
                if (abortProcess(processInfo.getProcessId())) {
                    abortRequested++;
                    abortedProcessIds.add(processInfo.getProcessId());
                    logger.info("Abort requested for process: {} [{}]", 
                            processInfo.getProcessId(), processInfo.getProcessType().getDisplayName());
                } else {
                    failedToAbortIds.add(processInfo.getProcessId());
                    logger.warn("Failed to abort process: {} [{}]", 
                            processInfo.getProcessId(), processInfo.getProcessType().getDisplayName());
                }
            } catch (Exception e) {
                failedToAbortIds.add(processInfo.getProcessId());
                logger.error("Error aborting process: {} [{}]", 
                        processInfo.getProcessId(), processInfo.getProcessType().getDisplayName(), e);
            }
        }
        
        logger.info("Abort all processes completed: {}/{} processes abort requested", abortRequested, totalActive);
        return new AbortAllResult(totalActive, abortRequested, abortedProcessIds, failedToAbortIds);
    }
    
    /**
     * Remove completed processes from the active list (cleanup).
     * This should be called periodically to prevent memory leaks.
     */
    public int cleanupCompletedProcesses() {
        List<String> completedProcessIds = activeProcesses.entrySet().stream()
                .filter(entry -> entry.getValue().getProcessInfo().isTerminal())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        for (String processId : completedProcessIds) {
            activeProcesses.remove(processId);
        }
        
        if (!completedProcessIds.isEmpty()) {
            logger.info("Cleaned up {} completed processes", completedProcessIds.size());
        }
        
        return completedProcessIds.size();
    }
    
    /**
     * Get summary statistics about processes.
     */
    public ProcessStatistics getStatistics() {
        List<ProcessInfo> allProcesses = getAllProcesses();
        
        Map<ProcessStatus, Long> statusCounts = allProcesses.stream()
                .collect(Collectors.groupingBy(ProcessInfo::getStatus, Collectors.counting()));
        
        Map<ProcessType, Long> typeCounts = allProcesses.stream()
                .collect(Collectors.groupingBy(ProcessInfo::getProcessType, Collectors.counting()));
        
        return new ProcessStatistics(
                allProcesses.size(),
                getActiveProcesses().size(),
                statusCounts,
                typeCounts
        );
    }
    
    /**
     * Result of aborting all processes.
     */
    public static class AbortAllResult {
        private final int totalActiveProcesses;
        private final int abortRequestsSent;
        private final List<String> abortedProcessIds;
        private final List<String> failedToAbortIds;
        
        public AbortAllResult(int totalActiveProcesses, int abortRequestsSent, 
                             List<String> abortedProcessIds, List<String> failedToAbortIds) {
            this.totalActiveProcesses = totalActiveProcesses;
            this.abortRequestsSent = abortRequestsSent;
            this.abortedProcessIds = abortedProcessIds;
            this.failedToAbortIds = failedToAbortIds;
        }
        
        public int getTotalActiveProcesses() { return totalActiveProcesses; }
        public int getAbortRequestsSent() { return abortRequestsSent; }
        public List<String> getAbortedProcessIds() { return abortedProcessIds; }
        public List<String> getFailedToAbortIds() { return failedToAbortIds; }
        
        public boolean isFullSuccess() {
            return abortRequestsSent == totalActiveProcesses && failedToAbortIds.isEmpty();
        }
    }
    
    /**
     * Statistics about processes.
     */
    public static class ProcessStatistics {
        private final int totalProcesses;
        private final int activeProcesses;
        private final Map<ProcessStatus, Long> statusCounts;
        private final Map<ProcessType, Long> typeCounts;
        
        public ProcessStatistics(int totalProcesses, int activeProcesses, 
                               Map<ProcessStatus, Long> statusCounts, Map<ProcessType, Long> typeCounts) {
            this.totalProcesses = totalProcesses;
            this.activeProcesses = activeProcesses;
            this.statusCounts = statusCounts;
            this.typeCounts = typeCounts;
        }
        
        public int getTotalProcesses() { return totalProcesses; }
        public int getActiveProcesses() { return activeProcesses; }
        public Map<ProcessStatus, Long> getStatusCounts() { return statusCounts; }
        public Map<ProcessType, Long> getTypeCounts() { return typeCounts; }
    }
}
