package com.investment.process;

/**
 * Enumeration of different types of long-running processes in the Investment Toolkit.
 * Used to categorize and identify different operation types for management and monitoring.
 */
public enum ProcessType {
    /**
     * DMI (Directional Movement Index) calculation for multiple instruments.
     */
    DMI_CALCULATION("DMI Calculation", "Calculate DMI technical indicators for instruments"),
    
    /**
     * <PERSON><PERSON><PERSON> Bands calculation for multiple instruments.
     */
    BOLLINGER_BANDS_CALCULATION("Bollinger Bands Calculation", "Calculate Bollinger Bands technical indicators for instruments"),
    
    /**
     * OHLCV data refresh operation for multiple instruments.
     */
    OHLCV_REFRESH("OHLCV Data Refresh", "Refresh OHLCV data from external data providers"),
    
    /**
     * SEC data synchronization operation.
     */
    SEC_SYNCHRONIZATION("SEC Data Synchronization", "Synchronize instrument data with SEC company tickers"),
    
    /**
     * CSV instrument data upload and processing.
     */
    CSV_INSTRUMENT_UPLOAD("CSV Instrument Upload", "Process and import instrument data from CSV files"),
    
    /**
     * Symbol validation operation.
     */
    SYMBOL_VALIDATION("Symbol Validation", "Validate instrument symbols against authoritative sources"),
    
    /**
     * Generic bulk data processing operation.
     */
    BULK_DATA_PROCESSING("Bulk Data Processing", "Generic bulk data processing operation");
    
    private final String displayName;
    private final String description;
    
    ProcessType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Check if this process type typically involves external API calls.
     */
    public boolean involvesExternalApis() {
        return this == OHLCV_REFRESH || this == SEC_SYNCHRONIZATION;
    }
    
    /**
     * Check if this process type typically involves database modifications.
     */
    public boolean involvesDatabaseModifications() {
        return this != SYMBOL_VALIDATION; // Most operations modify data except validation
    }
    
    /**
     * Get the estimated typical duration category for this process type.
     */
    public DurationCategory getTypicalDuration() {
        switch (this) {
            case DMI_CALCULATION:
            case BOLLINGER_BANDS_CALCULATION:
                return DurationCategory.LONG; // Can take hours for all instruments
            case OHLCV_REFRESH:
                return DurationCategory.VERY_LONG; // Can take many hours due to rate limiting
            case SEC_SYNCHRONIZATION:
                return DurationCategory.MEDIUM; // Usually completes in minutes
            case CSV_INSTRUMENT_UPLOAD:
                return DurationCategory.SHORT; // Usually completes quickly
            case SYMBOL_VALIDATION:
                return DurationCategory.MEDIUM; // Depends on number of symbols
            case BULK_DATA_PROCESSING:
                return DurationCategory.LONG; // Generic, assume long
            default:
                return DurationCategory.MEDIUM;
        }
    }
    
    /**
     * Duration categories for process types.
     */
    public enum DurationCategory {
        SHORT("< 1 minute"),
        MEDIUM("1-10 minutes"),
        LONG("10 minutes - 2 hours"),
        VERY_LONG("> 2 hours");
        
        private final String description;
        
        DurationCategory(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
