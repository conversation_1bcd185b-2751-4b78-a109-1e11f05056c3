package com.investment.process;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

/**
 * Service for executing long-running processes asynchronously with proper process tracking.
 * This class provides a standardized way to run operations in the background while
 * maintaining process lifecycle management and cancellation support.
 */
@Service
public class AsyncProcessExecutor {
    private static final Logger logger = LoggerFactory.getLogger(AsyncProcessExecutor.class);
    
    private final ProcessManager processManager;
    
    public AsyncProcessExecutor(ProcessManager processManager) {
        this.processManager = processManager;
    }
    
    /**
     * Execute a process asynchronously with full process tracking.
     *
     * @param processType The type of process being executed
     * @param description Human-readable description of the process
     * @param initiatedBy Who initiated the process (user, system, etc.)
     * @param operation The operation to execute, which receives a ProcessContext
     * @return CompletableFuture containing the ProcessExecutionResult for tracking
     */
    @Async
    public <T> CompletableFuture<ProcessExecutionResult<T>> executeAsync(
            ProcessType processType,
            String description,
            String initiatedBy,
            Function<ProcessContext, T> operation) {
        
        // Register the process
        ProcessContext context = processManager.registerProcess(processType, description, initiatedBy);
        String processId = context.getProcessInfo().getProcessId();
        
        logger.info("Starting async execution of process: {} [{}]", processId, processType.getDisplayName());
        
        try {
            // Execute the operation
            T result = operation.apply(context);

            // Mark as completed if not already in terminal state
            if (!context.getProcessInfo().isTerminal()) {
                context.markCompleted();
            }

            logger.info("Process completed successfully: {} [{}]", processId, processType.getDisplayName());
            return CompletableFuture.completedFuture(
                    new ProcessExecutionResult<>(context, result, null));

        } catch (RuntimeException e) {
            // Check if this is a wrapped ProcessCancelledException
            if (e.getCause() instanceof ProcessContext.ProcessCancelledException) {
                context.markAborted();
                logger.info("Process was cancelled: {} [{}] - {}", processId, processType.getDisplayName(), e.getCause().getMessage());
                return CompletableFuture.completedFuture(
                        new ProcessExecutionResult<>(context, null, (ProcessContext.ProcessCancelledException) e.getCause()));
            }

            // Process failed
            context.markFailed("Process failed: " + e.getMessage());
            logger.error("Process failed: {} [{}]", processId, processType.getDisplayName(), e);
            return CompletableFuture.completedFuture(
                    new ProcessExecutionResult<>(context, null, e));

        } catch (Exception e) {
            // Process failed
            context.markFailed("Process failed: " + e.getMessage());
            logger.error("Process failed: {} [{}]", processId, processType.getDisplayName(), e);
            return CompletableFuture.completedFuture(
                    new ProcessExecutionResult<>(context, null, e));
        }
    }
    


    /**
     * Start a process asynchronously and return the process ID immediately.
     * This method is designed for REST API endpoints that need to return quickly.
     *
     * @param processType The type of process being executed
     * @param description Human-readable description of the process
     * @param initiatedBy Who initiated the process (user, system, etc.)
     * @param operation The operation to execute, which receives a ProcessContext
     * @return Process ID for tracking the execution
     */
    public <T> String startAsync(
            ProcessType processType,
            String description,
            String initiatedBy,
            Function<ProcessContext, T> operation) {

        // Register the process first to get the ID
        ProcessContext context = processManager.registerProcess(processType, description, initiatedBy);
        String processId = context.getProcessInfo().getProcessId();

        // Start the async execution
        executeAsyncWithContext(context, operation);

        return processId;
    }

    /**
     * Execute a process asynchronously using an existing context.
     */
    @Async
    private <T> CompletableFuture<ProcessExecutionResult<T>> executeAsyncWithContext(
            ProcessContext context,
            Function<ProcessContext, T> operation) {

        String processId = context.getProcessInfo().getProcessId();
        ProcessType processType = context.getProcessInfo().getProcessType();

        logger.info("Starting async execution of process: {} [{}]", processId, processType.getDisplayName());

        try {
            // Execute the operation
            T result = operation.apply(context);

            // Mark as completed if not already in terminal state
            if (!context.getProcessInfo().isTerminal()) {
                context.markCompleted();
            }

            logger.info("Process completed successfully: {} [{}]", processId, processType.getDisplayName());
            return CompletableFuture.completedFuture(
                    new ProcessExecutionResult<>(context, result, null));

        } catch (RuntimeException e) {
            // Check if this is a wrapped ProcessCancelledException
            if (e.getCause() instanceof ProcessContext.ProcessCancelledException) {
                context.markAborted();
                logger.info("Process was cancelled: {} [{}] - {}", processId, processType.getDisplayName(), e.getCause().getMessage());
                return CompletableFuture.completedFuture(
                        new ProcessExecutionResult<>(context, null, (ProcessContext.ProcessCancelledException) e.getCause()));
            }

            // Process failed
            context.markFailed("Process failed: " + e.getMessage());
            logger.error("Process failed: {} [{}]", processId, processType.getDisplayName(), e);
            return CompletableFuture.completedFuture(
                    new ProcessExecutionResult<>(context, null, e));

        } catch (Exception e) {
            // Process failed
            context.markFailed("Process failed: " + e.getMessage());
            logger.error("Process failed: {} [{}]", processId, processType.getDisplayName(), e);
            return CompletableFuture.completedFuture(
                    new ProcessExecutionResult<>(context, null, e));
        }
    }
    
    /**
     * Execute a process synchronously (for testing or when async is not needed).
     */
    public <T> ProcessExecutionResult<T> executeSync(
            ProcessType processType,
            String description,
            String initiatedBy,
            Function<ProcessContext, T> operation) {
        
        ProcessContext context = processManager.registerProcess(processType, description, initiatedBy);
        String processId = context.getProcessInfo().getProcessId();
        
        logger.info("Starting sync execution of process: {} [{}]", processId, processType.getDisplayName());
        
        try {
            T result = operation.apply(context);

            if (!context.getProcessInfo().isTerminal()) {
                context.markCompleted();
            }

            logger.info("Process completed successfully: {} [{}]", processId, processType.getDisplayName());
            return new ProcessExecutionResult<>(context, result, null);

        } catch (RuntimeException e) {
            // Check if this is a wrapped ProcessCancelledException
            if (e.getCause() instanceof ProcessContext.ProcessCancelledException) {
                context.markAborted();
                logger.info("Process was cancelled: {} [{}] - {}", processId, processType.getDisplayName(), e.getCause().getMessage());
                return new ProcessExecutionResult<>(context, null, (ProcessContext.ProcessCancelledException) e.getCause());
            }

            // Process failed
            context.markFailed("Process failed: " + e.getMessage());
            logger.error("Process failed: {} [{}]", processId, processType.getDisplayName(), e);
            return new ProcessExecutionResult<>(context, null, e);

        } catch (Exception e) {
            context.markFailed("Process failed: " + e.getMessage());
            logger.error("Process failed: {} [{}]", processId, processType.getDisplayName(), e);
            return new ProcessExecutionResult<>(context, null, e);
        }
    }
    

    
    /**
     * Result of process execution containing the context, result, and any exception.
     */
    public static class ProcessExecutionResult<T> {
        private final ProcessContext context;
        private final T result;
        private final Exception exception;
        
        public ProcessExecutionResult(ProcessContext context, T result, Exception exception) {
            this.context = context;
            this.result = result;
            this.exception = exception;
        }
        
        public ProcessContext getContext() { return context; }
        public T getResult() { return result; }
        public Exception getException() { return exception; }
        
        public boolean isSuccess() { 
            return exception == null && context.getProcessInfo().getStatus() == ProcessStatus.COMPLETED; 
        }
        
        public boolean wasCancelled() { 
            return exception instanceof ProcessContext.ProcessCancelledException || 
                   context.getProcessInfo().getStatus() == ProcessStatus.ABORTED; 
        }
        
        public boolean hasFailed() { 
            return exception != null && !wasCancelled(); 
        }
        
        public String getProcessId() {
            return context.getProcessInfo().getProcessId();
        }
        
        public ProcessInfo getProcessInfo() {
            return context.getProcessInfo();
        }
    }
}
