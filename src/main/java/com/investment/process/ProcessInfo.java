package com.investment.process;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Information about a long-running process in the Investment Toolkit.
 * This class is thread-safe and tracks the complete lifecycle of a process.
 */
public class ProcessInfo {
    private final String processId;
    private final ProcessType processType;
    private final String description;
    private final LocalDateTime startTime;
    private final AtomicReference<ProcessStatus> status;
    private final AtomicReference<LocalDateTime> endTime;
    private final AtomicInteger progressPercentage;
    private final AtomicReference<String> currentOperation;
    private final AtomicReference<String> errorMessage;
    private final AtomicInteger totalItems;
    private final AtomicInteger processedItems;
    private final String initiatedBy;
    
    /**
     * Create a new ProcessInfo instance.
     */
    public ProcessInfo(String processId, ProcessType processType, String description, String initiatedBy) {
        this.processId = processId;
        this.processType = processType;
        this.description = description;
        this.initiatedBy = initiatedBy;
        this.startTime = LocalDateTime.now();
        this.status = new AtomicReference<>(ProcessStatus.RUNNING);
        this.endTime = new AtomicReference<>();
        this.progressPercentage = new AtomicInteger(0);
        this.currentOperation = new AtomicReference<>("Starting...");
        this.errorMessage = new AtomicReference<>();
        this.totalItems = new AtomicInteger(0);
        this.processedItems = new AtomicInteger(0);
    }
    
    // Getters
    public String getProcessId() { return processId; }
    public ProcessType getProcessType() { return processType; }
    public String getDescription() { return description; }
    public LocalDateTime getStartTime() { return startTime; }
    public ProcessStatus getStatus() { return status.get(); }
    public LocalDateTime getEndTime() { return endTime.get(); }
    public int getProgressPercentage() { return progressPercentage.get(); }
    public String getCurrentOperation() { return currentOperation.get(); }
    public String getErrorMessage() { return errorMessage.get(); }
    public int getTotalItems() { return totalItems.get(); }
    public int getProcessedItems() { return processedItems.get(); }
    public String getInitiatedBy() { return initiatedBy; }
    
    /**
     * Get the duration of the process (running time or total time if completed).
     */
    public Duration getDuration() {
        LocalDateTime end = endTime.get();
        if (end != null) {
            return Duration.between(startTime, end);
        } else {
            return Duration.between(startTime, LocalDateTime.now());
        }
    }
    
    /**
     * Check if the process is currently running.
     */
    public boolean isRunning() {
        return status.get() == ProcessStatus.RUNNING;
    }
    
    /**
     * Check if the process is in a terminal state.
     */
    public boolean isTerminal() {
        return status.get().isTerminal();
    }
    
    /**
     * Update the process status.
     */
    public void updateStatus(ProcessStatus newStatus) {
        ProcessStatus oldStatus = status.getAndSet(newStatus);
        if (newStatus.isTerminal() && !oldStatus.isTerminal()) {
            endTime.set(LocalDateTime.now());
        }
    }
    
    /**
     * Update the current operation description.
     */
    public void updateCurrentOperation(String operation) {
        currentOperation.set(operation);
    }
    
    /**
     * Update progress information.
     */
    public void updateProgress(int processed, int total) {
        processedItems.set(processed);
        totalItems.set(total);
        if (total > 0) {
            int percentage = (int) ((processed * 100.0) / total);
            progressPercentage.set(Math.min(100, percentage));
        }
    }
    
    /**
     * Update progress percentage directly.
     */
    public void updateProgressPercentage(int percentage) {
        progressPercentage.set(Math.max(0, Math.min(100, percentage)));
    }
    
    /**
     * Set error message and update status to FAILED.
     */
    public void setError(String error) {
        errorMessage.set(error);
        updateStatus(ProcessStatus.FAILED);
    }
    
    /**
     * Mark the process as completed successfully.
     */
    public void markCompleted() {
        updateStatus(ProcessStatus.COMPLETED);
        progressPercentage.set(100);
        currentOperation.set("Completed");
    }
    
    /**
     * Mark the process as aborted.
     */
    public void markAborted() {
        updateStatus(ProcessStatus.ABORTED);
        currentOperation.set("Aborted");
    }
    
    /**
     * Get a summary string of the process.
     */
    public String getSummary() {
        return String.format("%s [%s] - %s (%d%% complete, %d/%d items)", 
                processType.getDisplayName(), 
                status.get(), 
                description,
                progressPercentage.get(),
                processedItems.get(),
                totalItems.get());
    }
    
    @Override
    public String toString() {
        return String.format("ProcessInfo{id='%s', type=%s, status=%s, progress=%d%%, duration=%s}", 
                processId, processType, status.get(), progressPercentage.get(), getDuration());
    }
}
