package com.investment.process;

/**
 * Enumeration of possible process statuses in the Investment Toolkit.
 * Used to track the lifecycle of long-running operations.
 */
public enum ProcessStatus {
    /**
     * Process is currently running and active.
     */
    RUNNING("Process is currently executing"),
    
    /**
     * Process completed successfully.
     */
    COMPLETED("Process completed successfully"),
    
    /**
     * Process was aborted by user request or system intervention.
     */
    ABORTED("Process was aborted"),
    
    /**
     * Process failed due to an error or exception.
     */
    FAILED("Process failed with an error"),
    
    /**
     * Process is in the process of being aborted (cleanup in progress).
     */
    ABORTING("Process is being aborted");
    
    private final String description;
    
    ProcessStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Check if the process is in a terminal state (completed, aborted, or failed).
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == ABORTED || this == FAILED;
    }
    
    /**
     * Check if the process is currently active (running or aborting).
     */
    public boolean isActive() {
        return this == RUNNING || this == ABORTING;
    }
}
