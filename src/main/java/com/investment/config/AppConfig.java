package com.investment.config;

import com.investment.database.DatabaseManager;
import com.investment.provider.DataProvider;
import com.investment.provider.YahooFinanceProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executor;

/**
 * Application configuration class.
 */
@Configuration
@EnableAsync
public class AppConfig {

    @Bean
    public DatabaseManager databaseManager() {
        DatabaseManager dbManager = new DatabaseManager();
        dbManager.initDatabase();
        return dbManager;
    }

    @Bean
    public DataProvider dataProvider() {
        return new YahooFinanceProvider();
    }

    /**
     * Configure thread pool for async process execution.
     * Optimized for low-latency trading system requirements.
     */
    @Bean(name = "processExecutor")
    public Executor processExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // Core pool size: number of threads to keep alive
        executor.setCorePoolSize(4);

        // Maximum pool size: maximum number of threads
        executor.setMaxPoolSize(8);

        // Queue capacity: number of tasks to queue before creating new threads
        executor.setQueueCapacity(100);

        // Thread name prefix for easier debugging
        executor.setThreadNamePrefix("ProcessExec-");

        // Keep alive time for idle threads (in seconds)
        executor.setKeepAliveSeconds(60);

        // Allow core threads to timeout and be removed when idle
        executor.setAllowCoreThreadTimeOut(true);

        // Rejection policy: caller runs the task if pool is full
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());

        // Wait for tasks to complete on shutdown
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);

        executor.initialize();
        return executor;
    }

    /**
     * Configure CORS to allow requests from React frontend.
     * Enables cross-origin requests from the React development server.
     */
    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/api/**")
                        .allowedOrigins(
                            "http://localhost:3000",  // React development server
                            "http://127.0.0.1:3000",  // Alternative localhost
                            "http://localhost:3001"   // Alternative React port
                        )
                        .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
                        .allowedHeaders("*")
                        .allowCredentials(true)
                        .maxAge(3600); // Cache preflight response for 1 hour
            }
        };
    }
}
