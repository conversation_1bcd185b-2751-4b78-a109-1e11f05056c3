package com.investment.config;

import java.time.LocalDate;

/**
 * Configuration class for market data download operations.
 * Provides configurable parameters for historical data downloading.
 */
public class MarketDataDownloadConfig {
    
    // Yahoo Finance's historical data typically starts from January 2, 1962
    public static final int YAHOO_HISTORICAL_DATA_START_YEAR = 1962;
    
    // Default number of years to look back for historical data
    public static final int DEFAULT_HISTORY_YEARS = LocalDate.now().getYear() - YAHOO_HISTORICAL_DATA_START_YEAR - 1;
    
    // Configuration parameters
    private LocalDate startDate;
    private LocalDate endDate;
    private boolean continueOnError;
    private int maxRetries;
    private long retryDelayMs;
    private boolean skipExistingData;
    
    /**
     * Default constructor with sensible defaults.
     */
    public MarketDataDownloadConfig() {
        this.startDate = LocalDate.now().minusYears(DEFAULT_HISTORY_YEARS);
        this.endDate = LocalDate.now();
        this.continueOnError = true;
        this.maxRetries = 3;
        this.retryDelayMs = 1000; // 1 second
        this.skipExistingData = true;
    }
    
    /**
     * Constructor with custom date range.
     */
    public MarketDataDownloadConfig(LocalDate startDate, LocalDate endDate) {
        this();
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    /**
     * Get the start date for historical data download.
     * If null, will be calculated based on existing data for each symbol.
     */
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
    
    /**
     * Get the end date for historical data download.
     */
    public LocalDate getEndDate() {
        return endDate;
    }
    
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
    
    /**
     * Whether to continue processing other symbols if one fails.
     */
    public boolean isContinueOnError() {
        return continueOnError;
    }
    
    public void setContinueOnError(boolean continueOnError) {
        this.continueOnError = continueOnError;
    }
    
    /**
     * Maximum number of retries for failed downloads.
     */
    public int getMaxRetries() {
        return maxRetries;
    }
    
    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }
    
    /**
     * Delay between retries in milliseconds.
     */
    public long getRetryDelayMs() {
        return retryDelayMs;
    }
    
    public void setRetryDelayMs(long retryDelayMs) {
        this.retryDelayMs = retryDelayMs;
    }
    
    /**
     * Whether to skip downloading data that already exists.
     * If true, will only download data from the last available date.
     */
    public boolean isSkipExistingData() {
        return skipExistingData;
    }
    
    public void setSkipExistingData(boolean skipExistingData) {
        this.skipExistingData = skipExistingData;
    }
    
    /**
     * Create a configuration for downloading all historical data.
     */
    public static MarketDataDownloadConfig fullHistoricalData() {
        MarketDataDownloadConfig config = new MarketDataDownloadConfig();
        config.setStartDate(LocalDate.of(YAHOO_HISTORICAL_DATA_START_YEAR, 1, 2));
        config.setSkipExistingData(false);
        return config;
    }
    
    /**
     * Create a configuration for updating recent data only.
     */
    public static MarketDataDownloadConfig recentDataUpdate() {
        MarketDataDownloadConfig config = new MarketDataDownloadConfig();
        config.setStartDate(LocalDate.now().minusDays(30));
        config.setSkipExistingData(true);
        return config;
    }
    
    /**
     * Create a configuration for a specific date range.
     */
    public static MarketDataDownloadConfig dateRange(LocalDate startDate, LocalDate endDate) {
        return new MarketDataDownloadConfig(startDate, endDate);
    }
    
    @Override
    public String toString() {
        return "MarketDataDownloadConfig{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", continueOnError=" + continueOnError +
                ", maxRetries=" + maxRetries +
                ", retryDelayMs=" + retryDelayMs +
                ", skipExistingData=" + skipExistingData +
                '}';
    }
}
