# Bollinger Bands Calculation Modes

## Overview

The Bollinger Band service now supports three different calculation modes to optimize performance and provide flexibility for different use cases:

1. **INCREMENTAL** - Calculate only for new data points that haven't been processed yet
2. **FULL_RECALCULATION** - Calculate from the beginning for all data points, overwriting existing calculations
3. **SKIP_EXISTING** - Skip symbols that already have Bollinger Band data (legacy mode)

## Calculation Modes

### INCREMENTAL Mode (Default)

This is the new default mode that provides optimal performance for scenarios where only recent data needs processing.

**How it works:**
- Identifies the second-to-last date with existing Bollinger Band data for each symbol (N-1 reference point)
- Recalculates Bollinger Bands starting from the second-to-last calculated date onwards
- This ensures the most recent calculation (N) is updated using the N-1 reference point
- Automatically falls back to full calculation if insufficient existing data is found (less than 2 records)
- Uses a sliding window approach to ensure accurate calculations

**Use cases:**
- Daily data updates after market close
- Processing new data points without recalculating historical data
- Optimal for production environments with regular data updates

**Example:**
```json
{
  "calculationMode": "INCREMENTAL",
  "period": 20,
  "stdDevMultiplier": 2.0
}
```

### FULL_RECALCULATION Mode

This mode recalculates all Bollinger Band data from scratch, ensuring complete data integrity.

**How it works:**
- Clears all existing Bollinger Band data for each symbol
- Recalculates from the very beginning using all available historical data
- Provides the most accurate results but takes longer to process

**Use cases:**
- Data integrity verification
- After changing calculation parameters (period, standard deviation)
- Initial setup or data migration scenarios
- When historical data has been corrected or updated

**Example:**
```json
{
  "calculationMode": "FULL_RECALCULATION",
  "period": 20,
  "stdDevMultiplier": 2.0
}
```

### SKIP_EXISTING Mode

This is the legacy mode that skips symbols already containing Bollinger Band data.

**How it works:**
- Checks if a symbol has any existing Bollinger Band data
- Skips processing if data exists, processes if no data exists
- Does not update or extend existing calculations

**Use cases:**
- One-time bulk calculations
- Avoiding overwriting existing calculations
- Backward compatibility with existing workflows

**Example:**
```json
{
  "calculationMode": "SKIP_EXISTING",
  "period": 20,
  "stdDevMultiplier": 2.0
}
```

## API Usage Examples

### Using the New Calculation Modes

#### Incremental Processing (Recommended for Regular Updates)
```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/bollinger-bands/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "calculationMode": "INCREMENTAL",
    "period": 20,
    "stdDevMultiplier": 2.0
  }'
```

#### Full Recalculation (Data Integrity)
```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/bollinger-bands/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "calculationMode": "FULL_RECALCULATION",
    "period": 20,
    "stdDevMultiplier": 2.0
  }'
```

#### Skip Existing (Legacy Behavior)
```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/bollinger-bands/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "calculationMode": "SKIP_EXISTING",
    "period": 20,
    "stdDevMultiplier": 2.0
  }'
```

## Backward Compatibility

The implementation maintains full backward compatibility with the existing `forceRecalculate` boolean parameter:

- `forceRecalculate: true` → Maps to `FULL_RECALCULATION` mode
- `forceRecalculate: false` → Maps to `SKIP_EXISTING` mode

**Legacy Example (Still Supported):**
```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/bollinger-bands/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "forceRecalculate": true,
    "period": 20,
    "stdDevMultiplier": 2.0
  }'
```

## Performance Characteristics

| Mode | Performance | Use Case | Data Integrity |
|------|-------------|----------|----------------|
| INCREMENTAL | ⭐⭐⭐⭐⭐ | Regular updates | ⭐⭐⭐⭐ |
| FULL_RECALCULATION | ⭐⭐ | Data verification | ⭐⭐⭐⭐⭐ |
| SKIP_EXISTING | ⭐⭐⭐ | One-time setup | ⭐⭐⭐ |

## Response Format

The response now includes the calculation mode used:

```json
{
  "status": "success",
  "processedSymbols": 150,
  "totalRecordsUpdated": 45000,
  "parameters": {
    "period": 20,
    "stdDevMultiplier": 2.0,
    "minDataPoints": 20,
    "calculationMode": "INCREMENTAL"
  },
  "processingTimeMs": 2500
}
```

## Technical Implementation

### Database Operations

- **INCREMENTAL**: Uses N-1 reference point and date-filtered SQL queries to recalculate from the second-to-last calculated date
- **FULL_RECALCULATION**: Clears existing data first, then performs complete calculation
- **SKIP_EXISTING**: Checks for existing data before processing

### Transactional Safety

All calculation modes maintain transactional safety:
- Operations are wrapped in database transactions
- Rollback occurs on any failure
- Consistent state is maintained even during interruptions

### Garbage-Free Implementation

The implementation follows garbage-free programming patterns:
- Object reuse for database operations
- Primitive collections where applicable
- Minimal object allocation in hot paths
