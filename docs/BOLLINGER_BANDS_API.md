# Bollinger Bands Technical Indicator API

## Overview

The Bollinger Bands API provides endpoints for calculating and updating Bollinger Band technical indicators for all symbols in the OHLCV database. This implementation uses optimized DuckDB SQL window functions for efficient bulk calculation and supports various configuration options.

## Endpoint

### Calculate Bollinger Bands

**POST** `/api/technical-indicators/bollinger-bands/calculate`

Calculates and updates Bollinger Band technical indicators for all symbols in the database.

#### Request Body

```json
{
  "period": 20,
  "stdDevMultiplier": 2.0,
  "dryRun": false,
  "maxSymbols": 0,
  "minDataPoints": 20,
  "forceRecalculate": false
}
```

#### Request Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `period` | integer | 20 | Number of periods for moving average calculation (5-200) |
| `stdDevMultiplier` | double | 2.0 | Standard deviation multiplier for upper/lower bands (0.5-5.0) |
| `dryRun` | boolean | false | If true, validate and report without updating data |
| `maxSymbols` | integer | 0 | Maximum symbols to process (0 = no limit, max 10000) |
| `minDataPoints` | integer | 20 | Minimum data points required for calculation (5-500) |
| `forceRecalculate` | boolean | false | Recalculate for symbols with existing Bollinger Band data |

#### Response

```json
{
  "success": true,
  "message": "Bollinger Band calculation completed successfully",
  "data": {
    "status": "success",
    "processedSymbols": 150,
    "totalRecordsUpdated": 45000,
    "symbolsWithInsufficientData": ["NEWCO"],
    "skippedSymbols": [],
    "failedSymbols": [],
    "processingTimeMs": 2500,
    "errors": [],
    "dryRun": false,
    "parameters": {
      "period": 20,
      "stdDevMultiplier": 2.0,
      "minDataPoints": 20
    },
    "timestamp": "2024-01-15T10:30:45",
    "summary": "Bollinger Band calculation completed: 150 symbols processed, 45000 records updated in 2500ms"
  }
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | string | Operation status: "success", "partial_success", or "failed" |
| `processedSymbols` | integer | Number of symbols successfully processed |
| `totalRecordsUpdated` | integer | Total OHLCV records updated with Bollinger Band data |
| `symbolsWithInsufficientData` | array | Symbols that had insufficient data for calculation |
| `skippedSymbols` | array | Symbols skipped (already have data and forceRecalculate=false) |
| `failedSymbols` | array | Symbols that failed during calculation |
| `processingTimeMs` | integer | Processing time in milliseconds |
| `errors` | array | Error messages encountered during processing |
| `dryRun` | boolean | Whether this was a dry run (no actual updates) |
| `parameters` | object | Calculation parameters used |
| `timestamp` | string | When the operation completed |
| `summary` | string | Human-readable summary of the operation |

## Technical Implementation

### Database Schema

The Bollinger Band data is stored in the existing `ohlcv` table with these additional columns:

- `bb_middle_band` DECIMAL(20,6) - Middle band (20-day SMA)
- `bb_std_dev` DECIMAL(20,6) - Standard deviation of closing prices
- `bb_upper_band` DECIMAL(20,6) - Upper band (middle + 2 * std dev)
- `bb_lower_band` DECIMAL(20,6) - Lower band (middle - 2 * std dev)

### Calculation Formula

The implementation uses DuckDB window functions for efficient calculation:

```sql
-- Middle Band: 20-day Simple Moving Average
AVG(close) OVER (
    PARTITION BY symbol
    ORDER BY date
    ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
) AS bb_middle_band

-- Standard Deviation: 20-day standard deviation
STDDEV(close) OVER (
    PARTITION BY symbol
    ORDER BY date
    ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
) AS bb_std_dev

-- Upper Band: Middle band + (2 * standard deviation)
bb_middle_band + (2.0 * bb_std_dev) AS bb_upper_band

-- Lower Band: Middle band - (2 * standard deviation)
bb_middle_band - (2.0 * bb_std_dev) AS bb_lower_band
```

### Performance Characteristics

- **Bulk Processing**: Processes all symbols in the database efficiently
- **Smart Symbol Selection**: Only processes symbols that actually have OHLCV data, skipping instruments without price data
- **Optimized SQL**: Uses window functions for single-pass calculation
- **Transaction Safety**: Each symbol is processed in its own transaction
- **Memory Efficient**: Streams data rather than loading everything into memory
- **Scalable**: Can handle thousands of symbols and millions of records
- **Intelligent Filtering**: Automatically optimizes processing when instruments table is larger than symbols with OHLCV data

## Usage Examples

### Basic Calculation

Calculate Bollinger Bands for all symbols with default parameters:

```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/bollinger-bands/calculate \
  -H "Content-Type: application/json" \
  -d '{}'
```

### Custom Parameters

Calculate with custom period and standard deviation:

```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/bollinger-bands/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "period": 14,
    "stdDevMultiplier": 2.5,
    "minDataPoints": 30
  }'
```

### Dry Run Validation

Validate calculation without updating data:

```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/bollinger-bands/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "dryRun": true,
    "maxSymbols": 10
  }'
```

### Force Recalculation

Recalculate for symbols that already have Bollinger Band data:

```bash
curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/bollinger-bands/calculate \
  -H "Content-Type: application/json" \
  -d '{
    "forceRecalculate": true,
    "period": 20,
    "stdDevMultiplier": 2.0
  }'
```

## Error Handling

The API handles various error conditions gracefully:

- **Insufficient Data**: Symbols with fewer than `minDataPoints` are skipped
- **Database Errors**: Individual symbol failures don't stop the entire operation
- **Validation Errors**: Invalid parameters return 400 Bad Request
- **System Errors**: Database connection issues return 500 Internal Server Error

## Monitoring and Logging

The API provides comprehensive logging for monitoring:

- **INFO**: Operation start/completion, symbol processing counts
- **DEBUG**: Individual symbol processing details
- **WARN**: Symbols with insufficient data or existing data
- **ERROR**: Calculation failures and database errors

## Integration with OHLCV Data

The calculated Bollinger Band data is seamlessly integrated with existing OHLCV data:

- **API Responses**: OHLCV endpoints automatically include Bollinger Band data when available
- **Data Model**: OHLCV entity includes nullable Bollinger Band fields
- **Backward Compatibility**: Existing code continues to work without changes

## Best Practices

1. **Use Dry Run First**: Always test with `dryRun: true` before actual calculation
2. **Monitor Processing Time**: Large datasets may take several minutes to process
3. **Check Data Quality**: Ensure sufficient historical data before calculation
4. **Regular Updates**: Recalculate periodically as new OHLCV data is added
5. **Error Handling**: Monitor failed symbols and investigate data quality issues
