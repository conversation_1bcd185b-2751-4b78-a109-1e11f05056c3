# DMI DX Column Migration - Schema Version 5

## Overview

This document describes the addition of the `dmi_dx` column to the `ohlcv` table to support the complete Directional Movement Index (DMI) technical indicator calculations. This extends the existing DMI implementation from Schema Version 4.

## New Column Added

The following column has been added to the `ohlcv` table:

| Column Name | Data Type | Description | Nullable |
|-------------|-----------|-------------|----------|
| `dmi_dx` | DECIMAL(20,6) | Directional Index (DX) values used in ADX calculation | YES |

## Schema Migration Details

### Version Information
- **Previous Schema Version**: 4 (DMI Columns: +DI, -DI, ADX)
- **New Schema Version**: 5 (DMI DX Column)
- **Migration Method**: `migrateToVersion5()`

### Migration Process

The migration automatically:

1. **Checks for existing column** to prevent duplicate additions
2. **Adds the dmi_dx column** using `ALTER TABLE` statement
3. **Updates the schema version** to 5
4. **Maintains backward compatibility** with existing data

### SQL Migration Command

```sql
ALTER TABLE ohlcv ADD COLUMN dmi_dx DECIMAL(20,6);
```

## Updated Table Schema

The complete `ohlcv` table now includes:

```sql
CREATE TABLE ohlcv (
    symbol VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    open DECIMAL(20,6) NOT NULL,
    high DECIMAL(20,6) NOT NULL,
    low DECIMAL(20,6) NOT NULL,
    close DECIMAL(20,6) NOT NULL,
    volume BIGINT NOT NULL,
    -- Bollinger Band columns (Schema v3)
    bb_middle_band DECIMAL(20,6),
    bb_std_dev DECIMAL(20,6),
    bb_upper_band DECIMAL(20,6),
    bb_lower_band DECIMAL(20,6),
    -- DMI columns (Schema v4 & v5)
    dmi_plus_di DECIMAL(20,6),
    dmi_minus_di DECIMAL(20,6),
    dmi_dx DECIMAL(20,6),
    dmi_adx DECIMAL(20,6),
    PRIMARY KEY (symbol, date),
    FOREIGN KEY (symbol) REFERENCES instruments(symbol)
);
```

## Code Changes

### 1. DatabaseManager Updates

#### Schema Version
```java
private static final int CURRENT_SCHEMA_VERSION = 5; // Updated from 4
```

#### Migration Method
```java
private void migrateToVersion5() throws SQLException {
    logger.info("Migrating to schema version 5 - adding dmi_dx column to OHLCV table");
    
    // Check if dmi_dx column already exists
    boolean columnExists = false;
    try (Statement checkStmt = connection.createStatement();
         ResultSet rs = checkStmt.executeQuery("SELECT dmi_dx FROM ohlcv LIMIT 0")) {
        columnExists = true;
    } catch (SQLException e) {
        columnExists = false;
    }

    if (!columnExists) {
        try (Statement stmt = connection.createStatement()) {
            stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_dx DECIMAL(20,6)");
            logger.info("Successfully added dmi_dx column to OHLCV table");
        }
    } else {
        logger.info("dmi_dx column already exists in OHLCV table");
    }
    
    updateSchemaVersion(5);
}
```

#### Updated SQL Operations
- **INSERT/UPDATE**: Now includes dmi_dx column in `saveOHLCVData()`
- **SELECT**: Now retrieves dmi_dx column in `getOHLCVData()`
- **CLEAR**: Now clears dmi_dx column in `clearDMIData()`
- **NULL Handling**: Proper handling of nullable dmi_dx values

### 2. OHLCV Model Updates

#### New Field
```java
private final Double dmiDx;
```

#### Updated Constructor
```java
// Full constructor with Bollinger Band and DMI data
public OHLCV(String symbol, LocalDate date, double open, double high, double low, double close, long volume,
             Double bbMiddleBand, Double bbStdDev, Double bbUpperBand, Double bbLowerBand,
             Double dmiPlusDi, Double dmiMinusDi, Double dmiDx, Double dmiAdx) {
    // ... field assignments including dmiDx
}
```

#### New Getter Method
```java
public Double getDmiDx() { return dmiDx; }
```

## Column Ordering

**Note**: Due to SQL limitations, the `dmi_dx` column is added at the end of the DMI columns rather than between `dmi_minus_di` and `dmi_adx` as originally requested. The actual column order in the database is:

1. `dmi_plus_di`
2. `dmi_minus_di`
3. `dmi_adx`
4. `dmi_dx` (newly added)

However, the OHLCV model class maintains the logical ordering in its constructor and getter methods.

## Backward Compatibility

### Existing Data
- **No data loss**: Existing records remain unchanged
- **NULL values**: New dmi_dx column is NULL for existing records
- **Gradual population**: DX values can be calculated and populated incrementally

### Existing Code
- **Constructor compatibility**: Old constructors still work via method overloading
- **API compatibility**: Existing API calls continue to function
- **Database operations**: All existing operations remain functional

## Usage Examples

### Creating OHLCV with Complete DMI Data
```java
// With all DMI values including DX
OHLCV ohlcvWithCompleteDMI = new OHLCV(
    "AAPL", LocalDate.now(), 150.0, 155.0, 148.0, 152.0, 1000000L,
    null, null, null, null,     // BB values
    25.5, 15.3, 35.8, 45.2      // DMI values: +DI, -DI, DX, ADX
);

// Without DX value (backward compatibility)
OHLCV ohlcvWithPartialDMI = new OHLCV(
    "AAPL", LocalDate.now(), 150.0, 155.0, 148.0, 152.0, 1000000L,
    null, null, null, null,     // BB values
    25.5, 15.3, null, 45.2      // DMI values: +DI, -DI, null DX, ADX
);
```

### Retrieving DMI Data with DX
```java
List<OHLCV> data = databaseManager.getOHLCVData("AAPL", startDate, endDate);
for (OHLCV record : data) {
    Double plusDI = record.getDmiPlusDi();
    Double minusDI = record.getDmiMinusDi();
    Double dx = record.getDmiDx();        // New DX value
    Double adx = record.getDmiAdx();
    
    if (dx != null) {
        // Process DX data for advanced DMI analysis
    }
}
```

## Technical Implementation Notes

### Current DMI Calculation
The existing DMI calculation logic in `DatabaseManager.storeDMIResults()` currently sets `dmi_dx` to NULL since the current implementation doesn't calculate intermediate DX values. Future enhancements can:

1. Modify the DMI calculation algorithm to capture DX values
2. Update the `storeDMIResults()` method to accept and store DX values
3. Provide complete DMI data including the intermediate DX calculations

### Future Enhancements
- **DX Calculation**: Implement DX value calculation and storage
- **DMI Service**: Update DMI service to handle DX values
- **API Responses**: Include DX values in REST API responses

## Verification Commands

```sql
-- Check schema version
SELECT version FROM schema_version;

-- Verify dmi_dx column exists
DESCRIBE ohlcv;

-- Check for DMI data including DX
SELECT symbol, date, dmi_plus_di, dmi_minus_di, dmi_dx, dmi_adx 
FROM ohlcv 
WHERE dmi_plus_di IS NOT NULL 
LIMIT 10;
```

## Conclusion

Schema Version 5 successfully adds the `dmi_dx` column to support complete DMI technical indicator calculations while maintaining full backward compatibility with existing code and data.
