# Close Price Column Migration - Investment Toolkit

## Overview

This document describes the implementation of a new `close_price` column in the positions table to track the closing price when positions are sold/closed. This enhancement enables better realized P&L calculations and position tracking.

## Database Changes

### Schema Migration (Version 8 → 9)

**File**: `src/main/java/com/investment/database/DatabaseManager.java`

- Updated `CURRENT_SCHEMA_VERSION` from 8 to 9
- Added `migrateToVersion9()` method to add the `close_price` column
- Column specification: `DECIMAL(20,6)` (matches other price columns)
- Allows NULL values for existing positions

```sql
ALTER TABLE positions ADD COLUMN close_price DECIMAL(20,6);
```

### Updated Database Operations

**Modified Methods**:
- `updatePosition()` - Added `closePrice` parameter
- `getPositionById()` - Added `close_price` to SELECT query
- `getPositions()` - Added `close_price` to SELECT query

## Domain Model Changes

### Position.java

**File**: `src/main/java/com/investment/model/Position.java`

**Added**:
- `private BigDecimal closePrice` field
- `getClosePrice()` and `setClosePrice()` methods
- Overloaded `close(BigDecimal closePrice)` method

**Enhanced Functionality**:
- Positions can now be closed with a specific close price
- Maintains backward compatibility with existing `close()` method

## API Model Changes

### PositionResponse.java

**File**: `src/main/java/com/investment/api/model/PositionResponse.java`

**Added**:
- `closePrice` field with OpenAPI documentation
- Getter and setter methods
- Updated constructor to map from Position entity

### UpdatePositionRequest.java

**File**: `src/main/java/com/investment/api/model/UpdatePositionRequest.java`

**Added**:
- `closePrice` field with validation (`@DecimalMin`)
- Getter and setter methods
- Updated `hasUpdates()` method to include closePrice
- Updated `toString()` method

## Service Layer Changes

### PositionsService.java

**File**: `src/main/java/com/investment/service/PositionsService.java`

**Modified Methods**:
- `updatePosition()` - Handles closePrice from UpdatePositionRequest
- `updatePositionPrice()` - Updated database call signature
- `closePosition()` - Updated database call signature
- `mapDataToPosition()` - Maps close_price from database

**Added Methods**:
- `closePosition(Long id, BigDecimal closePrice)` - Close with specific price

## Usage Examples

### Close Position with Price

```java
// Service layer
Position closedPosition = positionsService.closePosition(1L, new BigDecimal("155.75"));

// REST API
PUT /api/positions/1
{
  "status": "CLOSED",
  "closePrice": 155.75
}
```

### Update Position with Close Price

```java
UpdatePositionRequest request = new UpdatePositionRequest();
request.setClosePrice(new BigDecimal("155.75"));
request.setStatus(Position.Status.CLOSED);

Position updated = positionsService.updatePosition(1L, request);
```

## Database Schema

### Updated Positions Table Structure

```sql
CREATE TABLE positions (
    id BIGINT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    position DECIMAL(20,6) NOT NULL,
    side VARCHAR(4) NOT NULL CHECK (side IN ('BUY', 'SELL')),
    status VARCHAR(6) NOT NULL CHECK (status IN ('OPEN', 'CLOSED')),
    trade_price DECIMAL(20,6) NOT NULL,
    trade_value DECIMAL(20,6) NOT NULL,
    init_portfolio_net_value DECIMAL(20,6),
    last_price DECIMAL(20,6),
    last_value DECIMAL(20,6),
    risk_unit DECIMAL(20,6),
    stop_percent DECIMAL(10,6),
    highest_after_trade DECIMAL(20,6),
    stop_value_from_highest DECIMAL(20,6),
    last_bbmb DECIMAL(20,6),
    bbmb_adj_percent DECIMAL(10,6),
    stop_value_from_bbmb DECIMAL(20,6),
    expand_or_contract VARCHAR(11) CHECK (expand_or_contract IN ('EXPANDING', 'CONTRACTING')),
    effective_stop_value DECIMAL(20,6),
    pnl_value DECIMAL(20,6),
    pnl_percent DECIMAL(10,6),
    close_price DECIMAL(20,6),  -- NEW COLUMN
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (symbol) REFERENCES instruments(symbol)
);
```

## Migration Safety

- **Backward Compatible**: Existing positions will have NULL close_price
- **Non-Breaking**: All existing API calls continue to work
- **Graceful Degradation**: Applications can handle NULL close_price values
- **Incremental Adoption**: Close price can be set when positions are closed

## Benefits

1. **Enhanced P&L Tracking**: Accurate realized P&L calculations
2. **Better Reporting**: Clear distinction between entry and exit prices
3. **Audit Trail**: Complete position lifecycle tracking
4. **Performance Analysis**: Compare entry vs exit execution quality

## Future Enhancements

- Calculate realized P&L using close_price when available
- Add close_date timestamp for complete position lifecycle
- Implement position performance analytics
- Add close_price validation against market data

## Testing Recommendations

1. Test migration on existing database with positions
2. Verify NULL close_price handling in all API responses
3. Test position closing with and without close_price
4. Validate close_price persistence and retrieval
5. Test UpdatePositionRequest with close_price field
