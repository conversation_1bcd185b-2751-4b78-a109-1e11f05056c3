# DMI Columns Migration - Schema Version 4

## Overview

This document describes the addition of three new columns to the `ohlcv` table to support the Directional Movement Index (DMI) technical indicator calculations. This follows the same pattern as the previously implemented Bollinger Band columns.

## New Columns Added

The following three columns have been added to the `ohlcv` table:

| Column Name | Data Type | Description | Nullable |
|-------------|-----------|-------------|----------|
| `dmi_plus_di` | DECIMAL(20,6) | Plus Directional Indicator (+DI) values | YES |
| `dmi_minus_di` | DECIMAL(20,6) | Minus Directional Indicator (-DI) values | YES |
| `dmi_adx` | DECIMAL(20,6) | Average Directional Index (ADX) values | YES |

## Schema Migration Details

### Version Information
- **Previous Schema Version**: 3 (Bollinger Bands)
- **New Schema Version**: 4 (DMI Columns)
- **Migration Method**: `migrateToVersion4()`

### Migration Process

The migration automatically:

1. **Checks for existing columns** to prevent duplicate additions
2. **Adds the three DMI columns** using `ALTER TABLE` statements
3. **Updates the schema version** to 4
4. **Maintains backward compatibility** with existing data

### SQL Migration Commands

```sql
ALTER TABLE ohlcv ADD COLUMN dmi_plus_di DECIMAL(20,6);
ALTER TABLE ohlcv ADD COLUMN dmi_minus_di DECIMAL(20,6);
ALTER TABLE ohlcv ADD COLUMN dmi_adx DECIMAL(20,6);
```

## Updated Table Schema

The complete `ohlcv` table now includes:

```sql
CREATE TABLE ohlcv (
    symbol VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    open DECIMAL(20,6) NOT NULL,
    high DECIMAL(20,6) NOT NULL,
    low DECIMAL(20,6) NOT NULL,
    close DECIMAL(20,6) NOT NULL,
    volume BIGINT NOT NULL,
    -- Bollinger Band columns (Schema v3)
    bb_middle_band DECIMAL(20,6),
    bb_std_dev DECIMAL(20,6),
    bb_upper_band DECIMAL(20,6),
    bb_lower_band DECIMAL(20,6),
    -- DMI columns (Schema v4)
    dmi_plus_di DECIMAL(20,6),
    dmi_minus_di DECIMAL(20,6),
    dmi_adx DECIMAL(20,6),
    PRIMARY KEY (symbol, date),
    FOREIGN KEY (symbol) REFERENCES instruments(symbol)
);
```

## Code Changes

### 1. DatabaseManager Updates

#### Schema Version
```java
private static final int CURRENT_SCHEMA_VERSION = 4; // Updated from 3
```

#### Migration Method
```java
private void migrateToVersion4() throws SQLException {
    logger.info("Migrating to schema version 4 - adding DMI columns to OHLCV table");
    
    // Check if DMI columns already exist
    boolean columnsExist = false;
    try (Statement checkStmt = connection.createStatement();
         ResultSet rs = checkStmt.executeQuery("SELECT dmi_plus_di FROM ohlcv LIMIT 0")) {
        columnsExist = true;
    } catch (SQLException e) {
        columnsExist = false;
    }

    if (!columnsExist) {
        try (Statement stmt = connection.createStatement()) {
            stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_plus_di DECIMAL(20,6)");
            stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_minus_di DECIMAL(20,6)");
            stmt.execute("ALTER TABLE ohlcv ADD COLUMN dmi_adx DECIMAL(20,6)");
            logger.info("Successfully added DMI columns to OHLCV table");
        }
    }
    
    updateSchemaVersion(4);
}
```

#### Updated SQL Operations
- **INSERT/UPDATE**: Now includes DMI columns in `saveOHLCVData()`
- **SELECT**: Now retrieves DMI columns in `getOHLCVData()`
- **NULL Handling**: Proper handling of nullable DMI values

### 2. OHLCV Model Updates

#### New Fields
```java
private final Double dmiPlusDi;
private final Double dmiMinusDi;
private final Double dmiAdx;
```

#### Updated Constructors
```java
// Backward compatibility constructor
public OHLCV(String symbol, LocalDate date, double open, double high, double low, double close, long volume) {
    this(symbol, date, open, high, low, close, volume, null, null, null, null, null, null, null);
}

// Full constructor with all technical indicators
public OHLCV(String symbol, LocalDate date, double open, double high, double low, double close, long volume,
             Double bbMiddleBand, Double bbStdDev, Double bbUpperBand, Double bbLowerBand,
             Double dmiPlusDi, Double dmiMinusDi, Double dmiAdx) {
    // Implementation...
}
```

#### New Getter Methods
```java
public Double getDmiPlusDi() { return dmiPlusDi; }
public Double getDmiMinusDi() { return dmiMinusDi; }
public Double getDmiAdx() { return dmiAdx; }
```

## Backward Compatibility

### Existing Data
- **No data loss**: Existing records remain unchanged
- **NULL values**: New DMI columns are NULL for existing records
- **Gradual population**: DMI values can be calculated and populated incrementally

### Existing Code
- **Constructor compatibility**: Old constructors still work
- **API compatibility**: Existing API calls continue to function
- **Database operations**: All existing operations remain functional

## Usage Examples

### Creating OHLCV with DMI Data
```java
// With DMI values
OHLCV ohlcvWithDMI = new OHLCV(
    "AAPL", LocalDate.now(), 150.0, 155.0, 148.0, 152.0, 1000000L,
    null, null, null, null,  // BB values
    25.5, 15.3, 45.2         // DMI values: +DI, -DI, ADX
);

// Without DMI values (backward compatibility)
OHLCV basicOHLCV = new OHLCV(
    "AAPL", LocalDate.now(), 150.0, 155.0, 148.0, 152.0, 1000000L
);
```

### Retrieving DMI Data
```java
List<OHLCV> data = databaseManager.getOHLCVData("AAPL", startDate, endDate);
for (OHLCV record : data) {
    Double plusDI = record.getDmiPlusDi();
    Double minusDI = record.getDmiMinusDi();
    Double adx = record.getDmiAdx();
    
    if (plusDI != null) {
        // Process DMI data
    }
}
```

## Testing

### Migration Tests
- **Schema version verification**: Confirms migration to version 4
- **Column existence**: Verifies all three DMI columns are present
- **Data integrity**: Ensures existing data remains intact
- **NULL handling**: Tests proper handling of NULL DMI values

### Integration Tests
- **Mixed data**: Tests records with both BB and DMI data
- **Backward compatibility**: Verifies old constructors still work
- **Update operations**: Tests updating existing records with DMI data

## Performance Considerations

### Database Impact
- **Minimal overhead**: Three additional DECIMAL columns per record
- **Efficient storage**: DuckDB handles NULL values efficiently
- **Index compatibility**: Primary key and foreign key constraints unchanged

### Memory Usage
- **Garbage-free patterns**: Follows existing object reuse patterns
- **Primitive handling**: Uses Double wrapper for nullable values
- **Batch operations**: Maintains efficient batch insert/update operations

## Future Enhancements

### DMI Service Implementation
The new columns prepare the foundation for:
- **DMI calculation service**: Similar to BollingerBandService
- **Incremental calculations**: Following the same patterns as Bollinger Bands
- **REST API endpoints**: For DMI calculation and retrieval
- **Technical analysis**: Integration with other technical indicators

### Additional Technical Indicators
The pattern established can be extended for:
- **RSI (Relative Strength Index)**: Single column
- **MACD**: Multiple columns for MACD line, signal line, histogram
- **Stochastic Oscillator**: %K and %D columns
- **Williams %R**: Single column

## Troubleshooting

### Common Issues

1. **Migration not running**: Check database connection and permissions
2. **Column already exists**: Migration safely handles existing columns
3. **NULL pointer exceptions**: Ensure proper NULL checking for DMI values
4. **Performance degradation**: Monitor query performance with additional columns

### Verification Commands

```sql
-- Check schema version
SELECT version FROM schema_version;

-- Verify DMI columns exist
DESCRIBE ohlcv;

-- Check for DMI data
SELECT symbol, date, dmi_plus_di, dmi_minus_di, dmi_adx 
FROM ohlcv 
WHERE dmi_plus_di IS NOT NULL 
LIMIT 10;
```

## Conclusion

The DMI columns migration successfully extends the `ohlcv` table to support Directional Movement Index calculations while maintaining full backward compatibility and following established patterns from the Bollinger Band implementation. The migration is safe, efficient, and prepares the foundation for comprehensive DMI technical analysis capabilities.
