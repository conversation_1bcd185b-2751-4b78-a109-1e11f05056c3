# Symbol Validation and Cleanup Feature

## Overview

The InvestmentToolKitV2 now includes a comprehensive symbol validation and cleanup feature that validates instrument symbols against official SEC (Securities and Exchange Commission) data. This ensures that only officially recognized U.S. stock symbols are maintained in the database, improving data quality and compliance.

## Features

### ✅ **SEC Data Integration**
- Downloads official SEC company tickers from: `https://www.sec.gov/files/company_tickers.json`
- Caches data locally for 24 hours to minimize API calls
- Supports force refresh for immediate updates

### ✅ **Symbol Validation**
- Compares database symbols against authoritative SEC ticker data
- Identifies invalid symbols that don't exist in SEC records
- Provides detailed validation reports

### ✅ **Data Cleanup**
- Safely removes invalid symbols and associated OHLCV data
- Maintains referential integrity during cleanup operations
- Transactional operations with rollback capability

### ✅ **Safety Features**
- **Dry-run mode**: Preview what would be deleted without making changes
- **Comprehensive logging**: Full audit trail of all operations
- **Error handling**: Graceful handling of network and database errors

## API Endpoints

### 1. Dry-Run Validation (Safe)
```http
GET /investment-toolkit/api/instruments/validate-symbols?forceRefresh=false
```

**Description**: Performs validation without making any changes to the database.

**Parameters**:
- `forceRefresh` (optional, default: false): Force download fresh SEC data

**Response Example**:
```json
{
  "success": true,
  "message": "Symbol validation completed",
  "data": {
    "totalSymbolsInDatabase": 150,
    "validSymbols": 145,
    "invalidSymbols": 5,
    "invalidSymbolsList": ["INVALID1", "FAKE2", "OLD3", "DELISTED4", "WRONG5"],
    "deletedSymbols": 0,
    "deletedOhlcvRecords": 0,
    "dryRun": true,
    "timestamp": "2025-05-29T00:50:00",
    "summary": "DRY RUN: Found 5 invalid symbols out of 150 total. Would delete 5 instruments and 1250 OHLCV records."
  }
}
```

### 2. Validation with Cleanup Options
```http
POST /investment-toolkit/api/instruments/validate-symbols
Content-Type: application/json

{
  "dryRun": true,
  "forceRefresh": false
}
```

**Description**: Performs validation with configurable cleanup options.

**Request Body**:
- `dryRun` (default: true): If false, performs actual cleanup
- `forceRefresh` (default: false): Force download fresh SEC data

**Response Example** (Actual Cleanup):
```json
{
  "success": true,
  "message": "Symbol cleanup completed",
  "data": {
    "totalSymbolsInDatabase": 150,
    "validSymbols": 145,
    "invalidSymbols": 5,
    "invalidSymbolsList": ["INVALID1", "FAKE2", "OLD3", "DELISTED4", "WRONG5"],
    "deletedSymbols": 5,
    "deletedOhlcvRecords": 1250,
    "dryRun": false,
    "timestamp": "2025-05-29T00:55:00",
    "summary": "CLEANUP COMPLETED: Deleted 5 invalid symbols out of 150 total. Removed 5 instruments and 1250 OHLCV records."
  }
}
```

### 3. Cache Status
```http
GET /investment-toolkit/api/instruments/sec-cache-status
```

**Description**: Returns information about the SEC data cache.

**Response Example**:
```json
{
  "success": true,
  "message": "Cache status retrieved",
  "data": {
    "cacheDirectory": "./data/sec_cache",
    "cacheFile": "company_tickers.json",
    "cacheExpiry": "PT24H",
    "lastCacheUpdate": "2025-05-29T00:30:00",
    "cachedTickersCount": 8500,
    "cacheFileExists": true
  }
}
```

## Usage Examples

### 1. Safe Validation Check
```bash
# Check what would be cleaned up without making changes
curl -X GET "http://localhost:8080/investment-toolkit/api/instruments/validate-symbols"
```

### 2. Force Fresh SEC Data
```bash
# Get latest SEC data and perform validation
curl -X GET "http://localhost:8080/investment-toolkit/api/instruments/validate-symbols?forceRefresh=true"
```

### 3. Actual Cleanup (CAUTION!)
```bash
# Perform actual cleanup - THIS WILL DELETE DATA
curl -X POST "http://localhost:8080/investment-toolkit/api/instruments/validate-symbols" \
  -H "Content-Type: application/json" \
  -d '{"dryRun": false, "forceRefresh": false}'
```

### 4. Check Cache Status
```bash
# Monitor cache status
curl -X GET "http://localhost:8080/investment-toolkit/api/instruments/sec-cache-status"
```

## Safety Considerations

### ⚠️ **Important Warnings**

1. **Always run dry-run first**: Use `dryRun: true` to preview changes before actual cleanup
2. **Backup your data**: Consider backing up the database before running cleanup operations
3. **Review invalid symbols**: Check the `invalidSymbolsList` to ensure they should actually be removed
4. **Network dependency**: The feature requires internet access to download SEC data

### 🔒 **Built-in Safety Features**

- **Default dry-run mode**: All operations default to dry-run unless explicitly set to false
- **Transactional operations**: Database changes are atomic - either all succeed or all fail
- **Comprehensive logging**: All operations are logged for audit purposes
- **Error recovery**: Failed operations are rolled back automatically

## Technical Details

### Data Source
- **SEC Company Tickers**: Official U.S. Securities and Exchange Commission data
- **URL**: https://www.sec.gov/files/company_tickers.json
- **Update Frequency**: Updated by SEC as needed
- **Cache Duration**: 24 hours locally

### Database Operations
- **Referential Integrity**: Automatically handles foreign key constraints
- **Batch Operations**: Efficient bulk deletion for multiple symbols
- **Transaction Safety**: All operations are wrapped in database transactions

### Performance
- **Caching**: SEC data is cached locally to minimize network requests
- **Batch Processing**: Multiple symbols are processed efficiently in single transactions
- **Memory Efficient**: Streaming JSON parsing for large SEC datasets

## Monitoring and Troubleshooting

### Log Messages
The feature provides comprehensive logging at different levels:

```
INFO  - Starting symbol validation - dryRun: true, forceRefresh: false
INFO  - Loaded 8500 SEC tickers for validation
INFO  - Found 150 symbols in database
INFO  - Found 5 invalid symbols: [INVALID1, FAKE2, OLD3, DELISTED4, WRONG5]
WARN  - PERFORMING ACTUAL CLEANUP - This will permanently delete invalid symbols
INFO  - Cleanup completed: deleted 5 symbols and 1250 OHLCV records
```

### Common Issues

1. **Network Errors**: Check internet connectivity if SEC data download fails
2. **Cache Issues**: Delete `./data/sec_cache/` directory to force fresh download
3. **Database Locks**: Ensure no other processes are accessing the database during cleanup

### File Locations
- **Database**: `./data/marketdata.duckdb`
- **SEC Cache**: `./data/sec_cache/company_tickers.json`
- **Logs**: `logs/investment-toolkit.log`

## Integration with Existing Features

The symbol validation feature integrates seamlessly with existing functionality:

- **Database Schema**: Uses the enhanced instruments table with new fields
- **REST API**: Follows the same patterns as existing OHLCV endpoints
- **Error Handling**: Uses the global exception handler
- **Logging**: Integrates with the existing SLF4J logging framework
- **Testing**: Comprehensive Spock test coverage

This feature ensures your investment data maintains the highest quality by validating against authoritative financial data sources.
