@echo off
echo Testing Close Price API Functionality
echo =====================================

echo.
echo 1. Creating a test position...
curl -X POST "http://localhost:8080/investment-toolkit/api/positions" ^
  -H "Content-Type: application/json" ^
  -d "{\"symbol\":\"AAPL\",\"position\":100,\"side\":\"BUY\",\"tradePrice\":150.00,\"initPortfolioNetValue\":100000.00,\"riskUnit\":1000.00,\"stopPercent\":0.02}"

echo.
echo.
echo 2. Getting all positions to see the created position...
curl -X GET "http://localhost:8080/investment-toolkit/api/positions"

echo.
echo.
echo 3. Updating position with close price (closing the position)...
curl -X PUT "http://localhost:8080/investment-toolkit/api/positions/1" ^
  -H "Content-Type: application/json" ^
  -d "{\"status\":\"CLOSED\",\"closePrice\":155.75}"

echo.
echo.
echo 4. Getting the updated position to verify close price was saved...
curl -X GET "http://localhost:8080/investment-toolkit/api/positions/1"

echo.
echo.
echo Test completed!
pause
