# Process Management System for Investment Toolkit

## Overview

The Investment Toolkit now includes a comprehensive process management system designed to track, monitor, and control long-running operations. This system provides robust support for:

- **Process Tracking**: Monitor active processes with detailed metadata
- **Cancellation Support**: Gracefully abort running operations
- **Progress Reporting**: Real-time progress updates with percentage completion
- **Resource Management**: Automatic cleanup and memory management
- **Thread Safety**: Concurrent access support for multi-threaded environments
- **REST API Integration**: Full API support for process management operations

## Architecture

### Core Components

#### 1. ProcessInfo
- **Purpose**: Immutable metadata container for process information
- **Features**: Thread-safe progress tracking, duration calculation, status management
- **Key Fields**: Process ID, type, status, progress percentage, start/end times

#### 2. ProcessContext
- **Purpose**: Thread-safe execution context for running processes
- **Features**: Cancellation signaling, progress callbacks, operation updates
- **Usage**: Passed to long-running operations for status reporting

#### 3. ProcessManager
- **Purpose**: Central registry for all active and completed processes
- **Features**: Process registration, abort operations, statistics, cleanup
- **Thread Safety**: Concurrent access using ConcurrentHashMap

#### 4. AsyncProcessExecutor
- **Purpose**: Spring-based async execution framework
- **Features**: CompletableFuture integration, exception handling, process lifecycle
- **Configuration**: Custom thread pool optimized for low-latency requirements

### Process Types

The system supports the following operation types:

- **DMI_CALCULATION**: Directional Movement Index calculations
- **BOLLINGER_BANDS_CALCULATION**: Bollinger Bands technical indicators
- **OHLCV_REFRESH**: Market data refresh operations
- **SEC_SYNCHRONIZATION**: SEC data synchronization
- **CSV_INSTRUMENT_UPLOAD**: CSV file processing
- **SYMBOL_VALIDATION**: Symbol validation operations
- **BULK_DATA_PROCESSING**: Generic bulk operations

### Process States

- **RUNNING**: Process is actively executing
- **COMPLETED**: Process finished successfully
- **FAILED**: Process terminated due to error
- **ABORTED**: Process was cancelled by user/system
- **ABORTING**: Process is in the process of being cancelled

## REST API Endpoints

### Process Management Controller (`/api/processes`)

#### 1. Abort All Processes
```http
POST /investment-toolkit/api/processes/abort-all
```
**Purpose**: Request cancellation of all active processes
**Response**: Summary of abort operations with success/failure details

#### 2. Get All Processes
```http
GET /investment-toolkit/api/processes
GET /investment-toolkit/api/processes?status=RUNNING
GET /investment-toolkit/api/processes?type=DMI_CALCULATION
```
**Purpose**: Retrieve process information with optional filtering
**Parameters**: 
- `status`: Filter by ProcessStatus
- `type`: Filter by ProcessType

#### 3. Get Active Processes
```http
GET /investment-toolkit/api/processes/active
```
**Purpose**: Retrieve only currently running processes

#### 4. Get Specific Process
```http
GET /investment-toolkit/api/processes/{processId}
```
**Purpose**: Get detailed information about a specific process

#### 5. Abort Specific Process
```http
POST /investment-toolkit/api/processes/{processId}/abort
```
**Purpose**: Request cancellation of a specific process

#### 6. Get Process Statistics
```http
GET /investment-toolkit/api/processes/statistics
```
**Purpose**: Retrieve system-wide process statistics

#### 7. Cleanup Completed Processes
```http
POST /investment-toolkit/api/processes/cleanup
```
**Purpose**: Remove completed processes from memory

### Enhanced Technical Indicator Endpoints

#### Async DMI Calculation
```http
POST /investment-toolkit/api/technical-indicators/dmi/calculate-async
```
**Purpose**: Start DMI calculation asynchronously with process tracking
**Response**: Process information for monitoring progress

## Integration Examples

### 1. DMI Service Integration

The DMI service now supports both synchronous and asynchronous execution:

```java
// Synchronous execution (existing)
DMIResponse response = dmiService.calculateDMI(request);

// Asynchronous execution with process tracking
CompletableFuture<ProcessExecutionResult<DMIResponse>> future = 
    dmiService.calculateDMIAsync(request, "admin");

// Start async and get process ID immediately
String processId = dmiService.startDMICalculationAsync(request, "admin");
```

### 2. Process-Aware Operations

Long-running operations can now report progress and check for cancellation:

```java
private DMIResponse calculateDMIWithProcessTracking(DMIRequest request, ProcessContext context) 
        throws ProcessContext.ProcessCancelledException {
    
    context.updateCurrentOperation("Retrieving symbols from database");
    List<String> symbols = getSymbols();
    
    context.updateProgress(0, symbols.size(), "Starting calculations");
    
    for (int i = 0; i < symbols.size(); i++) {
        // Check for cancellation before processing each symbol
        context.throwIfCancellationRequested();
        
        String symbol = symbols.get(i);
        context.updateCurrentOperation("Processing " + symbol);
        
        // Perform calculation
        processSymbol(symbol);
        
        // Update progress
        context.updateProgress(i + 1, symbols.size());
    }
    
    context.markCompleted();
    return response;
}
```

## Configuration

### Thread Pool Configuration

The system uses a custom thread pool optimized for low-latency trading requirements:

```java
@Bean(name = "processExecutor")
public Executor processExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(4);           // Core threads
    executor.setMaxPoolSize(8);            // Maximum threads
    executor.setQueueCapacity(100);        // Queue size
    executor.setThreadNamePrefix("ProcessExec-");
    executor.setKeepAliveSeconds(60);      // Thread timeout
    executor.setAllowCoreThreadTimeOut(true);
    return executor;
}
```

### Spring Configuration

Enable async processing in your configuration:

```java
@Configuration
@EnableAsync
public class AppConfig {
    // Configuration beans
}
```

## Usage Patterns

### 1. Emergency Process Termination

```bash
# Abort all active processes
curl -X POST http://localhost:8080/investment-toolkit/api/processes/abort-all

# Check which processes were aborted
curl http://localhost:8080/investment-toolkit/api/processes?status=ABORTED
```

### 2. Monitoring Long-Running Operations

```bash
# Start async DMI calculation
PROCESS_ID=$(curl -X POST http://localhost:8080/investment-toolkit/api/technical-indicators/dmi/calculate-async \
  -H "Content-Type: application/json" \
  -d '{"period": 14, "dryRun": false}' | jq -r '.data.processId')

# Monitor progress
curl http://localhost:8080/investment-toolkit/api/processes/$PROCESS_ID

# Abort if needed
curl -X POST http://localhost:8080/investment-toolkit/api/processes/$PROCESS_ID/abort
```

### 3. System Maintenance

```bash
# Get system statistics
curl http://localhost:8080/investment-toolkit/api/processes/statistics

# Clean up completed processes
curl -X POST http://localhost:8080/investment-toolkit/api/processes/cleanup
```

## Performance Characteristics

### Memory Management
- Automatic cleanup of completed processes
- Configurable retention policies
- Memory-efficient process tracking

### Latency Optimization
- Sub-microsecond process registration
- Lock-free data structures where possible
- Optimized thread pool configuration

### Scalability
- Concurrent process execution
- Thread-safe operations
- Configurable resource limits

## Error Handling

### Graceful Degradation
- Processes continue running if management system fails
- Automatic recovery from transient errors
- Comprehensive error logging

### Resource Cleanup
- Database connection management
- Memory leak prevention
- Thread resource cleanup

### Exception Handling
- Proper exception propagation
- Process state consistency
- Error message preservation

## Monitoring and Logging

### Process Lifecycle Logging
- Process start/completion events
- Progress milestone logging
- Error and cancellation tracking

### Performance Metrics
- Process duration tracking
- Resource utilization monitoring
- Success/failure statistics

### Correlation IDs
- Process-specific log correlation
- Distributed tracing support
- Audit trail maintenance

## Future Enhancements

### Planned Features
- Process scheduling and queuing
- Resource usage monitoring
- Process priority management
- Distributed process coordination
- Historical process analytics

### Integration Opportunities
- JMX monitoring integration
- Metrics collection (Micrometer)
- Health check endpoints
- Circuit breaker patterns
