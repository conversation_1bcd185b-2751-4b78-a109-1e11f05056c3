# Investment Toolkit Frontend

A modern React TypeScript frontend for the Investment Toolkit financial analysis platform.

## Features

- **Dashboard**: Overview of system statistics and quick actions
- **Instruments**: Financial instrument management and search
- **OHLCV Data**: Price data visualization with technical indicators
- **Technical Indicators**: Bollinger Bands and DMI calculation interface
- **Positions**: Portfolio position management
- **Watch List**: Symbol tracking and alerts
- **Processes**: Real-time monitoring of background operations

## Technology Stack

- **React 18** with TypeScript
- **Material-UI (MUI)** for component library
- **React Router** for navigation
- **Axios** for API communication
- **Recharts** for data visualization
- **Date-fns** for date manipulation

## Prerequisites

- Node.js 16+ and npm
- Investment Toolkit Backend running on `http://localhost:8080`

## Installation

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

4. Open your browser to `http://localhost:3000`

## Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm eject` - Eject from Create React App

## API Integration

The frontend communicates with the Spring Boot backend through:

- **Base URL**: `http://localhost:8080/investment-toolkit/api`
- **CORS**: Configured to allow requests from `localhost:3000`
- **Authentication**: Currently none (can be added later)

## Project Structure

```
frontend/
├── public/
│   ├── index.html
│   └── manifest.json
├── src/
│   ├── components/
│   │   └── Navigation.tsx
│   ├── pages/
│   │   ├── Dashboard.tsx
│   │   ├── Instruments.tsx
│   │   ├── OHLCVData.tsx
│   │   ├── TechnicalIndicators.tsx
│   │   ├── Positions.tsx
│   │   ├── WatchList.tsx
│   │   └── Processes.tsx
│   ├── services/
│   │   └── api/
│   │       ├── apiClient.ts
│   │       ├── instrumentService.ts
│   │       ├── ohlcvService.ts
│   │       ├── technicalIndicatorService.ts
│   │       ├── positionService.ts
│   │       ├── watchListService.ts
│   │       └── processService.ts
│   ├── types/
│   │   └── api.ts
│   ├── App.tsx
│   └── index.tsx
├── package.json
├── tsconfig.json
└── README.md
```

## Key Components

### API Services
- **apiClient.ts**: Axios configuration with interceptors
- **Service classes**: Type-safe API communication for each domain

### Pages
- **Dashboard**: System overview with statistics cards
- **Instruments**: Searchable table of financial instruments
- **OHLCVData**: Interactive price charts with technical indicators
- **Processes**: Real-time process monitoring with abort functionality

### Navigation
- **Sidebar navigation** with Material-UI drawer
- **Route-based highlighting** for current page
- **Responsive design** for mobile and desktop

## Configuration

### Environment Variables
Create a `.env` file in the frontend directory:

```
REACT_APP_API_BASE_URL=http://localhost:8080/investment-toolkit/api
REACT_APP_API_TIMEOUT=30000
```

### Proxy Configuration
The `package.json` includes a proxy setting to forward API requests to the backend during development:

```json
"proxy": "http://localhost:8080"
```

## Development Guidelines

### Code Style
- Use TypeScript for all components
- Follow React functional component patterns
- Use Material-UI components consistently
- Implement proper error handling

### API Integration
- All API calls go through service classes
- Use TypeScript interfaces for type safety
- Handle loading states and errors gracefully
- Implement proper error messages for users

### Performance
- Use React.memo for expensive components
- Implement proper loading states
- Optimize re-renders with useCallback/useMemo
- Lazy load routes if needed

## Deployment

### Production Build
```bash
npm run build
```

### Serve Static Files
The build folder can be served by any static file server or integrated with the Spring Boot backend.

## Future Enhancements

- **Authentication**: User login and role-based access
- **Real-time Updates**: WebSocket integration for live data
- **Advanced Charts**: More sophisticated financial charting
- **Mobile App**: React Native version
- **PWA Features**: Offline support and push notifications

## Troubleshooting

### CORS Issues
Ensure the Spring Boot backend has CORS configured for `http://localhost:3000`

### API Connection
Verify the backend is running on `http://localhost:8080/investment-toolkit`

### Build Issues
Clear node_modules and reinstall:
```bash
rm -rf node_modules package-lock.json
npm install
```
