import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Paper, List, ListItem, ListItemText, Chip } from '@mui/material';
import { InstrumentService } from '../services/api/instrumentService';

const InstrumentLoadingDebug: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [instrumentCount, setInstrumentCount] = useState(0);
  const [lastResponse, setLastResponse] = useState<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toISOString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const testInstrumentLoading = async () => {
    addLog('🚀 Starting instrument loading test');
    setIsLoading(true);
    
    try {
      addLog('📡 Making API call to /instruments');
      const startTime = Date.now();
      
      const response = await InstrumentService.getInstruments(0, 1000, 'marketCap', 'desc');
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      addLog(`📥 API response received in ${duration}ms`);
      addLog(`✅ Success: ${response.success}`);
      addLog(`📊 Data length: ${response.data?.content?.length || 0}`);
      addLog(`💬 Message: ${response.message}`);
      
      setLastResponse(response);
      setInstrumentCount(response.data?.content?.length || 0);
      
      if (response.success && response.data) {
        addLog('✅ Test completed successfully');
      } else {
        addLog('❌ Test failed - response not successful');
      }
    } catch (error: any) {
      addLog(`❌ Error occurred: ${error.message}`);
      addLog(`🔍 Error details: ${JSON.stringify(error, null, 2)}`);
    } finally {
      setIsLoading(false);
      addLog('🏁 Test completed');
    }
  };

  const clearLogs = () => {
    setLogs([]);
    setLastResponse(null);
    setInstrumentCount(0);
  };

  const checkCache = () => {
    const cached = localStorage.getItem('watchlist_instruments_cache');
    if (cached) {
      try {
        const parsed = JSON.parse(cached);
        const age = Date.now() - parsed.timestamp;
        addLog(`🗄️ Cache found: ${parsed.data?.length || 0} instruments, age: ${Math.round(age / 1000)}s`);
      } catch (error) {
        addLog('❌ Cache parsing error');
      }
    } else {
      addLog('🗄️ No cache found');
    }
  };

  useEffect(() => {
    addLog('🏗️ Debug component mounted');
    checkCache();
  }, []);

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Instrument Loading Debug Tool
      </Typography>
      
      <Box display="flex" gap={2} mb={3}>
        <Button 
          variant="contained" 
          onClick={testInstrumentLoading}
          disabled={isLoading}
        >
          {isLoading ? 'Testing...' : 'Test Instrument Loading'}
        </Button>
        <Button variant="outlined" onClick={clearLogs}>
          Clear Logs
        </Button>
        <Button variant="outlined" onClick={checkCache}>
          Check Cache
        </Button>
      </Box>

      <Box display="flex" gap={2} mb={3}>
        <Chip 
          label={`Loading: ${isLoading}`} 
          color={isLoading ? 'warning' : 'default'}
        />
        <Chip 
          label={`Instruments: ${instrumentCount}`} 
          color={instrumentCount > 0 ? 'success' : 'default'}
        />
      </Box>

      <Paper elevation={1} sx={{ p: 2, maxHeight: 400, overflow: 'auto' }}>
        <Typography variant="h6" gutterBottom>
          Debug Logs
        </Typography>
        <List dense>
          {logs.map((log, index) => (
            <ListItem key={index} sx={{ py: 0.5 }}>
              <ListItemText 
                primary={log}
                sx={{ 
                  fontFamily: 'monospace', 
                  fontSize: '0.875rem',
                  wordBreak: 'break-all'
                }}
              />
            </ListItem>
          ))}
        </List>
      </Paper>

      {lastResponse && (
        <Paper elevation={1} sx={{ p: 2, mt: 2 }}>
          <Typography variant="h6" gutterBottom>
            Last Response
          </Typography>
          <pre style={{ fontSize: '0.75rem', overflow: 'auto' }}>
            {JSON.stringify(lastResponse, null, 2)}
          </pre>
        </Paper>
      )}
    </Box>
  );
};

export default InstrumentLoadingDebug;
