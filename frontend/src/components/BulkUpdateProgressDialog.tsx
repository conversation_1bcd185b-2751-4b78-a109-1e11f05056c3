import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
  Alert,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  HourglassEmpty as HourglassEmptyIcon,
  Sync as SyncIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

import { BulkUpdateProgress, BulkUpdateResult } from '../types/api';

interface BulkUpdateProgressDialogProps {
  open: boolean;
  onClose: () => void;
  onCancel?: () => void;
  onRetryFailed?: () => void;
  progress: BulkUpdateProgress[];
  isComplete: boolean;
  result?: BulkUpdateResult;
  title?: string;
}

const BulkUpdateProgressDialog: React.FC<BulkUpdateProgressDialogProps> = ({
  open,
  onClose,
  onCancel,
  onRetryFailed,
  progress,
  isComplete,
  result,
  title = 'Bulk OHLCV Data Update',
}) => {
  const getStatusIcon = (status: BulkUpdateProgress['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'processing':
        return <SyncIcon color="primary" sx={{ animation: 'spin 1s linear infinite' }} />;
      case 'pending':
      default:
        return <HourglassEmptyIcon color="disabled" />;
    }
  };

  const getStatusColor = (status: BulkUpdateProgress['status']) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'processing':
        return 'primary';
      case 'pending':
      default:
        return 'default';
    }
  };

  const completedCount = progress.filter(p => p.status === 'success' || p.status === 'error').length;
  const totalCount = progress.length;
  const progressPercentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;
  const failedSymbols = progress.filter(p => p.status === 'error');

  return (
    <>
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>
      <Dialog
        open={open}
        onClose={isComplete ? onClose : undefined}
        maxWidth="md"
        fullWidth
        disableEscapeKeyDown={!isComplete}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">{title}</Typography>
            {result && (
              <Box display="flex" gap={1}>
                <Chip
                  label={`${result.successCount} Success`}
                  color="success"
                  size="small"
                  variant="outlined"
                />
                {result.errorCount > 0 && (
                  <Chip
                    label={`${result.errorCount} Failed`}
                    color="error"
                    size="small"
                    variant="outlined"
                  />
                )}
              </Box>
            )}
          </Box>
        </DialogTitle>

        <DialogContent>
          {/* Overall Progress */}
          <Box mb={3}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="body2" color="text.secondary">
                Progress: {completedCount} of {totalCount} symbols
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {Math.round(progressPercentage)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={progressPercentage}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>

          {/* Result Summary */}
          {result && (
            <Alert
              severity={result.errorCount === 0 ? 'success' : result.successCount === 0 ? 'error' : 'warning'}
              sx={{ mb: 2 }}
            >
              {result.summary}
            </Alert>
          )}

          {/* Progress List */}
          <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
            <List dense>
              {progress.map((item, index) => (
                <React.Fragment key={item.symbol}>
                  <ListItem>
                    <ListItemIcon>
                      {getStatusIcon(item.status)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body2" fontWeight={600}>
                            {item.symbol}
                          </Typography>
                          <Chip
                            label={item.status}
                            size="small"
                            color={getStatusColor(item.status) as any}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        item.status === 'error' ? item.error : item.message
                      }
                      secondaryTypographyProps={{
                        color: item.status === 'error' ? 'error' : 'text.secondary',
                        variant: 'body2',
                      }}
                    />
                  </ListItem>
                  {index < progress.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Box>
        </DialogContent>

        <DialogActions>
          {!isComplete && onCancel && (
            <Button
              onClick={onCancel}
              startIcon={<CancelIcon />}
              color="error"
            >
              Cancel
            </Button>
          )}
          
          {isComplete && failedSymbols.length > 0 && onRetryFailed && (
            <Button
              onClick={onRetryFailed}
              startIcon={<SyncIcon />}
              color="warning"
              variant="outlined"
            >
              Retry Failed ({failedSymbols.length})
            </Button>
          )}
          
          {isComplete && (
            <Button
              onClick={onClose}
              variant="contained"
              color="primary"
            >
              Close
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default BulkUpdateProgressDialog;
