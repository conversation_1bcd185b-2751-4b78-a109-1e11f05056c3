import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
} from '@mui/material';
import {
  DateRange as DateRangeIcon,
  Refresh as RefreshIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';

export interface DateRange {
  startDate: string | null;
  endDate: string | null;
}

interface DateRangePickerProps {
  onDateRangeChange: (dateRange: DateRange) => void;
  loading?: boolean;
  symbol?: string;
  currentRange?: DateRange;
  dataDateRange?: {
    earliest: string;
    latest: string;
  };
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  onDateRangeChange,
  loading = false,
  symbol,
  currentRange,
  dataDateRange,
}) => {
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [preset, setPreset] = useState<string>('all');

  // Update local state when currentRange changes
  useEffect(() => {
    if (currentRange) {
      setStartDate(currentRange.startDate || '');
      setEndDate(currentRange.endDate || '');
      
      // Determine which preset matches current range
      if (!currentRange.startDate && !currentRange.endDate) {
        setPreset('all');
      } else {
        setPreset('custom');
      }
    }
  }, [currentRange]);

  // Preset date ranges
  const getPresetDates = (presetType: string): DateRange => {
    const today = new Date();
    const formatDate = (date: Date) => date.toISOString().split('T')[0];

    switch (presetType) {
      case 'last30':
        return {
          startDate: formatDate(new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)),
          endDate: formatDate(today),
        };
      case 'last90':
        return {
          startDate: formatDate(new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)),
          endDate: formatDate(today),
        };
      case 'last180':
        return {
          startDate: formatDate(new Date(today.getTime() - 180 * 24 * 60 * 60 * 1000)),
          endDate: formatDate(today),
        };
      case 'lastYear':
        return {
          startDate: formatDate(new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000)),
          endDate: formatDate(today),
        };
      case 'ytd':
        return {
          startDate: `${today.getFullYear()}-01-01`,
          endDate: formatDate(today),
        };
      case 'all':
      default:
        return {
          startDate: null,
          endDate: null,
        };
    }
  };

  // Handle preset selection
  const handlePresetChange = (presetType: string) => {
    setPreset(presetType);
    const dateRange = getPresetDates(presetType);
    setStartDate(dateRange.startDate || '');
    setEndDate(dateRange.endDate || '');
    onDateRangeChange(dateRange);
  };

  // Handle custom date changes
  const handleCustomDateChange = () => {
    const dateRange: DateRange = {
      startDate: startDate || null,
      endDate: endDate || null,
    };
    setPreset('custom');
    onDateRangeChange(dateRange);
  };

  // Handle clear dates
  const handleClear = () => {
    setStartDate('');
    setEndDate('');
    setPreset('all');
    onDateRangeChange({ startDate: null, endDate: null });
  };

  // Get display text for current range
  const getCurrentRangeText = () => {
    if (!currentRange?.startDate && !currentRange?.endDate) {
      return 'All available data';
    }
    if (currentRange.startDate && currentRange.endDate) {
      return `${currentRange.startDate} to ${currentRange.endDate}`;
    }
    if (currentRange.startDate) {
      return `From ${currentRange.startDate}`;
    }
    if (currentRange.endDate) {
      return `Until ${currentRange.endDate}`;
    }
    return 'Custom range';
  };

  // Validate date range
  const isValidRange = () => {
    if (!startDate || !endDate) return true; // Allow partial ranges
    return new Date(startDate) <= new Date(endDate);
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <DateRangeIcon color="primary" />
          <Typography variant="h6">
            Date Range
          </Typography>
          {symbol && (
            <Chip
              label={symbol}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
        </Box>

        {/* Current Range Display */}
        <Box mb={2}>
          <Typography variant="body2" color="text.secondary">
            Current range: <strong>{getCurrentRangeText()}</strong>
          </Typography>
          {dataDateRange && (
            <Typography variant="caption" color="text.secondary">
              Available data: {dataDateRange.earliest} to {dataDateRange.latest}
            </Typography>
          )}
        </Box>

        <Grid container spacing={2} alignItems="center">
          {/* Preset Selection */}
          <Grid item xs={12} md={4}>
            <FormControl fullWidth size="small">
              <InputLabel>Quick Select</InputLabel>
              <Select
                value={preset}
                label="Quick Select"
                onChange={(e) => handlePresetChange(e.target.value)}
                disabled={loading}
              >
                <MenuItem value="all">All Data</MenuItem>
                <MenuItem value="last30">Last 30 Days</MenuItem>
                <MenuItem value="last90">Last 90 Days</MenuItem>
                <MenuItem value="last180">Last 6 Months</MenuItem>
                <MenuItem value="lastYear">Last Year</MenuItem>
                <MenuItem value="ytd">Year to Date</MenuItem>
                <MenuItem value="custom">Custom Range</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Custom Date Inputs */}
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              label="Start Date"
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              InputLabelProps={{ shrink: true }}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              label="End Date"
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              InputLabelProps={{ shrink: true }}
              disabled={loading}
            />
          </Grid>

          {/* Action Buttons */}
          <Grid item xs={12} md={2}>
            <Box display="flex" gap={1}>
              <Button
                variant="contained"
                size="small"
                onClick={handleCustomDateChange}
                disabled={loading || !isValidRange()}
                startIcon={<RefreshIcon />}
              >
                Apply
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={handleClear}
                disabled={loading}
                startIcon={<ClearIcon />}
              >
                Clear
              </Button>
            </Box>
          </Grid>
        </Grid>

        {/* Validation Error */}
        {!isValidRange() && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Start date must be before or equal to end date
          </Alert>
        )}

        {/* Loading Indicator */}
        {loading && (
          <Box mt={2}>
            <Typography variant="body2" color="primary">
              Loading data for selected date range...
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default DateRangePicker;
