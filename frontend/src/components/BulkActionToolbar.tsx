import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Chip,
  Slide,
  Tooltip,
  Divider,
} from '@mui/material';
import {
  Update as UpdateIcon,
  Clear as ClearIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

interface BulkActionToolbarProps {
  selectedCount: number;
  onUpdateOHLCV: () => void;
  onClearSelection: () => void;
  onClose: () => void;
  isVisible: boolean;
  isLoading?: boolean;
}

const BulkActionToolbar: React.FC<BulkActionToolbarProps> = ({
  selectedCount,
  onUpdateOHLCV,
  onClearSelection,
  onClose,
  isVisible,
  isLoading = false,
}) => {
  return (
    <Slide direction="up" in={isVisible} mountOnEnter unmountOnExit>
      <Paper
        elevation={8}
        sx={{
          position: 'fixed',
          bottom: 24,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1300,
          borderRadius: 3,
          overflow: 'hidden',
          minWidth: 400,
          maxWidth: 600,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 2,
            backgroundColor: 'primary.main',
            color: 'primary.contrastText',
          }}
        >
          <Box display="flex" alignItems="center" gap={2}>
            <Chip
              label={`${selectedCount} selected`}
              size="small"
              sx={{
                backgroundColor: 'primary.light',
                color: 'primary.contrastText',
                fontWeight: 600,
              }}
            />
            <Typography variant="body2" fontWeight={500}>
              Bulk Actions
            </Typography>
          </Box>

          <Tooltip title="Close">
            <IconButton
              onClick={onClose}
              size="small"
              sx={{ color: 'primary.contrastText' }}
            >
              <CloseIcon />
            </IconButton>
          </Tooltip>
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 2,
            backgroundColor: 'background.paper',
          }}
        >
          <Button
            variant="contained"
            startIcon={<UpdateIcon />}
            onClick={onUpdateOHLCV}
            disabled={isLoading || selectedCount === 0}
            sx={{ flex: 1 }}
          >
            {isLoading ? 'Updating...' : 'Update OHLCV Data'}
          </Button>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          <Tooltip title="Clear Selection">
            <IconButton
              onClick={onClearSelection}
              disabled={isLoading || selectedCount === 0}
              color="primary"
            >
              <ClearIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Progress indicator when loading */}
        {isLoading && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: 3,
              backgroundColor: 'primary.light',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                height: '100%',
                width: '30%',
                backgroundColor: 'primary.main',
                animation: 'loading 1.5s ease-in-out infinite',
              },
            }}
          />
        )}

        <style>
          {`
            @keyframes loading {
              0% { transform: translateX(-100%); }
              50% { transform: translateX(300%); }
              100% { transform: translateX(-100%); }
            }
          `}
        </style>
      </Paper>
    </Slide>
  );
};

export default BulkActionToolbar;
