import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Divider,
  CircularProgress,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Sync as SyncIcon,
  HourglassEmpty as HourglassEmptyIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  Calculate as CalculateIcon,
} from '@mui/icons-material';
import { WatchListUpdateResult } from '../types/api';

interface WatchListUpdateProgressDialogProps {
  open: boolean;
  onClose: () => void;
  onCancel?: () => void;
  isComplete: boolean;
  result?: WatchListUpdateResult;
  title?: string;
  startTime?: Date;
}

const WatchListUpdateProgressDialog: React.FC<WatchListUpdateProgressDialogProps> = ({
  open,
  onClose,
  onCancel,
  isComplete,
  result,
  title = 'Update OHLCV Data',
  startTime,
}) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [showDetails, setShowDetails] = useState(false);

  // Update elapsed time every second
  useEffect(() => {
    if (!startTime || isComplete) return;

    const interval = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setElapsedTime(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime, isComplete]);

  const formatElapsedTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'ohlcv':
        return <TimelineIcon color="primary" />;
      case 'bollinger':
        return <TrendingUpIcon color="secondary" />;
      case 'dmi':
        return <CalculateIcon color="info" />;
      case 'complete':
        return <CheckCircleIcon color="success" />;
      default:
        return <HourglassEmptyIcon color="disabled" />;
    }
  };

  const getPhaseDescription = (phase: string) => {
    switch (phase) {
      case 'ohlcv':
        return 'Updating OHLCV historical data from Yahoo Finance';
      case 'bollinger':
        return 'Recalculating Bollinger Bands indicators';
      case 'dmi':
        return 'Recalculating DMI technical indicators';
      case 'complete':
        return 'Update process completed successfully';
      default:
        return 'Preparing update process';
    }
  };

  const calculateProgress = (): number => {
    if (!result) return 0;
    if (isComplete) return 100;
    
    // Estimate progress based on phases completed
    const phases = ['ohlcv', 'bollinger', 'dmi'];
    let completedPhases = 0;
    
    if (result.ohlcvSuccessCount > 0 || result.ohlcvErrorCount > 0) completedPhases++;
    if (result.bollingerBandsProcessed > 0) completedPhases++;
    if (result.dmiProcessed > 0) completedPhases++;
    
    return Math.round((completedPhases / phases.length) * 100);
  };

  return (
    <Dialog
      open={open}
      onClose={isComplete ? onClose : undefined}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown={!isComplete}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">{title}</Typography>
          {!isComplete && startTime && (
            <Chip
              label={`Elapsed: ${formatElapsedTime(elapsedTime)}`}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
        </Box>
      </DialogTitle>

      <DialogContent>
        {!isComplete && (
          <Box mb={3}>
            <Box display="flex" alignItems="center" mb={1}>
              <CircularProgress size={20} sx={{ mr: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Processing watch list symbols...
              </Typography>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={calculateProgress()} 
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {calculateProgress()}% complete
            </Typography>
          </Box>
        )}

        {result && (
          <Box>
            {/* Summary Statistics */}
            <Box mb={2}>
              <Typography variant="subtitle2" gutterBottom>
                Update Summary
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                <Chip
                  label={`${result.totalSymbols} symbols`}
                  size="small"
                  color="primary"
                />
                <Chip
                  label={`${result.ohlcvSuccessCount} OHLCV updated`}
                  size="small"
                  color="success"
                />
                {result.ohlcvErrorCount > 0 && (
                  <Chip
                    label={`${result.ohlcvErrorCount} errors`}
                    size="small"
                    color="error"
                  />
                )}
                <Chip
                  label={`${result.totalRecordsUpdated} records`}
                  size="small"
                  color="info"
                />
              </Box>
            </Box>

            {/* Processing Phases */}
            <Box mb={2}>
              <Typography variant="subtitle2" gutterBottom>
                Processing Phases
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>{getPhaseIcon('ohlcv')}</ListItemIcon>
                  <ListItemText
                    primary="OHLCV Data Update"
                    secondary={`${result.ohlcvSuccessCount} successful, ${result.ohlcvErrorCount} failed`}
                  />
                  {(result.ohlcvSuccessCount > 0 || result.ohlcvErrorCount > 0) && (
                    <CheckCircleIcon color="success" />
                  )}
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>{getPhaseIcon('bollinger')}</ListItemIcon>
                  <ListItemText
                    primary="Bollinger Bands Calculation"
                    secondary={`${result.bollingerBandsProcessed || 0} symbols processed`}
                  />
                  {result.bollingerBandsProcessed > 0 && (
                    <CheckCircleIcon color="success" />
                  )}
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>{getPhaseIcon('dmi')}</ListItemIcon>
                  <ListItemText
                    primary="DMI Indicators Calculation"
                    secondary={`${result.dmiProcessed || 0} symbols processed`}
                  />
                  {result.dmiProcessed > 0 && (
                    <CheckCircleIcon color="success" />
                  )}
                </ListItem>
              </List>
            </Box>

            {/* Detailed Results */}
            {result.phases?.ohlcv && result.phases.ohlcv.length > 0 && (
              <Box>
                <Button
                  startIcon={showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  onClick={() => setShowDetails(!showDetails)}
                  size="small"
                >
                  {showDetails ? 'Hide' : 'Show'} Symbol Details
                </Button>
                
                <Collapse in={showDetails}>
                  <Box mt={2} maxHeight={200} overflow="auto">
                    <List dense>
                      {result.phases.ohlcv.map((item, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            {item.status === 'success' && <CheckCircleIcon color="success" />}
                            {item.status === 'error' && <ErrorIcon color="error" />}
                            {item.status === 'processing' && <SyncIcon color="primary" />}
                            {item.status === 'pending' && <HourglassEmptyIcon color="disabled" />}
                          </ListItemIcon>
                          <ListItemText
                            primary={item.symbol}
                            secondary={item.message || item.error}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                </Collapse>
              </Box>
            )}

            {/* Completion Message */}
            {isComplete && (
              <Alert 
                severity={result.ohlcvErrorCount === 0 ? "success" : "warning"} 
                sx={{ mt: 2 }}
              >
                <Typography variant="body2">
                  {result.summary}
                </Typography>
                {result.processingTimeMs && (
                  <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                    Processing time: {Math.round(result.processingTimeMs / 1000)} seconds
                  </Typography>
                )}
              </Alert>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        {!isComplete && onCancel && (
          <Button onClick={onCancel} color="secondary">
            Cancel
          </Button>
        )}
        <Button 
          onClick={onClose} 
          variant={isComplete ? "contained" : "outlined"}
          disabled={!isComplete}
        >
          {isComplete ? 'Close' : 'Processing...'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default WatchListUpdateProgressDialog;
