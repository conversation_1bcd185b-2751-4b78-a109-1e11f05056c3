import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
} from '@mui/material';
import {
  Business as BusinessIcon,
  ShowChart as ShowChartIcon,
  AccountBalance as AccountBalanceIcon,
  Visibility as VisibilityIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

// Services
import { InstrumentService } from '../services/api/instrumentService';
import { OHLCVService } from '../services/api/ohlcvService';
import { PositionService } from '../services/api/positionService';
import { WatchListService } from '../services/api/watchListService';
import { ProcessService } from '../services/api/processService';

interface DashboardStats {
  instruments: number;
  ohlcvSymbols: number;
  positions: number;
  watchListItems: number;
  activeProcesses: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all dashboard statistics in parallel
      const [
        instrumentsResponse,
        ohlcvResponse,
        positionsResponse,
        watchListResponse,
        processesResponse,
      ] = await Promise.allSettled([
        InstrumentService.getStatistics(),
        OHLCVService.getSymbolsWithData(0, 1),
        PositionService.getPositions(),
        WatchListService.getWatchListItems(),
        ProcessService.getActiveProcesses(),
      ]);

      const dashboardStats: DashboardStats = {
        instruments: instrumentsResponse.status === 'fulfilled'
          ? instrumentsResponse.value.data?.totalInstruments || 0
          : 0,
        ohlcvSymbols: ohlcvResponse.status === 'fulfilled'
          ? ohlcvResponse.value.data?.length || 0
          : 0,
        positions: positionsResponse.status === 'fulfilled'
          ? positionsResponse.value.data?.length || 0
          : 0,
        watchListItems: watchListResponse.status === 'fulfilled'
          ? watchListResponse.value.data?.length || 0
          : 0,
        activeProcesses: processesResponse.status === 'fulfilled'
          ? processesResponse.value.data?.length || 0
          : 0,
      };

      setStats(dashboardStats);
    } catch (err: any) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error" onClose={() => setError(null)}>
          {error}
        </Alert>
      </Box>
    );
  }

  const statCards = [
    {
      title: 'Instruments',
      value: stats?.instruments || 0,
      icon: <BusinessIcon fontSize="large" />,
      color: '#1976d2',
    },
    {
      title: 'OHLCV Symbols',
      value: stats?.ohlcvSymbols || 0,
      icon: <ShowChartIcon fontSize="large" />,
      color: '#388e3c',
    },
    {
      title: 'Positions',
      value: stats?.positions || 0,
      icon: <AccountBalanceIcon fontSize="large" />,
      color: '#f57c00',
    },
    {
      title: 'Watch List Items',
      value: stats?.watchListItems || 0,
      icon: <VisibilityIcon fontSize="large" />,
      color: '#7b1fa2',
    },
    {
      title: 'Active Processes',
      value: stats?.activeProcesses || 0,
      icon: <SettingsIcon fontSize="large" />,
      color: stats?.activeProcesses && stats.activeProcesses > 0 ? '#d32f2f' : '#616161',
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
        Investment Toolkit Dashboard
      </Typography>

      <Grid container spacing={3}>
        {statCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} lg={2.4} key={index}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                },
              }}
            >
              <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: 3 }}>
                <Box
                  sx={{
                    color: card.color,
                    mb: 2,
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  {card.icon}
                </Box>
                <Typography variant="h4" component="div" sx={{ fontWeight: 600, mb: 1 }}>
                  {card.value.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {card.title}
                </Typography>
                {card.title === 'Active Processes' && card.value > 0 && (
                  <Chip
                    label="Running"
                    color="error"
                    size="small"
                    sx={{ mt: 1 }}
                  />
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box mt={4}>
        <Typography variant="h5" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Data Management
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Manage financial instruments, OHLCV data, and synchronize with external sources.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Technical Analysis
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Calculate and analyze technical indicators like Bollinger Bands and DMI.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default Dashboard;
