import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Refresh as RefreshIcon, Stop as StopIcon } from '@mui/icons-material';

import { ProcessService } from '../services/api/processService';
import { ProcessInfo } from '../types/api';

const Processes: React.FC = () => {
  const [processes, setProcesses] = useState<ProcessInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProcesses();
    // Set up auto-refresh every 5 seconds
    const interval = setInterval(loadProcesses, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadProcesses = async () => {
    try {
      setError(null);
      const response = await ProcessService.getAllProcesses();
      setProcesses(response.data || []);
    } catch (err: any) {
      console.error('Error loading processes:', err);
      setError('Failed to load processes');
    } finally {
      setLoading(false);
    }
  };

  const handleAbortProcess = async (processId: string) => {
    try {
      await ProcessService.abortProcess(processId);
      loadProcesses(); // Refresh the list
    } catch (err: any) {
      console.error('Error aborting process:', err);
      setError(`Failed to abort process: ${processId}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'primary';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'aborted':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Process Management
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={loadProcesses}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading && processes.length === 0 ? (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell><strong>Process ID</strong></TableCell>
                <TableCell><strong>Type</strong></TableCell>
                <TableCell><strong>Status</strong></TableCell>
                <TableCell><strong>Start Time</strong></TableCell>
                <TableCell><strong>Duration</strong></TableCell>
                <TableCell><strong>Progress</strong></TableCell>
                <TableCell><strong>Actions</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {processes.map((process) => (
                <TableRow key={process.processId} hover>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {process.processId}
                    </Typography>
                  </TableCell>
                  <TableCell>{process.processType}</TableCell>
                  <TableCell>
                    <Chip 
                      label={process.status} 
                      color={getStatusColor(process.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(process.startTime).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    {process.duration ? `${process.duration}ms` : 'N/A'}
                  </TableCell>
                  <TableCell>
                    {process.progress ? `${process.progress}%` : 'N/A'}
                  </TableCell>
                  <TableCell>
                    {process.status === 'RUNNING' && (
                      <Button
                        size="small"
                        color="error"
                        startIcon={<StopIcon />}
                        onClick={() => handleAbortProcess(process.processId)}
                      >
                        Abort
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {!loading && processes.length === 0 && (
        <Card>
          <CardContent>
            <Box textAlign="center" p={3}>
              <Typography variant="h6" color="text.secondary">
                No processes found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                All processes have completed or no processes have been started
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default Processes;
