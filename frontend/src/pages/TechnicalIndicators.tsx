import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  TextField,
  Chip,
  CircularProgress,
  Alert,
  Snackbar,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormLabel,
  Divider,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Calculate as CalculateIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  ShowChart as ShowChartIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { TechnicalIndicatorService } from '../services/api/technicalIndicatorService';
import { InstrumentService } from '../services/api/instrumentService';
import { Instrument, TechnicalIndicatorRequest, DMICalculationProgress } from '../types/api';

interface CalculationResult {
  success: boolean;
  message: string;
  details?: any;
}

const TechnicalIndicators: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // State management
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [selectedSymbols, setSelectedSymbols] = useState<string[]>([]);
  const [calculationMode, setCalculationMode] = useState<'INCREMENTAL' | 'FULL_RECALCULATION' | 'SKIP_EXISTING'>('INCREMENTAL');
  const [dmiCalculationMethod, setDmiCalculationMethod] = useState<'PURE_JAVA' | 'HYBRID_SQL_JAVA'>('HYBRID_SQL_JAVA');
  const [loadingInstruments, setLoadingInstruments] = useState(false);
  const [calculatingBollinger, setCalculatingBollinger] = useState(false);
  const [calculatingDMI, setCalculatingDMI] = useState(false);
  const [dmiProgress, setDmiProgress] = useState<DMICalculationProgress>({
    isCalculating: false,
    elapsedSeconds: 0,
  });
  const [isAutoLoaded, setIsAutoLoaded] = useState(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Load instruments on component mount
  useEffect(() => {
    loadInstruments();
  }, []);

  // Check for refresh flag when component mounts or when coming from other pages
  useEffect(() => {
    const shouldRefresh = localStorage.getItem('refreshInstruments');
    if (shouldRefresh === 'true') {
      localStorage.removeItem('refreshInstruments');
      // Small delay to ensure the component is fully mounted
      setTimeout(() => {
        loadInstruments(true);
      }, 500);
    }
  }, []);

  // Auto-select symbol from URL parameter after instruments are loaded
  useEffect(() => {
    const symbolFromUrl = searchParams.get('symbol');
    if (symbolFromUrl && instruments.length > 0 && !isAutoLoaded) {
      const symbolUpper = symbolFromUrl.toUpperCase();
      console.log(`Processing URL symbol: ${symbolUpper}, instruments loaded: ${instruments.length}`);

      // Validate that the symbol exists in our instruments list
      const symbolExists = instruments.some(instrument => instrument.symbol === symbolUpper);
      if (symbolExists) {
        setSelectedSymbols([symbolUpper]);
        showSnackbar(`Pre-selected symbol: ${symbolUpper}`, 'info');
        console.log(`Symbol ${symbolUpper} found in instruments list and selected`);
      } else {
        console.log(`Symbol ${symbolUpper} not found in instruments list, searching...`);
        // Symbol not found in current list, try to search for it
        searchForMissingSymbol(symbolUpper);
      }
      setIsAutoLoaded(true);
    }
  }, [searchParams, instruments, isAutoLoaded]);

  // Additional effect to handle URL parameter when instruments are still loading
  useEffect(() => {
    const symbolFromUrl = searchParams.get('symbol');
    if (symbolFromUrl && instruments.length === 0 && !loadingInstruments && !isAutoLoaded) {
      const symbolUpper = symbolFromUrl.toUpperCase();
      console.log(`URL symbol detected but instruments not loaded yet: ${symbolUpper}, triggering search`);
      // If instruments haven't loaded yet, try to search directly
      searchForMissingSymbol(symbolUpper);
      setIsAutoLoaded(true);
    }
  }, [searchParams, instruments.length, loadingInstruments, isAutoLoaded]);

  // Search for a specific symbol if it's not in the current instruments list
  const searchForMissingSymbol = async (symbol: string) => {
    console.log(`Searching for missing symbol: ${symbol}`);
    try {
      // First try to search by exact symbol match
      const response = await InstrumentService.searchInstruments(symbol);
      console.log(`Search response for ${symbol}:`, response);

      if (response.success && response.data && response.data.length > 0) {
        // Look for exact match first
        let foundInstrument = response.data.find(inst => inst.symbol === symbol);

        // If no exact match, look for case-insensitive match
        if (!foundInstrument) {
          foundInstrument = response.data.find(inst => inst.symbol.toUpperCase() === symbol.toUpperCase());
        }

        if (foundInstrument) {
          console.log(`Found instrument:`, foundInstrument);
          // Add the found instrument to our list and select it
          setInstruments(prev => {
            const exists = prev.some(inst => inst.symbol === foundInstrument!.symbol);
            if (!exists) {
              console.log(`Adding ${foundInstrument!.symbol} to instruments list`);
              return [...prev, foundInstrument!];
            }
            console.log(`${foundInstrument!.symbol} already exists in instruments list`);
            return prev;
          });
          setSelectedSymbols([foundInstrument.symbol]);
          showSnackbar(`Found and selected symbol: ${foundInstrument.symbol}`, 'success');
        } else {
          console.log(`No exact match found for ${symbol} in search results`);
          // Try to get the symbol directly as a fallback
          await tryDirectSymbolLookup(symbol);
        }
      } else {
        console.log(`No search results for ${symbol}`);
        // Try to get the symbol directly as a fallback
        await tryDirectSymbolLookup(symbol);
      }
    } catch (error: any) {
      console.error('Error searching for symbol:', error);
      showSnackbar(`Error searching for symbol ${symbol}: ${error.message || 'Unknown error'}`, 'error');
    }
  };

  // Try to get a symbol directly by symbol lookup as a fallback
  const tryDirectSymbolLookup = async (symbol: string) => {
    console.log(`Trying direct lookup for symbol: ${symbol}`);
    try {
      const response = await InstrumentService.getInstrumentBySymbol(symbol);
      console.log(`Direct lookup response for ${symbol}:`, response);

      if (response.success && response.data) {
        const foundInstrument = response.data;
        console.log(`Found instrument via direct lookup:`, foundInstrument);

        // Add the found instrument to our list and select it
        setInstruments(prev => {
          const exists = prev.some(inst => inst.symbol === foundInstrument.symbol);
          if (!exists) {
            console.log(`Adding ${foundInstrument.symbol} to instruments list via direct lookup`);
            return [...prev, foundInstrument];
          }
          console.log(`${foundInstrument.symbol} already exists in instruments list`);
          return prev;
        });
        setSelectedSymbols([foundInstrument.symbol]);
        showSnackbar(`Found and selected symbol: ${foundInstrument.symbol}`, 'success');
      } else {
        console.log(`Direct lookup failed for ${symbol}`);
        showSnackbar(`Symbol ${symbol} not found in database`, 'warning');
      }
    } catch (error: any) {
      console.error('Error in direct symbol lookup:', error);
      showSnackbar(`Symbol ${symbol} not found in database`, 'warning');
    }
  };

  // Timer effect for DMI calculation progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (dmiProgress.isCalculating && dmiProgress.startTime) {
      interval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - dmiProgress.startTime!.getTime()) / 1000);
        setDmiProgress(prev => ({ ...prev, elapsedSeconds: elapsed }));
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [dmiProgress.isCalculating, dmiProgress.startTime]);

  const loadInstruments = async (forceRefresh: boolean = false) => {
    setLoadingInstruments(true);
    try {
      // Fetch more instruments to ensure newly created ones are included
      // Use maximum allowed page size (1000) - server constraint: page size must be between 1 and 1000
      const response = await InstrumentService.getInstruments(0, 1000, 'marketCap', 'desc');
      if (response.success && response.data) {
        const loadedInstruments = response.data.content;
        setInstruments(loadedInstruments);

        if (forceRefresh) {
          showSnackbar(`Refreshed ${loadedInstruments.length} instruments`, 'success');
        }

        console.log(`Loaded ${loadedInstruments.length} instruments for Technical Indicators`);
      } else {
        showSnackbar('Failed to load instruments', 'error');
      }
    } catch (error: any) {
      console.error('Error loading instruments:', error);
      showSnackbar('Error loading instruments: ' + (error.message || 'Unknown error'), 'error');
    } finally {
      setLoadingInstruments(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleCalculateBollingerBands = async () => {
    setCalculatingBollinger(true);
    try {
      const request: TechnicalIndicatorRequest = {
        calculationMode: calculationMode,
        period: 20,
        stdDevMultiplier: 2.0,
        minDataPoints: 20,
        dryRun: false,
        ...(selectedSymbols.length > 0 && { symbols: selectedSymbols }),
      };

      const response = await TechnicalIndicatorService.calculateBollingerBands(request);

      if (response.success) {
        const details = response.data;
        const message = `Bollinger Bands calculation completed! Processed ${details.processedSymbols || 0} symbols, updated ${details.totalRecordsUpdated || 0} records.`;
        showSnackbar(message, 'success');
      } else {
        showSnackbar(`Bollinger Bands calculation failed: ${response.message}`, 'error');
      }
    } catch (error: any) {
      console.error('Error calculating Bollinger Bands:', error);
      showSnackbar(`Error calculating Bollinger Bands: ${error.message || 'Unknown error'}`, 'error');
    } finally {
      setCalculatingBollinger(false);
    }
  };

  const formatElapsedTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  const handleCalculateDMI = async () => {
    setCalculatingDMI(true);
    const startTime = new Date();
    setDmiProgress({
      isCalculating: true,
      startTime,
      elapsedSeconds: 0,
      currentOperation: 'Initializing DMI calculation...',
    });

    try {
      const request: TechnicalIndicatorRequest = {
        calculationMode: calculationMode,
        calculationMethod: dmiCalculationMethod,
        period: 14,
        minDataPoints: 28, // DMI needs 2*period for full calculation
        dryRun: false,
        ...(selectedSymbols.length > 0 && { symbols: selectedSymbols }),
      };

      setDmiProgress(prev => ({ ...prev, currentOperation: 'Processing DMI calculations...' }));

      // Use the long-running service method with extended timeout
      const response = await TechnicalIndicatorService.calculateDMILongRunning(request);

      const endTime = new Date();
      const totalElapsedMs = endTime.getTime() - startTime.getTime();
      const totalElapsedSeconds = Math.floor(totalElapsedMs / 1000);

      if (response.success) {
        const details = response.data;
        const completionMessage = `DMI calculation completed in ${formatElapsedTime(totalElapsedSeconds)}! ` +
          `Processed ${details.processedSymbols || 0} symbols, ` +
          `updated ${details.totalRecordsUpdated || 0} records ` +
          `using ${dmiCalculationMethod.replace('_', ' ')} method.`;

        // Show completion summary
        setDmiProgress(prev => ({
          ...prev,
          currentOperation: `Completed successfully in ${formatElapsedTime(totalElapsedSeconds)}`,
        }));

        showSnackbar(completionMessage, 'success');
      } else {
        showSnackbar(`DMI calculation failed: ${response.message}`, 'error');
      }
    } catch (error: any) {
      console.error('Error calculating DMI:', error);
      const errorMessage = error.message || 'Unknown error';

      // Check if it's a timeout error
      if (errorMessage.includes('timeout')) {
        showSnackbar(
          'DMI calculation is taking longer than expected. This is normal for large datasets. ' +
          'The calculation may still be running in the background. Please check back later.',
          'warning'
        );
      } else {
        showSnackbar(`Error calculating DMI: ${errorMessage}`, 'error');
      }
    } finally {
      setCalculatingDMI(false);
      setDmiProgress({
        isCalculating: false,
        elapsedSeconds: 0,
      });
    }
  };

  const getSymbolOptions = () => {
    return instruments.map(instrument => ({
      label: `${instrument.symbol} - ${instrument.name}`,
      value: instrument.symbol,
    }));
  };

  // Handle manual symbol search when user types a symbol not in the list
  const handleSymbolInputChange = async (event: any, value: string, reason: string) => {
    if (reason === 'input' && value && value.length >= 2) {
      const searchTerm = value.toUpperCase();
      // Check if the search term looks like a symbol and isn't already in our list
      if (/^[A-Z0-9.-]+$/.test(searchTerm) && !instruments.some(inst => inst.symbol.includes(searchTerm))) {
        try {
          const response = await InstrumentService.searchInstruments(searchTerm);
          if (response.success && response.data && response.data.length > 0) {
            // Add any new instruments found to our list
            setInstruments(prev => {
              const newInstruments = response.data.filter(newInst =>
                !prev.some(existingInst => existingInst.symbol === newInst.symbol)
              );
              if (newInstruments.length > 0) {
                console.log(`Found ${newInstruments.length} new instruments matching "${searchTerm}"`);
                return [...prev, ...newInstruments];
              }
              return prev;
            });
          }
        } catch (error) {
          console.error('Error searching for symbols:', error);
        }
      }
    }
  };

  // Handle navigation to OHLCV Data with selected symbol
  const handleViewOHLCVData = () => {
    if (selectedSymbols.length === 1) {
      const symbol = selectedSymbols[0];
      navigate(`/ohlcv/${symbol.toUpperCase()}`);
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Technical Indicators
      </Typography>

      {/* Debug Information (only show if URL parameter exists) */}
      {searchParams.get('symbol') && (
        <Card sx={{ mb: 2, backgroundColor: '#f5f5f5' }}>
          <CardContent>
            <Typography variant="subtitle2" gutterBottom>
              Debug Information
            </Typography>
            <Typography variant="caption" display="block">
              URL Symbol: {searchParams.get('symbol')}
            </Typography>
            <Typography variant="caption" display="block">
              Instruments Loaded: {instruments.length}
            </Typography>
            <Typography variant="caption" display="block">
              Selected Symbols: {selectedSymbols.join(', ') || 'None'}
            </Typography>
            <Typography variant="caption" display="block">
              Auto-loaded: {isAutoLoaded ? 'Yes' : 'No'}
            </Typography>
            <Typography variant="caption" display="block">
              Loading Instruments: {loadingInstruments ? 'Yes' : 'No'}
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Configuration Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <SettingsIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">
              Calculation Settings
            </Typography>
          </Box>

          <Grid container spacing={3}>
            {/* Symbol Selection */}
            <Grid item xs={12} md={5}>
              <Autocomplete
                multiple
                options={getSymbolOptions()}
                getOptionLabel={(option) => option.label}
                value={getSymbolOptions().filter(option => selectedSymbols.includes(option.value))}
                onChange={(_, newValue) => {
                  setSelectedSymbols(newValue.map(option => option.value));
                }}
                onInputChange={handleSymbolInputChange}
                loading={loadingInstruments}
                filterOptions={(options, { inputValue }) => {
                  // Enhanced filtering to show partial matches
                  const filtered = options.filter(option =>
                    option.label.toLowerCase().includes(inputValue.toLowerCase()) ||
                    option.value.toLowerCase().includes(inputValue.toLowerCase())
                  );
                  return filtered;
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Symbols"
                    placeholder={selectedSymbols.length === 0 ? "All Symbols" : "Add more symbols..."}
                    helperText={
                      selectedSymbols.length === 0
                        ? `Leave empty to calculate for all symbols (${instruments.length} available)`
                        : `${selectedSymbols.length} symbol(s) selected from ${instruments.length} available`
                    }
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {loadingInstruments ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option.value}
                      size="small"
                      {...getTagProps({ index })}
                      key={option.value}
                    />
                  ))
                }
                disabled={calculatingBollinger || calculatingDMI}
              />
            </Grid>

            {/* Refresh Symbols Button */}
            <Grid item xs={12} md={1}>
              <Button
                variant="outlined"
                onClick={() => loadInstruments(true)}
                disabled={loadingInstruments || calculatingBollinger || calculatingDMI}
                startIcon={loadingInstruments ? <CircularProgress size={16} /> : <RefreshIcon />}
                fullWidth
                sx={{ height: '56px' }} // Match the height of the autocomplete field
              >
                Refresh
              </Button>
            </Grid>

            {/* Calculation Mode */}
            <Grid item xs={12} md={6}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Calculation Mode</FormLabel>
                <RadioGroup
                  value={calculationMode}
                  onChange={(e) => setCalculationMode(e.target.value as any)}
                  row
                >
                  <FormControlLabel
                    value="INCREMENTAL"
                    control={<Radio size="small" />}
                    label="Incremental"
                    disabled={calculatingBollinger || calculatingDMI}
                  />
                  <FormControlLabel
                    value="FULL_RECALCULATION"
                    control={<Radio size="small" />}
                    label="Full Recalc"
                    disabled={calculatingBollinger || calculatingDMI}
                  />
                  <FormControlLabel
                    value="SKIP_EXISTING"
                    control={<Radio size="small" />}
                    label="Skip Existing"
                    disabled={calculatingBollinger || calculatingDMI}
                  />
                </RadioGroup>
              </FormControl>
            </Grid>
          </Grid>

          {/* Conditional OHLCV Navigation */}
          {selectedSymbols.length === 1 && (
            <Box mt={3}>
              <Divider sx={{ mb: 2 }} />
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Single Symbol Analysis
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    View detailed OHLCV data and charts for {selectedSymbols[0]}
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  startIcon={<ShowChartIcon />}
                  onClick={handleViewOHLCVData}
                  disabled={calculatingBollinger || calculatingDMI}
                  sx={{ ml: 2 }}
                >
                  View OHLCV Data
                </Button>
              </Box>
            </Box>
          )}

          {/* Mode Descriptions */}
          <Box mt={2}>
            <Typography variant="caption" color="text.secondary">
              <strong>Incremental:</strong> Calculate only new data points (recommended for regular updates) |{' '}
              <strong>Full Recalc:</strong> Recalculate all data from scratch |{' '}
              <strong>Skip Existing:</strong> Only calculate for symbols without existing data
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Technical Indicators */}
      <Grid container spacing={3}>
        {/* Bollinger Bands */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TrendingUpIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">
                  Bollinger Bands
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" paragraph>
                Volatility indicator consisting of a middle band (SMA) and upper/lower bands based on standard deviation.
                Uses 20-day period with 2.0 standard deviation multiplier.
              </Typography>

              {calculatingBollinger && (
                <Box mb={2}>
                  <LinearProgress />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    Calculating Bollinger Bands...
                  </Typography>
                </Box>
              )}

              <Button
                variant="contained"
                startIcon={calculatingBollinger ? <CircularProgress size={20} /> : <CalculateIcon />}
                onClick={handleCalculateBollingerBands}
                disabled={calculatingBollinger || calculatingDMI}
                fullWidth
              >
                {calculatingBollinger ? 'Calculating...' : 'Calculate Bollinger Bands'}
              </Button>

              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Target: {selectedSymbols.length > 0 ? `${selectedSymbols.length} selected symbols` : 'All symbols'} |
                  Mode: {calculationMode.replace('_', ' ').toLowerCase()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* DMI */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TrendingUpIcon sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography variant="h6">
                  DMI (Directional Movement Index)
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" paragraph>
                Momentum indicator that measures the strength of price movement in positive and negative directions.
                Uses 14-day period and includes +DI, -DI, and ADX calculations.
              </Typography>

              {/* DMI Calculation Method Selection */}
              <Box mb={3}>
                <FormControl component="fieldset" fullWidth>
                  <FormLabel component="legend" sx={{ mb: 1, fontSize: '0.875rem' }}>
                    DMI Calculation Method
                  </FormLabel>
                  <RadioGroup
                    value={dmiCalculationMethod}
                    onChange={(e) => setDmiCalculationMethod(e.target.value as 'PURE_JAVA' | 'HYBRID_SQL_JAVA')}
                    row
                  >
                    <FormControlLabel
                      value="HYBRID_SQL_JAVA"
                      control={<Radio size="small" />}
                      label="Hybrid (SQL+Java)"
                      disabled={calculatingBollinger || calculatingDMI}
                    />
                    <FormControlLabel
                      value="PURE_JAVA"
                      control={<Radio size="small" />}
                      label="Pure Java"
                      disabled={calculatingBollinger || calculatingDMI}
                    />
                  </RadioGroup>
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                    {dmiCalculationMethod === 'HYBRID_SQL_JAVA'
                      ? 'SQL for bulk calculations, Java for ADX smoothing (recommended for performance)'
                      : 'Pure Java implementation for all calculations (better for debugging)'
                    }
                  </Typography>
                </FormControl>
              </Box>

              {calculatingDMI && (
                <Box mb={2}>
                  <LinearProgress color="secondary" />
                  <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mt: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      {dmiProgress.currentOperation || 'Calculating DMI indicators...'}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                      Elapsed: {formatElapsedTime(dmiProgress.elapsedSeconds)}
                    </Typography>
                  </Box>
                  {dmiProgress.elapsedSeconds > 30 && (
                    <Typography variant="caption" color="warning.main" sx={{ mt: 0.5, display: 'block' }}>
                      Large datasets may take several minutes to process. Please be patient.
                    </Typography>
                  )}
                </Box>
              )}

              <Button
                variant="contained"
                color="secondary"
                startIcon={calculatingDMI ? <CircularProgress size={20} /> : <CalculateIcon />}
                onClick={handleCalculateDMI}
                disabled={calculatingBollinger || calculatingDMI}
                fullWidth
              >
                {calculatingDMI ? 'Calculating...' : 'Calculate DMI'}
              </Button>

              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Target: {selectedSymbols.length > 0 ? `${selectedSymbols.length} selected symbols` : 'All symbols'} |
                  Mode: {calculationMode.replace('_', ' ').toLowerCase()} |
                  Method: {dmiCalculationMethod.replace('_', ' ').toLowerCase()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TechnicalIndicators;
