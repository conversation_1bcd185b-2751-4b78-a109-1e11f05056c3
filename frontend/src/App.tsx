import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box } from '@mui/material';

// Components
import Navigation from './components/Navigation';
import Dashboard from './pages/Dashboard';
import Instruments from './pages/Instruments';
import OHLCVData from './pages/OHLCVData';
import TechnicalIndicators from './pages/TechnicalIndicators';
import Positions from './pages/Positions';
import WatchList from './pages/WatchList';
import Processes from './pages/Processes';

// Create Material-UI theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderRadius: 8,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 6,
        },
      },
    },
  },
});

const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{ display: 'flex', minHeight: '100vh' }}>
          <Navigation />
          <Box
            component="main"
            sx={{
              flexGrow: 1,
              p: 3,
              backgroundColor: 'background.default',
              minHeight: '100vh',
            }}
          >
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/instruments" element={<Instruments />} />
              <Route path="/ohlcv" element={<OHLCVData />} />
              <Route path="/ohlcv/:symbol" element={<OHLCVData />} />
              <Route path="/technical-indicators" element={<TechnicalIndicators />} />
              <Route path="/positions" element={<Positions />} />
              <Route path="/watchlist" element={<WatchList />} />
              <Route path="/processes" element={<Processes />} />
            </Routes>
          </Box>
        </Box>
      </Router>
    </ThemeProvider>
  );
};

export default App;
