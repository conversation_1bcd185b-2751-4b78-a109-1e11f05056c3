import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse } from '../../types/api';

// Create axios instance with base configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: 'http://localhost:8080/investment-toolkit/api',
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Include cookies for CORS
});

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    
    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const message = error.response.data?.message || error.message;
      
      switch (status) {
        case 400:
          console.error('Bad Request:', message);
          break;
        case 401:
          console.error('Unauthorized:', message);
          break;
        case 403:
          console.error('Forbidden:', message);
          break;
        case 404:
          console.error('Not Found:', message);
          break;
        case 500:
          console.error('Internal Server Error:', message);
          break;
        default:
          console.error(`HTTP ${status}:`, message);
      }
    } else if (error.request) {
      // Network error
      console.error('Network Error: Unable to reach the server');
    } else {
      // Other error
      console.error('Error:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// Generic API call wrapper
export const apiCall = async <T>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  url: string,
  data?: any,
  config?: any
): Promise<ApiResponse<T>> => {
  try {
    const response = await apiClient.request<ApiResponse<T>>({
      method,
      url,
      data,
      ...config,
    });
    return response.data;
  } catch (error: any) {
    // Transform error into consistent format
    const errorResponse: ApiResponse<T> = {
      success: false,
      message: error.response?.data?.message || error.message || 'Unknown error occurred',
      data: null as any,
      timestamp: new Date().toISOString(),
    };
    throw errorResponse;
  }
};

// Convenience methods
export const get = <T>(url: string, config?: any) =>
  apiCall<T>('GET', url, undefined, config);

export const post = <T>(url: string, data?: any, config?: any) =>
  apiCall<T>('POST', url, data, config);

export const put = <T>(url: string, data?: any, config?: any) =>
  apiCall<T>('PUT', url, data, config);

export const del = <T>(url: string, config?: any) =>
  apiCall<T>('DELETE', url, undefined, config);

export const patch = <T>(url: string, data?: any, config?: any) =>
  apiCall<T>('PATCH', url, data, config);

// Long-running operation methods with extended timeout (3 minutes)
export const postLongRunning = <T>(url: string, data?: any, config?: any) =>
  apiCall<T>('POST', url, data, { ...config, timeout: 180000 });

export default apiClient;
