import { get, post, postLongRunning } from './apiClient';
import {
  TechnicalIndicatorRequest,
  ApiResponse
} from '../../types/api';

export class TechnicalIndicatorService {
  
  /**
   * Calculate Bollinger Bands for symbols
   */
  static async calculateBollingerBands(
    request: TechnicalIndicatorRequest
  ): Promise<ApiResponse<any>> {
    return post<any>('/technical-indicators/bollinger-bands/calculate', request);
  }

  /**
   * Calculate DMI indicators for symbols
   */
  static async calculateDMI(
    request: TechnicalIndicatorRequest
  ): Promise<ApiResponse<any>> {
    return post<any>('/technical-indicators/dmi/calculate', request);
  }

  /**
   * Calculate DMI indicators for symbols with extended timeout (3 minutes)
   * Use this for large datasets that may take longer than 30 seconds
   */
  static async calculateDMILongRunning(
    request: TechnicalIndicatorRequest
  ): Promise<ApiResponse<any>> {
    return postLongRunning<any>('/technical-indicators/dmi/calculate', request);
  }

  /**
   * Calculate DMI indicators asynchronously
   */
  static async calculateDMIAsync(
    request: TechnicalIndicatorRequest
  ): Promise<ApiResponse<any>> {
    return post<any>('/technical-indicators/dmi/calculate-async', request);
  }

  /**
   * Get technical indicator information
   */
  static async getTechnicalIndicatorInfo(): Promise<ApiResponse<any>> {
    return get<any>('/technical-indicators/info');
  }

  /**
   * Get Bollinger Bands data for a symbol
   */
  static async getBollingerBandsData(
    symbol: string,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<any>> {
    let url = `/technical-indicators/bollinger-bands/${symbol}`;
    const params = new URLSearchParams();
    
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    return get<any>(url);
  }

  /**
   * Get DMI data for a symbol
   */
  static async getDMIData(
    symbol: string,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<any>> {
    let url = `/technical-indicators/dmi/${symbol}`;
    const params = new URLSearchParams();
    
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    return get<any>(url);
  }
}
