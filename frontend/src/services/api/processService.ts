import { get, post } from './apiClient';
import { 
  ProcessInfo,
  ApiResponse 
} from '../../types/api';

export class ProcessService {
  
  /**
   * Get all processes with optional filtering
   */
  static async getAllProcesses(
    status?: string,
    type?: string
  ): Promise<ApiResponse<ProcessInfo[]>> {
    let url = '/processes';
    const params = new URLSearchParams();
    
    if (status) params.append('status', status);
    if (type) params.append('type', type);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    return get<ProcessInfo[]>(url);
  }

  /**
   * Get active processes only
   */
  static async getActiveProcesses(): Promise<ApiResponse<ProcessInfo[]>> {
    return get<ProcessInfo[]>('/processes/active');
  }

  /**
   * Get specific process by ID
   */
  static async getProcess(processId: string): Promise<ApiResponse<ProcessInfo>> {
    return get<ProcessInfo>(`/processes/${processId}`);
  }

  /**
   * Abort specific process
   */
  static async abortProcess(processId: string): Promise<ApiResponse<ProcessInfo>> {
    return post<ProcessInfo>(`/processes/${processId}/abort`);
  }

  /**
   * Abort all active processes
   */
  static async abortAllProcesses(): Promise<ApiResponse<any>> {
    return post<any>('/processes/abort-all');
  }

  /**
   * Get process statistics
   */
  static async getProcessStatistics(): Promise<ApiResponse<any>> {
    return get<any>('/processes/statistics');
  }

  /**
   * Clean up completed processes
   */
  static async cleanupProcesses(): Promise<ApiResponse<any>> {
    return post<any>('/processes/cleanup');
  }
}
