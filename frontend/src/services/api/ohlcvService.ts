import { get, post } from './apiClient';
import {
  OHLCV,
  RefreshAllRequest,
  ApiResponse,
  BulkUpdateRequest,
  BulkUpdateProgress,
  BulkUpdateResult
} from '../../types/api';

export class OHLCVService {
  
  /**
   * Get OHLCV data for a specific symbol
   */
  static async getOHLCVData(
    symbol: string,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<OHLCV[]>> {
    let url = `/ohlcv/${symbol}`;
    const params = new URLSearchParams();
    
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    return get<OHLCV[]>(url);
  }

  /**
   * Get latest OHLCV data for multiple symbols
   */
  static async getLatestOHLCVData(symbols: string[]): Promise<ApiResponse<OHLCV[]>> {
    return post<OHLCV[]>('/ohlcv/latest', { symbols });
  }

  /**
   * Update OHLCV data for a specific symbol
   */
  static async updateOHLCVData(
    symbol: string,
    startDate?: string,
    endDate?: string,
    dryRun: boolean = true
  ): Promise<ApiResponse<any>> {
    // Backend endpoint only takes symbol as path parameter, no request body needed
    return post<any>(`/ohlcv/update/${symbol}`);
  }

  /**
   * Refresh OHLCV data for all instruments
   */
  static async refreshAllOHLCVData(request: RefreshAllRequest): Promise<ApiResponse<any>> {
    return post<any>('/ohlcv/refresh-all', request);
  }

  /**
   * Get OHLCV statistics
   */
  static async getOHLCVStatistics(): Promise<ApiResponse<any>> {
    return get<any>('/ohlcv/statistics');
  }

  /**
   * Get symbols with OHLCV data
   */
  static async getSymbolsWithData(
    page: number = 0,
    size: number = 50
  ): Promise<ApiResponse<string[]>> {
    return get<string[]>(`/ohlcv/symbols?page=${page}&size=${size}`);
  }

  /**
   * Update OHLCV data for multiple symbols in bulk
   */
  static async bulkUpdateOHLCVData(
    request: BulkUpdateRequest,
    onProgress?: (progress: BulkUpdateProgress[]) => void,
    abortSignal?: AbortSignal
  ): Promise<BulkUpdateResult> {
    const results: BulkUpdateProgress[] = request.symbols.map(symbol => ({
      symbol,
      status: 'pending' as const,
    }));

    let successCount = 0;
    let errorCount = 0;

    // Process symbols sequentially to avoid overwhelming the server
    for (let i = 0; i < request.symbols.length; i++) {
      if (abortSignal?.aborted) {
        // Mark remaining symbols as cancelled
        for (let j = i; j < request.symbols.length; j++) {
          results[j].status = 'error';
          results[j].error = 'Operation cancelled by user';
        }
        break;
      }

      const symbol = request.symbols[i];
      results[i].status = 'processing';

      // Call progress callback if provided
      if (onProgress) {
        onProgress([...results]);
      }

      try {
        const response = await this.updateOHLCVData(
          symbol,
          request.startDate,
          request.endDate,
          request.dryRun || false
        );

        if (response.success) {
          results[i].status = 'success';
          results[i].message = response.message || 'Updated successfully';
          successCount++;
        } else {
          results[i].status = 'error';
          results[i].error = response.message || 'Update failed';
          errorCount++;
        }
      } catch (error: any) {
        results[i].status = 'error';
        results[i].error = error.message || 'Network error';
        errorCount++;
      }

      // Call progress callback after each symbol
      if (onProgress) {
        onProgress([...results]);
      }

      // Small delay to prevent overwhelming the server
      if (i < request.symbols.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return {
      totalSymbols: request.symbols.length,
      successCount,
      errorCount,
      results,
      summary: `Processed ${request.symbols.length} symbols: ${successCount} successful, ${errorCount} failed`
    };
  }

  /**
   * Retry failed symbols from a previous bulk operation
   */
  static async retryFailedSymbols(
    previousResults: BulkUpdateProgress[],
    request: Omit<BulkUpdateRequest, 'symbols'>,
    onProgress?: (progress: BulkUpdateProgress[]) => void,
    abortSignal?: AbortSignal
  ): Promise<BulkUpdateResult> {
    const failedSymbols = previousResults
      .filter(result => result.status === 'error')
      .map(result => result.symbol);

    if (failedSymbols.length === 0) {
      return {
        totalSymbols: 0,
        successCount: 0,
        errorCount: 0,
        results: [],
        summary: 'No failed symbols to retry'
      };
    }

    return this.bulkUpdateOHLCVData(
      { ...request, symbols: failedSymbols },
      onProgress,
      abortSignal
    );
  }
}
